import gql from 'graphql-tag'

export const GET_INVENTORY_BY_IDS = gql`
    query ReadInventories($filter: InventoryFilterFields = {}) {
        readInventories(filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                item
                title
            }
        }
    }
`
