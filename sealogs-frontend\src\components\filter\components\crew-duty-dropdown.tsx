'use client'

import { CREW_DUTY } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import { useEffect, useState } from 'react'
import { CrewDuty } from '../../../../types/crew-duty'
import CrewDutyModel from '@/app/offline/models/crewDuty'
import CrewDutyForm from '../../../app/ui/crew-duty/form'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { Combobox } from '@/components/ui/comboBox'
interface CrewDutyDropdownProps {
    crewDutyID?: number
    onChange: (value: any) => void
    offline?: boolean
    placeholder?: string
    label?: string
    multi?: boolean
    hideCreateOption?: boolean
    modal?: boolean
}
const CrewDutyDropdown = ({
    crewDutyID = 0,
    onChange,
    label,
    offline = false,
    placeholder = 'Duty',
    multi = false,
    modal,
    hideCreateOption = false,
}: CrewDutyDropdownProps) => {
    const dutyModel = new CrewDutyModel()
    const [isLoading, setIsLoading] = useState(true)
    const [duties, setDuties] = useState([] as any)
    const [duty, setDuty] = useState([] as any)
    const [openDialog, setOpenDialog] = useState(false)
    const [queryDuties] = useLazyQuery(CREW_DUTY, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readCrewDuties.nodes
            if (data) {
                const activeDuties = data.filter((duty: any) => !duty.archived)
                let finalDuties = activeDuties

                if (!hideCreateOption) {
                    const dutyObject: CrewDuty = {
                        id: 0,
                        title: '-- Create New Duty --',
                        archived: false,
                        abbreviation: 'NEW',
                    }
                    finalDuties = [dutyObject, ...activeDuties]
                }

                setDuties(finalDuties)
                if (crewDutyID > 0) {
                    const selectedDuty = activeDuties.find(
                        (d: any) => d.id === crewDutyID,
                    )
                    setDuty({
                        label: selectedDuty.title,
                        value: selectedDuty.id,
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('queryDutiesEntry error', error)
        },
    })
    const loadCrewDuties = async () => {
        if (offline) {
            const data = await dutyModel.getAll()
            if (data) {
                const activeDuties = data.filter((duty: any) => !duty.archived)
                let finalDuties = activeDuties

                if (!hideCreateOption) {
                    const dutyObject: CrewDuty = {
                        id: 0,
                        title: '-- Create New Duty --',
                        archived: false,
                        abbreviation: 'NEW',
                    }
                    finalDuties = [dutyObject, ...activeDuties]
                }

                setDuties(finalDuties)
                if (crewDutyID > 0) {
                    const selectedDuty = activeDuties.find(
                        (d: any) => d.id === crewDutyID,
                    )
                    setDuty({
                        label: selectedDuty.title,
                        value: selectedDuty.id,
                    })
                }
            }
        } else {
            await queryDuties()
        }
    }
    const handleOnChange = (selectedOptions: any) => {
        // Check if the "Create New Duty" option was selected
        if (selectedOptions && selectedOptions.value === 0) {
            setOpenDialog(true)
            return
        }

        if (multi) {
            setDuty(selectedOptions) // Store an array of selected values
            onChange(selectedOptions) // Pass the selected array
        } else {
            setDuty(selectedOptions)
            onChange(selectedOptions)
        }
    }
    const handleCreateCrewDuty = (crewDuty: any) => {
        const newDuty = { label: crewDuty.title, value: crewDuty.id }
        setDuty(newDuty)
        onChange(newDuty)
        loadCrewDuties()
        setOpenDialog(false)
    }
    useEffect(() => {
        if (isLoading) {
            loadCrewDuties()
            setIsLoading(false)
        }
    }, [isLoading])

    useEffect(() => {
        if (crewDutyID > 0 && duties.length > 0) {
            const selectedDuty = duties.find((d: any) => d.id == crewDutyID)
            if (selectedDuty) {
                setDuty({ label: selectedDuty.title, value: selectedDuty.id })
            }
        } else if (crewDutyID === 0) {
            setDuty(null)
        }
    }, [crewDutyID, duties])

    return (
        <>
            <Combobox
                modal={modal}
                options={duties.map((d: any) => ({
                    label: `${d.title}`,
                    value: d.id,
                }))}
                label={label}
                multi={multi}
                value={duty}
                onChange={handleOnChange}
                placeholder={placeholder}
            />

            <AlertDialogNew
                openDialog={openDialog}
                setOpenDialog={setOpenDialog}
                size="md"
                noButton
                noFooter
                className="space-y-0">
                <CrewDutyForm
                    crewDutyId={0}
                    onCancel={() => setOpenDialog(false)}
                    onCreate={handleCreateCrewDuty}
                    isPopup={true}
                />
            </AlertDialogNew>
        </>
    )
}

export default CrewDutyDropdown
