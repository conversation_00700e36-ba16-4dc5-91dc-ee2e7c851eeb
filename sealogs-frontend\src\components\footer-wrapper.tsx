'use client'

import { cn } from '@/app/lib/utils'
import type * as React from 'react'

interface FooterWrapperProps extends React.HTMLAttributes<HTMLDivElement> {
    noBorder?: boolean
    bottom?: string
    parentClassName?: string
    children: React.ReactNode
}

export function FooterWrapper({
    noBorder = false,
    bottom,
    parentClassName,
    children,
    className,
    ...props
}: FooterWrapperProps) {
    return (
        <>
            <div
                className={cn(
                    'sticky w-full z-50 bottom-4 md:bottom-2 inset-x-2',
                    parentClassName,
                )}
                {...props}>
                <div
                    className={cn(
                        'flex w-full justify-end gap-2.5 md:gap-3 p-2.5 border bg-fire-bush-100 border-fire-bush-700 text-fire-bush-700 rounded-md shadow-md py-3 md:py-4',
                        'md:px-6 lg:px-8',
                        className,
                    )}>
                    {children}
                </div>
            </div>
        </>
    )
}
