import gql from 'graphql-tag'

export const CREW_LIST_WITHOUT_TRAINING_STATUS = gql`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: <PERSON><PERSON> }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                archived
                isArchived
                firstName
                isCrew
                departments {
                    nodes {
                        id
                    }
                }
                primaryDutyID
                primaryDuty {
                    id
                    title
                }
                surname
                vehicles {
                    nodes {
                        id
                        title
                    }
                }
            }
        }
    }
`
