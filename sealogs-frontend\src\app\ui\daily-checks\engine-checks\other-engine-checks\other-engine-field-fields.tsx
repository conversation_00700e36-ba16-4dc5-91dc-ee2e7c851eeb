'use client'

import React, { Di<PERSON>atch, useMemo } from 'react'
import { useEngineFields } from '../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import {
    DailyCheckField,
    CheckField,
    CheckFieldTopContent,
    CheckFieldContent,
} from '@/components/daily-check-field'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'

interface IProps {
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: (content: string | React.ReactNode) => void
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
}

export default function OtherEngineFieldFields({
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
}: IProps) {
    const { otherEngineFieldFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(
                otherEngineFieldFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [logBookConfig, otherEngineFieldFields])

    if (filteredFields.length === 0) {
        return <></>
    }

    return (
        <>
            {filteredFields.filter((groupField: any) =>
                groupField?.items?.some((field: any) =>
                    displayField(field.name, logBookConfig),
                ),
            ).length > 0 && <CheckFieldTopContent />}
            {logBookConfig &&
                vesselDailyCheck &&
                filteredFields.map((groupField: any) => (
                    <div key={groupField.name}>
                        {groupField?.items
                            ?.filter((field: any) =>
                                displayField(field.name, logBookConfig),
                            )
                            ?.map((field: any, index: number) => (
                                <DailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    key={index}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleEngineChecks(false, field.value)
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleEngineChecks(true, field.value)
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment(field.name),
                                            composeField(
                                                field.name,
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                />
                            ))}
                        {displayDescription(groupField.name, logBookConfig) && (
                            <Button
                                variant="ghost"
                                size="icon"
                                onClick={() => {
                                    setDescriptionPanelContent(
                                        displayDescription(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    setOpenDescriptionPanel(true)
                                    setDescriptionPanelHeading(groupField.name)
                                }}>
                                <AlertCircle className="h-5 w-5" />
                            </Button>
                        )}
                    </div>
                ))}
        </>
    )
}
