'use client'

import type React from 'react'

import { useEffect, useState } from 'react'

/**
 * Hook that detects if content fits within its container
 * @param contentRef - Reference to the content element
 * @param containerRef - Reference to the container element
 * @param extraSpace - Additional space to reserve (in pixels)
 * @returns Whether the content fits within the container
 */
export function useContainerQuery<T extends HTMLElement, C extends HTMLElement>(
    contentRef: React.RefObject<T>,
    containerRef: React.RefObject<C>,
    extraSpace = 0,
): boolean {
    const [fits, setFits] = useState(true)

    useEffect(() => {
        if (!contentRef.current || !containerRef.current) return

        const checkFit = () => {
            const contentWidth = contentRef.current?.scrollWidth || 0
            const containerWidth = containerRef.current?.clientWidth || 0

            // Check if content fits within container with extra space
            setFits(contentWidth + extraSpace <= containerWidth)
        }

        // Initial check
        checkFit()

        // Set up ResizeObserver to monitor container size changes
        const resizeObserver = new ResizeObserver(() => {
            checkFit()
        })

        resizeObserver.observe(containerRef.current)

        // Clean up
        return () => {
            resizeObserver.disconnect()
        }
    }, [contentRef, containerRef, extraSpace])

    return fits
}
