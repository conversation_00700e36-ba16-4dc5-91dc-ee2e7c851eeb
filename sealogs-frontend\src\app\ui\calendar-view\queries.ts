import gql from 'graphql-tag'

export const ReadLogBookEntries = gql`
    query {
        readLogBookEntries {
            nodes {
                id
                startDate
                archived
                masterID
            }
        }
    }
`

export const ReadCrewMembers_LogBookEntrySections = gql`
    query ReadCrewMembers_LogBookEntrySections(
        $filter: CrewMembers_LogBookEntrySectionFilterFields = {}
    ) {
        readCrewMembers_LogBookEntrySections(filter: $filter) {
            nodes {
                id
                crewMember {
                    id
                    firstName
                    surname
                }
            }
        }
    }
`

export const ReadTrainingSessionDues = gql`
    query ReadTrainingSessionDues(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionDueFilterFields = {}
    ) {
        readTrainingSessionDues(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: {
                dueDate: ASC
                trainingTypeID: ASC
                vesselID: ASC
                memberID: ASC
            }
        ) {
            nodes {
                id
                dueDate
                memberID
                member {
                    id
                    firstName
                    surname
                }
                vesselID
                vessel {
                    id
                    title
                    seaLogsMembers {
                        nodes {
                            id
                            firstName
                            surname
                        }
                    }
                }
                trainingTypeID
                trainingType {
                    id
                    title
                }
            }
        }
    }
`

export const ReadComponentMaintenanceCheckList = gql`
    query ReadComponentMaintenanceCheckList(
        $inventoryID: Int!
        $vesselID: Int!
    ) {
        readComponentMaintenanceCheckList(
            inventoryID: $inventoryID
            vesselID: $vesselID
        ) {
            list
        }
    }
`

export const ReadInventories = gql`
    query ReadInventories(
        $limit: Int = 100
        $offset: Int = 0
        $filter: InventoryFilterFields = {}
    ) {
        readInventories(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                item
            }
        }
    }
`

export const ReadSeaLogsMembers = gql`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                firstName
                surname
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        vesselID
                    }
                }
            }
        }
    }
`

export const ReadOneLogBookEntry = gql`
    query ReadOneLogBookEntry($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            master {
                id
                firstName
                surname
            }
            vehicle {
                id
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
            }
        }
    }
`

export const ReadTrainingSessions = gql`
    query ReadTrainingSessions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionFilterFields = {}
    ) {
        readTrainingSessions(limit: $limit, offset: $offset, filter: $filter) {
            nodes {
                id
                date
                trainingSummary
                vessel {
                    id
                    title
                }
            }
        }
    }
`

export const ReadVessels = gql`
    query ReadVessels(
        $limit: Int
        $offset: Int
        $filter: VesselFilterFields = {}
    ) {
        readVessels(filter: $filter, limit: $limit, offset: $offset) {
            nodes {
                id
                archived
                title
            }
        }
    }
`
