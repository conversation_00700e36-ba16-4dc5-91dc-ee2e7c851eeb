'use client'

import React from 'react'
import { useMediaQuery } from '@reactuses/core'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { TabItem } from './custom-tab'
import { cn } from '@/app/lib/utils'
import { useSidebar } from '@/components/ui/sidebar'
import { useQueryState } from 'nuqs'
import { Separator } from './separator'
import { H3, H4 } from './typography'

interface ResponsiveTabProps {
    tabs: TabItem[]
    className?: string
    tabsListClassName?: string
    tabsTriggerClassName?: string
    tabsContentClassName?: string
    defaultTab?: string
    queryParam: string
    isSecHeading?: boolean
    onTabChange?: (value: string) => void // Optional callback for notification only
    disableResponsive?: boolean // Explicitly disable responsive behavior
}

export const ResponsiveTab: React.FC<ResponsiveTabProps> = ({
    tabs,
    className = '',
    tabsListClassName = '',
    tabsTriggerClassName = '',
    tabsContentClassName = '',
    defaultTab,
    queryParam,
    isSecHeading = false,
    onTabChange,
    disableResponsive = false,
}) => {
    // Use useMediaQuery hook to detect screen size
    const isLgScreen = useMediaQuery('(min-width: 1280px)') // lg breakpoint
    const isMdScreen = useMediaQuery('(min-width: 1024px)') // md breakpoint
    const isSmScreen = useMediaQuery('(min-width: 768px)') // sm breakpoint

    // Determine default tab value
    const initialTab = defaultTab || (tabs.length > 0 ? tabs[0].value : '')

    // Use nuqs to manage the tab state through URL query parameters
    const [activeTab, setActiveTab] = useQueryState(queryParam, {
        defaultValue: initialTab,
    })

    // Handle tab change
    const handleTabChange = (value: string) => {
        setActiveTab(value)
        // Notify parent component if callback is provided
        if (onTabChange) {
            onTabChange(value)
        }
    }

    // Try to get the sidebar state, but fall back to screen size if not available
    let sidebarState: 'expanded' | 'collapsed' | undefined
    try {
        const { state } = useSidebar()
        sidebarState = state
    } catch (error) {
        // If useSidebar throws an error (not within a SidebarProvider),
        // we'll fall back to using just the screen size
        console.warn('ResponsiveTab used outside of SidebarProvider context')
        sidebarState = undefined
    }

    // isDesktop logic:
    // - Always true if disableResponsive is true
    // - Always true if there are fewer than 6 tabs (disable responsive behavior) and not on small screen
    // - On small screens: always false
    // - On lg screens and above: always true
    // - On md screens: depends on sidebar collapsed state
    // - If sidebar state is not available, fall back to screen size detection
    const isDesktop = (() => {
        // If responsive behavior is explicitly disabled, always use desktop view
        if (disableResponsive) {
            return true
        }

        // First check if we're on a small screen
        if (!isSmScreen) {
            // Always false on small screens
            return false
        }

        // If we have fewer than 6 tabs, always use desktop view (unless on small screen)
        if (tabs.length < 1) {
            return true
        }

        // Otherwise, use the standard responsive logic
        if (isLgScreen) {
            return true // Always true on lg screens
        } else if (isMdScreen) {
            // On md screens, depends on sidebar state
            return sidebarState !== undefined
                ? sidebarState === 'collapsed'
                : true
        } else {
            return false
        }
    })()

    // Filter out tabs without components
    const tabsWithComponents = tabs.filter((tab) => tab.component)

    // Render tabs for desktop view
    if (isDesktop) {
        return (
            <Tabs
                value={activeTab}
                className={cn(className)}
                onValueChange={handleTabChange}>
                <TabsList className={tabsListClassName}>
                    {tabs.map((tab) => (
                        <TabsTrigger
                            key={tab.id}
                            value={tab.value}
                            className={tabsTriggerClassName}>
                            {tab.icon && (
                                <span className="mr-2">{tab.icon}</span>
                            )}
                            {tab.label}
                        </TabsTrigger>
                    ))}
                </TabsList>

                {/* Render tab content */}
                {tabsWithComponents.map((tab) => (
                    <TabsContent
                        key={tab.id}
                        value={tab.value}
                        className={tabsContentClassName}>
                        {tab.component}
                    </TabsContent>
                ))}
            </Tabs>
        )
    }

    // Render simple list for mobile view
    return (
        <div className={`w-full space-y-8 ${className}`}>
            {tabs.map((tab) => (
                <div key={tab.id} className="space-y-2">
                    {!isSecHeading ? (
                        <H3>{tab.label}</H3>
                    ) : (
                        <H4>{tab.label}</H4>
                    )}
                    {tab.component && <div>{tab.component}</div>}
                </div>
            ))}
        </div>
    )
}

export default ResponsiveTab
