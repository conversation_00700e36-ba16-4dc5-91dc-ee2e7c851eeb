import { useMediaQuery } from '@mantine/hooks'

type BreakpointKey =
    | 'tiny'
    | 'small'
    | 'standard'
    | 'phablet'
    | 'tablet-sm'
    | 'tablet-md'
    | 'tablet-lg'
    | 'landscape'
    | 'laptop'
    | 'desktop'

interface BreakpointsConfig {
    tiny: string
    small: string
    standard: string
    phablet: string
    'tablet-sm': string
    'tablet-md': string
    'tablet-lg': string
    landscape: string
    laptop: string
    desktop: string
}

const defaultBreakpoints: BreakpointsConfig = {
    tiny: '320px', // Tiny phones (iPhone SE, ultra-small screens)
    small: '375px', // Small phones (iPhone 12/13 Mini)
    standard: '430px', // Standard phones (iPhone 14 Pro Max, Galaxy S22)
    phablet: '480px', // Larger mobile devices in portrait
    'tablet-sm': '600px', // Small tablets
    'tablet-md': '768px', // Medium tablets (iPad Mini / standard portrait iPads)
    'tablet-lg': '834px', // Large tablets (iPad Pro 11-inch in portrait)
    landscape: '1024px', // iPad in landscape, Grid and dense views start to work
    laptop: '1280px', // Small laptops
    desktop: '1536px', // Desktop screens
}

/**
 * Custom hook that returns boolean flags for each breakpoint in the config.
 * You can optionally override the default breakpoints by passing a partial config.
 */
export function useBreakpoints(
    overrides?: Partial<BreakpointsConfig>,
): Record<BreakpointKey, boolean> {
    const config = { ...defaultBreakpoints, ...overrides } as BreakpointsConfig

    const matches: Partial<Record<BreakpointKey, boolean>> = {}

    ;(Object.keys(config) as BreakpointKey[]).forEach((key) => {
        const minWidth = config[key]
        // useMediaQuery returns true if viewport >= minWidth
        matches[key] = useMediaQuery(`(min-width: ${minWidth})`)
    })

    return matches as Record<BreakpointKey, boolean>
}

/**
 * Usage example:
 *
 * function App() {
 *   const bp = useBreakpoints();
 *
 *   return (
 *     <div>
 *       {bp.tiny && <p>Viewport is at least 320px wide.</p>}
 *       {bp.tabletMd && <p>Viewport is at least 768px wide.</p>}
 *       {!bp.desktop && <p>Viewport is smaller than 1536px.</p>}
 *     </div>
 *   );
 * }
 */
