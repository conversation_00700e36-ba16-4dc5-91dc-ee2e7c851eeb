'use client'

import Link from 'next/link'
import { useState } from 'react'
import Image from 'next/image'
import Lightbox from 'yet-another-react-lightbox'
import Captions from 'yet-another-react-lightbox/plugins/captions'
import Zoom from 'yet-another-react-lightbox/plugins/zoom'
import Download from 'yet-another-react-lightbox/plugins/download'
import Fullscreen from 'yet-another-react-lightbox/plugins/fullscreen'
import 'yet-another-react-lightbox/styles.css'
import 'yet-another-react-lightbox/plugins/captions.css'
import { Button } from '@/components/ui/button'
import { FileImage, ExternalLink, X } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'

interface Document {
    id: string
    title: string
    fileFilename: string
    created: string
}

interface FileItemProps {
    document: Document
    hideTitle?: boolean
    showDeleteButton?: boolean
    onDelete?: (documentId: string) => void
    canDelete?: boolean
    deleteErrorMessage?: string
}

const FileItem = ({
    document,
    hideTitle = false,
    showDeleteButton = false,
    onDelete,
    canDelete = true,
    deleteErrorMessage = 'You do not have permission to delete this document',
}: FileItemProps) => {
    const [openLightbox, setOpenLightbox] = useState(false)
    const [imageError, setImageError] = useState(false)
    const { toast } = useToast()

    const handleDelete = () => {
        if (!canDelete) {
            toast({ description: deleteErrorMessage, variant: 'destructive' })
            return
        }
        if (onDelete) {
            onDelete(document.id)
        }
    }

    const isImage = (): boolean => {
        const fileExtension = document.fileFilename
            ?.split('.')
            .pop()
            ?.toLowerCase()
        const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg']
        return imageExtensions.includes(fileExtension || '')
    }

    const getFileIcon = (): string => {
        const fileExtension = document.fileFilename
            ?.split('.')
            .pop()
            ?.toLowerCase()

        const iconMap: Record<string, string> = {
            pdf: '/file-types/pdf.svg',
            xls: '/file-types/xls.svg',
            xlsx: '/file-types/xls.svg',
            ppt: '/file-types/ppt.svg',
            pptx: '/file-types/ppt.svg',
            txt: '/file-types/txt.svg',
            csv: '/file-types/csv.svg',
        }

        return iconMap[fileExtension || ''] || '/file-types/doc.svg'
    }

    const getFileUrl = (): string => {
        const baseUrl =
            process.env.NEXT_PUBLIC_FILE_BASE_URL ||
            process.env.FILE_BASE_URL ||
            ''
        return `${baseUrl}${document.fileFilename}`
    }

    if (isImage() && !imageError) {
        return (
            <div className="group relative flex items-center gap-3">
                <Button
                    variant="ghost"
                    onClick={() => setOpenLightbox(true)}
                    className="h-auto p-2 hover:bg-muted/50 transition-colors flex-1"
                    aria-label={`View image: ${document.title}`}>
                    <div className="flex items-center gap-3">
                        <div className="relative overflow-hidden rounded-lg border border-border bg-muted/20">
                            <Image
                                src={getFileUrl() || '/placeholder.svg'}
                                alt={document.title}
                                width={64}
                                height={64}
                                className="h-16 w-16 object-cover transition-transform group-hover:scale-105"
                                onError={() => setImageError(true)}
                            />
                            <div className="absolute inset-0 bg-black/0 group-hover:bg-black/10 transition-colors flex items-center justify-center">
                                <FileImage className="h-4 w-4 text-white opacity-0 group-hover:opacity-100 transition-opacity" />
                            </div>
                        </div>
                        {!hideTitle && (
                            <div className="flex-1 text-left">
                                <p className="font-medium text-sm truncate max-w-[200px]">
                                    {document.title}
                                </p>
                                <p className="text-xs text-muted-foreground">
                                    Image •{' '}
                                    {document.fileFilename
                                        ?.split('.')
                                        .pop()
                                        ?.toUpperCase()}
                                </p>
                            </div>
                        )}
                    </div>
                </Button>

                {showDeleteButton && (
                    <Button
                        variant="destructive"
                        iconLeft={X}
                        iconOnly
                        onClick={handleDelete}
                        aria-label={`Delete ${document.title}`}
                    />
                )}

                <Lightbox
                    open={openLightbox}
                    close={() => setOpenLightbox(false)}
                    slides={[
                        {
                            src: getFileUrl(),
                            alt: document.title,
                            description: document.title,
                        },
                    ]}
                    render={{
                        buttonPrev: () => null,
                        buttonNext: () => null,
                    }}
                    controller={{
                        closeOnPullUp: true,
                        closeOnPullDown: true,
                        closeOnBackdropClick: true,
                    }}
                    plugins={[Captions, Fullscreen, Download, Zoom]}
                />
            </div>
        )
    }

    // Non-image files or image with error
    return (
        <div className="flex items-center gap-3">
            <Link
                href={getFileUrl()}
                target="_blank"
                rel="noopener noreferrer"
                className="group block flex-1">
                <div className="flex items-center gap-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
                    <div className="relative">
                        <div className="h-16 w-16 rounded-lg border border-border border-dashed bg-muted/20 flex items-center justify-center group-hover:bg-muted/30 transition-colors">
                            {imageError ? (
                                <FileImage className="h-6 w-6 text-muted-foreground" />
                            ) : (
                                <Image
                                    src={getFileIcon() || '/placeholder.svg'}
                                    alt={`${document.fileFilename?.split('.').pop()?.toUpperCase()} file`}
                                    width={24}
                                    height={24}
                                    className="h-6 w-6"
                                />
                            )}
                        </div>
                        <div className="absolute -top-1 -right-1 bg-background border border-border rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity">
                            <ExternalLink className="h-3 w-3 text-muted-foreground" />
                        </div>
                    </div>
                    {!hideTitle && (
                        <div className="flex-1 min-w-0">
                            <p className="font-medium text-sm truncate">
                                {document.title}
                            </p>
                            <p className="text-xs text-muted-foreground">
                                Document •{' '}
                                {document.fileFilename
                                    ?.split('.')
                                    .pop()
                                    ?.toUpperCase()}
                            </p>
                        </div>
                    )}
                </div>
            </Link>

            {showDeleteButton && (
                <Button
                    variant="destructive"
                    iconLeft={X}
                    iconOnly
                    onClick={handleDelete}
                    aria-label={`Delete ${document.title}`}
                />
            )}
        </div>
    )
}

export default FileItem
