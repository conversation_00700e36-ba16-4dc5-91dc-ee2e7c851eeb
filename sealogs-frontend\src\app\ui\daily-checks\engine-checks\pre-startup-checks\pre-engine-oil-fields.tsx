'use client'

import { Dispatch, useMemo } from 'react'
import { useEngineFields } from '../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import { AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'

interface IProps {
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    getComment: (fieldName: string, commentType?: string) => any
    edit_logBookEntry: boolean
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
}

export default function PreEngineOilFields({
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
}: IProps) {
    const { preEngineOilFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(preEngineOilFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [logBookConfig, preEngineOilFields])

    return (
        <>
            {filteredFields.filter((groupField: any) =>
                groupField?.items?.some((field: any) =>
                    displayField(field.name, logBookConfig),
                ),
            ).length > 0 && <CheckFieldTopContent />}
            {filteredFields.map((groupField: any) => (
                <div key={groupField.name}>
                    {/* <div className="mt-6   uppercase text-left">
                                                    {getFieldLabel(
                                                        groupField.name,
                                                        logBookConfig,
                                                    )}
                                                </div> */}
                    {groupField?.items
                        ?.filter((field: any) =>
                            displayField(field.name, logBookConfig),
                        )
                        ?.map((field: any, index: number) => (
                            <DailyCheckField
                                locked={locked || !edit_logBookEntry}
                                key={index}
                                displayField={displayField(
                                    field.name,
                                    logBookConfig,
                                )}
                                displayDescription={displayDescription(
                                    field.name,
                                    logBookConfig,
                                )}
                                setOpenDescriptionPanel={
                                    setOpenDescriptionPanel
                                }
                                setDescriptionPanelHeading={
                                    setDescriptionPanelHeading
                                }
                                displayLabel={getFieldLabel(
                                    field.name,
                                    logBookConfig,
                                )}
                                inputId={field.value}
                                handleNoChange={() =>
                                    // field.handleChange(
                                    //     false,
                                    // )
                                    handleEngineChecks(false, field.value)
                                }
                                defaultNoChecked={field.checked === 'Not_Ok'}
                                handleYesChange={() =>
                                    // field.handleChange(
                                    //     true,
                                    // )
                                    handleEngineChecks(true, field.value)
                                }
                                defaultYesChecked={field.checked === 'Ok'}
                                commentAction={() =>
                                    showCommentPopup(
                                        getComment(field.name),
                                        composeField(field.name, logBookConfig),
                                    )
                                }
                                comment={getComment(field.name)?.comment}
                            />
                        ))}
                    {displayDescription(groupField.name, logBookConfig) && (
                        <Button
                            variant="text"
                            iconLeft={AlertCircle}
                            onClick={() => {
                                setDescriptionPanelContent(
                                    displayDescription(
                                        groupField.name,
                                        logBookConfig,
                                    ),
                                )
                                setOpenDescriptionPanel(true)
                                setDescriptionPanelHeading(groupField.name)
                            }}
                        />
                    )}
                    <>
                        {/* <div className="flex flex-row">
                                                    <DailyCheckGroupField
                                                        locked={
                                                            locked ||
                                                            !edit_logBookEntry
                                                        }
                                                        groupField={groupField?.items?.filter(
                                                            (field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                        )}
                                                        handleYesChange={() =>
                                                            handleGroupYesChange(
                                                                groupField?.items?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        handleNoChange={() =>
                                                            handleGroupNoChange(
                                                                groupField?.items?.filter(
                                                                    (field: any) =>
                                                                        displayField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                ),
                                                                groupField,
                                                            )
                                                        }
                                                        defaultNoChecked={groupField?.items
                                                            ?.filter((field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Not_Ok',
                                                            )}
                                                        defaultYesChecked={groupField?.items
                                                            ?.filter((field: any) =>
                                                                displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                            ?.every(
                                                                (field: any) =>
                                                                    field.checked ===
                                                                    'Ok',
                                                            )}
                                                        commentAction={() =>
                                                            showCommentPopup(
                                                                getComment(
                                                                    groupField.name,
                                                                ),
                                                                composeField(
                                                                    groupField.name,
                                                                    logBookConfig,
                                                                ),
                                                            )
                                                        }
                                                        comment={
                                                            getComment(
                                                                groupField.name,
                                                            )?.comment
                                                        }
                                                    />
                                                    {groupField?.items?.map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <DailyCheckField
                                                                locked={
                                                                    locked ||
                                                                    !edit_logBookEntry
                                                                }
                                                                className={`lg:!grid-cols-2 hidden`}
                                                                innerWrapperClassName={`lg:!col-span-1`}
                                                                key={index}
                                                                displayField={displayField(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayDescription={displayDescription(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                displayLabel={getFieldLabel(
                                                                    field.name,
                                                                    logBookConfig,
                                                                )}
                                                                inputId={
                                                                    field.value
                                                                }
                                                                handleNoChange={() =>
                                                                    handleEngineChecks(
                                                                        false,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultNoChecked={
                                                                    field.checked ===
                                                                    'Not_Ok'
                                                                }
                                                                handleYesChange={() =>
                                                                    handleEngineChecks(
                                                                        true,
                                                                        field.value,
                                                                    )
                                                                }
                                                                defaultYesChecked={
                                                                    field.checked ===
                                                                    'Ok'
                                                                }
                                                                commentAction={() =>
                                                                    showCommentPopup(
                                                                        getComment(
                                                                            field.name,
                                                                        ),
                                                                        composeField(
                                                                            field.name,
                                                                            logBookConfig,
                                                                        ),
                                                                    )
                                                                }
                                                                comment={
                                                                    getComment(
                                                                        field.name,
                                                                    )?.comment
                                                                }
                                                            />
                                                        ),
                                                    )}
                                                </div> */}
                    </>
                </div>
            ))}
        </>
    )
}
