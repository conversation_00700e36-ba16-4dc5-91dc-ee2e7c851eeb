'use client'
import React, { useEffect, useState } from 'react'
import { isOverDueTask } from '@/app/lib/actions'
import { useLazyQuery } from '@apollo/client'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { PieChartComponent } from '@/components/pie-chart'
import { ChartConfig } from '@/components/ui/chart'
import { CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { ReadComponentMaintenanceChecks } from './queries'
import { P } from '@/components/ui'

export default function MaintenancePieChart() {
    const [maintenanceChecks, setMaintenanceChecks] = useState<any>()

    const [filter, setFilter] = useState({} as SearchFilter)
    const [isLoading, setIsLoading] = useState(true)
    const [keywordFilter, setKeywordFilter] = useState([] as any)
    const [permissions, setPermissions] = useState<any>(false)
    const [edit_task, setEdit_task] = useState<any>(false)
    const [overdueTasks, setOverdueTasks] = useState<any>()
    const [upcomingTasks, setUpcomingTasks] = useState<any>()
    const [extractedDays, setExtractedDays] = useState<any>()
    const [under30, setUnder30] = useState<any>()
    const [between30and90, setBetween30and90] = useState<any>()
    const [over90, setOver90] = useState<any>()

    const init_permissions = () => {
        if (permissions) {
            if (hasPermission('EDIT_TASK', permissions)) {
                setEdit_task(true)
            } else {
                setEdit_task(false)
            }
        }
    }

    useEffect(() => {
        setPermissions(getPermissions)
        init_permissions()
    }, [])

    useEffect(() => {
        init_permissions()
    }, [permissions])

    const [queryMaintenanceChecks] = useLazyQuery(
        ReadComponentMaintenanceChecks,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readComponentMaintenanceChecks.nodes
                if (data) {
                    handleSetMaintenanceChecks(data)
                }
            },
            onError: (error: any) => {
                console.error('queryMaintenanceChecks error', error)
            },
        },
    )
    useEffect(() => {
        if (isLoading) {
            // loadVessels()
            loadMaintenanceChecks()
            setIsLoading(false)
        }
    }, [isLoading])
    const loadMaintenanceChecks = async (
        searchFilter: SearchFilter = { ...filter },
        searchkeywordFilter: any = keywordFilter,
    ) => {
        if (searchkeywordFilter.length > 0) {
            const promises = searchkeywordFilter.map(
                async (keywordFilter: any) => {
                    return await queryMaintenanceChecks({
                        variables: {
                            filter: { ...searchFilter, ...keywordFilter },
                        },
                    })
                },
            )
            let responses = await Promise.all(promises)
            // filter out empty results
            responses = responses.filter(
                (r: any) =>
                    r.data.readComponentMaintenanceChecks.nodes.length > 0,
            )
            // flatten results
            responses = responses.flatMap(
                (r: any) => r.data.readComponentMaintenanceChecks.nodes,
            )
            // filter out duplicates
            responses = responses.filter(
                (value: any, index: any, self: any) =>
                    self.findIndex((v: any) => v.id === value.id) === index,
            )
            handleSetMaintenanceChecks(responses)
        } else {
            await queryMaintenanceChecks({
                variables: {
                    filter: searchFilter,
                },
            })
        }
    }

    const handleSetMaintenanceChecks = (tasks: any) => {
        const activeTasks = tasks
            .filter((task: any) => task.archived === false)
            .map((task: any) => ({
                ...task,
                isOverDue: isOverDueTask(task),
            }))
        setMaintenanceChecks(activeTasks)
        // const appendedData: number[] = Array.from(
        //     new Set(
        //         activeTasks
        //             .filter((item: any) => item.assignedToID > 0)
        //             .map((item: any) => item.assignedToID),
        //     ),
        // )
        // loadCrewMemberInfo(appendedData)
    }

    // const [queryCrewMemberInfo] = useLazyQuery(GET_CREW_BY_IDS, {
    //     fetchPolicy: 'cache-and-network',
    //     onCompleted: (response: any) => {
    //         const data = response.readSeaLogsMembers.nodes
    //         if (data) {
    //             // setCrewInfo(data)
    //         }
    //     },
    //     onError: (error) => {
    //         console.error('queryCrewMemberInfo error', error)
    //     },
    // })
    // const loadCrewMemberInfo = async (crewId: any) => {
    //     await queryCrewMemberInfo({
    //         variables: {
    //             crewMemberIDs: crewId.length > 0 ? crewId : [0],
    //         },
    //     })
    // }

    useEffect(() => {
        if (maintenanceChecks) {
            setOverdueTasks(
                maintenanceChecks.filter(
                    (task: any) =>
                        task.isOverDue.status === 'High' &&
                        task.isOverDue.days.includes('days'),
                ).length,
            )
            setUpcomingTasks(
                maintenanceChecks.filter(
                    (task: any) => task.isOverDue.status === 'Upcoming',
                ),
            )
        }
    }, [maintenanceChecks])

    useEffect(() => {
        if (upcomingTasks) {
            setExtractedDays(
                upcomingTasks.filter((task: any) =>
                    task.isOverDue.days.includes('days'),
                ),
            )
        }
    }, [upcomingTasks])

    useEffect(() => {
        if (extractedDays) {
            setUnder30(
                extractedDays.filter((task: any) => {
                    const match = (task.isOverDue.days || '').match(/(\d+)/)
                    const days = match ? parseInt(match[0], 10) : null
                    return days !== null && days < 30
                }).length,
            )

            setBetween30and90(
                extractedDays.filter((task: any) => {
                    const match = (task.isOverDue.days || '').match(/(\d+)/)
                    const days = match ? parseInt(match[0], 10) : null
                    return days !== null && days >= 30 && days <= 90
                }).length,
            )

            setOver90(
                extractedDays.filter((task: any) => {
                    const match = (task.isOverDue.days || '').match(/(\d+)/)
                    const days = match ? parseInt(match[0], 10) : null
                    return days !== null && days > 90
                }).length,
            )
        }
    }, [extractedDays])

    const chartData = [
        {
            title: 'Tasks overdue',
            amount: overdueTasks,
            fill: 'var(--color-overdue)',
            stroke: 'hsl(1, 97%, 60%)',
        },
        {
            title: 'Tasks due < 30',
            amount: under30,
            fill: 'var(--color-thirtyDays)',
            stroke: 'hsl(205, 78%, 48%)',
        },
        {
            title: 'Tasks due 30 - 90',
            amount: between30and90,
            fill: 'var(--color-thirtyToNinety)',
            stroke: 'hsl(205, 32%, 45%)',
        },
        {
            title: 'Tasks due > 90',
            amount: over90,
            fill: 'var(--color-ninetyPlus)',
            stroke: 'hsl(174, 100%, 40%)',
        },
        //{ title: "Completed", amount: 2, fill: "var(--color-completed)" },
    ]
    const chartConfig = {
        amount: {
            label: 'Amount',
        },
        overdue: {
            label: 'Tasks overdue',
            color: 'var(--chart-1)',
        },
        thirtyDays: {
            label: 'Tasks due under 30-days',
            color: 'var(--chart-3)',
        },
        thirtyToNinety: {
            label: 'Tasks due 30-90 days',
            color: 'var(--chart-4)',
        },
        ninetyPlus: {
            label: 'Tasks due 90+ days',
            color: 'var(--chart-5)',
        },
    } satisfies ChartConfig

    return (
        <div className='flex flex-col text-center'>
            <P>Tasks due - days</P>
            <PieChartComponent
                chartData={chartData}
                chartConfig={chartConfig}
            />
        </div>
    )
}
