'use client'
import React, { useState, useEffect } from 'react'
import { AlertDialogBody, AlertDialogNew } from '@/components/ui'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { <PERSON>lider } from '@/components/ui/slider'
import { H4 } from '@/components/ui/typography'
import { ScrollArea } from '@/components/ui/scroll-area'
import { Alert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { Button } from '@/components/ui/button'
import Editor from '@/app/ui/editor'

export interface RiskDialogProps {
    /**
     * Whether the dialog is open
     */
    open: boolean

    /**
     * Callback when the dialog open state changes
     */
    onOpenChange: (open: boolean) => void

    /**
     * The current risk being edited
     */
    currentRisk: any

    /**
     * Callback when the risk is saved
     */
    onSave: () => void

    /**
     * Available risk options for the combobox
     */
    riskOptions: Array<{ label: string; value: string }>

    /**
     * The currently selected risk value
     */
    riskValue: any

    /**
     * Callback when the risk value changes
     */
    onRiskValueChange: (value: any) => void

    /**
     * The current risk impact options
     */
    riskImpacts: Array<{ label: string; value: string }>

    /**
     * Callback when the risk impact changes
     */
    onRiskImpactChange: (value: any) => void

    /**
     * Callback when the risk probability changes
     */
    onRiskProbabilityChange: (value: number) => void

    /**
     * The current mitigation strategies
     */
    currentStrategies: Array<any>

    /**
     * The current editor content
     */
    content: string

    /**
     * Callback to open the strategy dialog
     */
    onAddStrategyClick: () => void
}

export function RiskDialog({
    open,
    onOpenChange,
    currentRisk,
    onSave,
    riskOptions,
    riskValue,
    onRiskValueChange,
    riskImpacts,
    onRiskImpactChange,
    onRiskProbabilityChange,
    currentStrategies,
    content,
    onAddStrategyClick,
}: RiskDialogProps) {
    return (
        <AlertDialogNew
            openDialog={open}
            setOpenDialog={onOpenChange}
            handleCreate={onSave}
            actionText={currentRisk?.id > 0 ? 'Update' : 'Create Risk'}
            title={currentRisk?.id > 0 ? 'Update Risk' : 'Create New Risk'}
            variant="default"
            size="md"
            position="center"
            cancelText="Cancel">
            <AlertDialogBody className="space-y-5">
                {riskOptions && (
                    <Label label="Risk" htmlFor="risk">
                        <Combobox
                            id="risk"
                            options={riskOptions.filter(
                                (option) =>
                                    option.value !== null &&
                                    option.label !== null,
                            )}
                            defaultValues={riskValue}
                            placeholder="Select a risk"
                            onChange={(value) => {
                                onRiskValueChange(value)
                            }}
                        />
                    </Label>
                )}
                <Label label="Risk impact" htmlFor="impact">
                    <Combobox
                        id="impact"
                        options={riskImpacts.filter(
                            (option) =>
                                option.value !== null && option.label !== null,
                        )}
                        placeholder="Select risk impact"
                        defaultValues={
                            currentRisk?.impact
                                ? riskImpacts.find(
                                      (impact: any) =>
                                          impact.value == currentRisk?.impact,
                                  )
                                : null
                        }
                        required
                        onChange={(value: any) => {
                            onRiskImpactChange(value)
                        }}
                    />
                </Label>

                <Label label="Risk probability">
                    <Slider
                        defaultValue={[currentRisk?.probability ?? 5]}
                        max={10}
                        min={0}
                        step={1}
                        onValueChange={(values) =>
                            onRiskProbabilityChange(values[0])
                        }
                        className="w-full"
                    />
                </Label>
                <div className="space-y-4">
                    <div>
                        <H4>Mitigation strategy</H4>
                        {currentStrategies.length > 0 ? (
                            <ScrollArea className="max-h-[200px] mt-2 border border-border rounded-md p-3">
                                <div className="space-y-3">
                                    {currentStrategies.map((s: any) => (
                                        <div
                                            key={s.id}
                                            className="p-2 bg-muted/50 rounded-md">
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: s.strategy,
                                                }}
                                            />
                                        </div>
                                    ))}
                                </div>
                            </ScrollArea>
                        ) : (
                            <Alert className="mt-2 border-amber-100 bg-amber-50">
                                <AlertCircle className="h-4 w-4 text-amber-600" />
                                <AlertTitle>No strategies added</AlertTitle>
                                <AlertDescription>
                                    Click the button below to add mitigation
                                    strategies.
                                </AlertDescription>
                            </Alert>
                        )}

                        {content && (
                            <div className="mt-3 p-2 bg-muted/50 rounded-md">
                                <div
                                    dangerouslySetInnerHTML={{
                                        __html: content,
                                    }}
                                />
                            </div>
                        )}

                        <Button
                            variant="primary"
                            className="mt-4 w-full sm:w-auto"
                            onClick={onAddStrategyClick}>
                            Add strategy
                        </Button>
                    </div>
                </div>
            </AlertDialogBody>
        </AlertDialogNew>
    )
}
