import { swellRanges } from '@/app/helpers/weatherHelper'
import { Label } from '@/components/ui/label'
import { useEffect, useState } from 'react'
import SeaSwellDropdown from '../sea-swell-dropdown'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui'
import SeaSwellSlider from '../sea-swell-slider'

const SwellWidget = ({
    value = 'smooth',
    editMode = false,
    iconOnly = false,
    onChange,
}: {
    value?: string
    editMode?: boolean
    iconOnly?: boolean
    onChange?: any
}) => {
    const [selectedSwell, setSelectedSwell] = useState(
        swellRanges.find((item) => item.value === value) || swellRanges[0],
    )

    const [fillAmount, setFillAmount] = useState(0)

    useEffect(() => {
        const meterValue = Math.ceil(selectedSwell.fill) // round up the meter value
        const fillPercentage = (meterValue / 100) * 100 // calculate the fill percentage
        setFillAmount(Math.min(Math.max(fillPercentage, 0), 100)) // cap the fill amount between 0 and 100
    }, [selectedSwell])

    useEffect(() => {
        const newSwell =
            swellRanges.find((item) => item.value === value) || swellRanges[0]
        setSelectedSwell(newSwell)
    }, [value])

    const handleSwellChange = (item: any) => {
        setSelectedSwell(item)
        if (onChange) {
            onChange(item)
        }
    }
    const swellSVG = (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="125.971"
            height="48.438"
            viewBox="0 0 125.971 48.438"
            className={` ${iconOnly ? 'h-8' : ''}`}>
            <path
                id="Path_246"
                data-name="Path 246"
                d="M.471,48.438a2.685,2.685,0,0,1,2.684-2.684c6.442,0,14.333-5.368,14.333-11.649a2.684,2.684,0,0,1,5.368,0,11.649,11.649,0,1,0,23.3,0v0a2.684,2.684,0,0,1,5.368,0c0,5.932,10.173,11.7,16.1,11.7a7.7,7.7,0,0,0,6.737-2.979C79.519,35.5,74.687,25.3,69,21.221c-1.449-1.02-4.08-2.469-5.368-.591a3.058,3.058,0,0,1-3.758,1.02c-2.684-1.208-3.785-6.335-2.979-10.173S61.32.5,75.519.5s27.673,8.052,38.3,22.976a94.4,94.4,0,0,1,11.649,22.547"
                transform="translate(0.029 0)"
                fill="none"
                stroke="#035388"
                strokeMiterlimit="10"
                strokeWidth="1"
            />
            <path
                id="Fill"
                d="M.471,48.438a2.685,2.685,0,0,1,2.684-2.684c6.442,0,14.333-5.368,14.333-11.649a2.684,2.684,0,0,1,5.368,0,11.649,11.649,0,1,0,23.3,0v0a2.684,2.684,0,0,1,5.368,0c0,5.932,10.173,11.7,16.1,11.7a7.7,7.7,0,0,0,6.737-2.979C79.519,35.5,74.687,25.3,69,21.221c-1.449-1.02-4.08-2.469-5.368-.591a3.058,3.058,0,0,1-3.758,1.02c-2.684-1.208-3.785-6.335-2.979-10.173S61.32.5,75.519.5s27.673,8.052,38.3,22.976a94.4,94.4,0,0,1,11.649,22.547"
                transform="translate(0.029 0)"
                fill="#035388"
                stroke="#035388"
                strokeMiterlimit="10"
                strokeWidth="1"
                style={{
                    clipPath: `polygon(0 0, ${fillAmount}% 0, ${fillAmount}% 100%, 0 100%)`,
                }}
            />
        </svg>
    )

    if (editMode) {
        return (
            <div className="flex flex-col w-full gap-4">
                <Label label={`Swell: ${selectedSwell.label}`}>
                    <SeaSwellSlider
                        fillAmount={fillAmount}
                        onChange={(swellValue: string) => {
                            const swell = swellRanges.find(
                                (e) => e.value === swellValue,
                            )
                            handleSwellChange(swell)
                        }}
                    />
                </Label>
            </div>
        )
    }

    return (
        <div className="flex flex-col justify-center items-center p-4">
            <div className="relative">
                {!iconOnly && swellSVG}
                {iconOnly && (
                    <Popover triggerType="hover">
                        <PopoverTrigger asChild>{swellSVG}</PopoverTrigger>
                        <PopoverContent>
                            <span className="font-bold">Swell:</span>{' '}
                            {selectedSwell.label}
                        </PopoverContent>
                    </Popover>
                )}
            </div>
            {!iconOnly && (
                <div className="mt-2">
                    <span className="font-semibold">Swell:</span>{' '}
                    {selectedSwell.label}
                </div>
            )}
        </div>
    )
}

export default SwellWidget
