import BarometricPressureSlider from '../barometric-pressure-slider'
import { Label } from '@/components/ui/label'

const BarometerWidget = ({
    value = 0,
    editMode = false,
    onChange,
}: {
    value?: number
    editMode?: boolean
    onChange?: any
}) => {
    if (editMode) {
        return (
            <div className="flex flex-col w-full gap-4">
                <BarometricPressureSlider
                    label="Barometer"
                    value={value}
                    onChange={onChange}
                    unitOfMeasure="hPa"
                />
            </div>
        )
    }

    return (
        <div className="flex items-center justify-center">
            <div className="flex flex-wrap gap-1 font-bold items-end">
                <div className="text-4xl">{value}</div>
                <div className="text-base">hPa</div>
            </div>
        </div>
    )
}

export default BarometerWidget
