'use client'

import { OverdueTrainingList } from '@/app/ui/crew-training/list'
import { TrainingList } from '@/app/ui/reporting/crew-training-completed-report'

export default function CrewTrainingTab({
    trainingSessions,
    trainingSessionDues,
}: {
    trainingSessions: any
    trainingSessionDues: any
}) {
    return (
        <div className="space-y-4">
            {trainingSessionDues?.length > 0 && (
                <OverdueTrainingList
                    trainingSessionDues={trainingSessionDues}
                    isVesselView={true}
                />
            )}
            {trainingSessions?.length > 0 && (
                <TrainingList
                    trainingList={trainingSessions}
                    trainingSessionDues={trainingSessionDues}
                    isVesselView={true}
                />
            )}
        </div>
    )
}
