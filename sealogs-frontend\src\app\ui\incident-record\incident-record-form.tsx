'use client'

import React, { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useLazyQuery, useMutation } from '@apollo/client'
import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'
import {
    CREATE_INCIDENT_RECORD,
    UPDATE_INCIDENT_RECORD,
    DELETE_INCIDENT_RECORDS,
} from './graphql/mutations'
import { GET_INCIDENT_RECORD } from './graphql/queries'
import DateField from '@/components/ui/date-field'
import TimeField from '@/app/ui/logbook/components/time'
import LocationField from '@/app/ui/logbook/components/location'
import CrewMultiSelectDropdown from '@/app/ui/crew/multiselect-dropdown'
import Loading from '@/app/loading'
import UploadCloudFlare from '@/app/ui/logbook/components/upload-cf'
import { CREATE_R2FILE } from '@/app/lib/graphQL/mutation'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Checkbox } from '@/components/ui/checkbox'
import { Textarea } from '@/components/ui/textarea'
import { Button } from '@/components/ui/button'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { useToast } from '@/hooks/use-toast'
import { Combobox } from '@/components/ui/comboBox'
import { H1, H2, H3, P } from '@/components/ui/typography'
import { ArrowLeft, Check } from 'lucide-react'
import VesselDropdown from '@/components/filter/components/vessel-dropdown'
import CrewDropdown from '@/components/filter/components/crew-dropdown'
import Editor from '../editor'
import { CreateTripEvent, UpdateTripEvent } from '@/app/lib/graphQL/mutation'
import { useQueryState } from 'nuqs'
import { CheckFieldLabel } from '@/components/ui/check-field-label'

interface IncidentRecordFormProps {
    id?: number
    inLogbook?: boolean
    closeModal?: () => void
    updateTripReport?: any
    currentTrip?: any

    selectedEvent?: any
}

const IncidentRecordForm: React.FC<IncidentRecordFormProps> = ({
    id = 0,
    inLogbook = false,
    closeModal,
    updateTripReport,
    currentTrip,
    selectedEvent,
}) => {
    const router = useRouter()
    const searchParams = useSearchParams()
    // Get recordId from URL, props, or selectedEvent
    const recordId = searchParams.get('id')
        ? parseInt(searchParams.get('id') as string)
        : id
    const { toast } = useToast()

    // Form state
    const [title, setTitle] = useState<string>('')
    const [vesselId, setVesselId] = useState<string | null>(null)
    const [startDate, setStartDate] = useState<any>(dayjs())
    const [startTime, setStartTime] = useState<any>(dayjs().format('HH:mm'))
    const [endDate, setEndDate] = useState<any>(dayjs())
    const [endTime, setEndTime] = useState<any>(dayjs().format('HH:mm'))
    const [locationId, setLocationId] = useState<string | null>(null)
    const [incidentType, setIncidentType] = useState<string>('incident')
    const [personsInvolved, setPersonsInvolved] = useState<number>(0)
    const [reportedById, setReportedById] = useState<string | null>(null)
    const [description, setDescription] = useState<string>('')
    const [treatment, setTreatment] = useState<string>('none')
    const [contributingFactor, setContributingFactor] = useState<string>('none')
    const [riskAssessmentReviewed, setRiskAssessmentReviewed] =
        useState<boolean>(false)
    const [notifiable, setNotifiable] = useState<boolean>(false)
    const [membersToNotify, setMembersToNotify] = useState<string[]>([])

    // UI state
    const [loading, setLoading] = useState<boolean>(false) // Start with loading false
    const [openDeleteConfirmation, setOpenDeleteConfirmation] =
        useState<boolean>(false)
    const [attachments, setAttachments] = useState<any[]>([])

    const [activeTab] = useQueryState('tab')

    const [createIncidentRecord, { loading: createLoading }] = useMutation(
        CREATE_INCIDENT_RECORD,
        {
            onError: (error) => {
                const errorContext = {
                    timestamp: new Date().toISOString(),
                    operation: 'CREATE_INCIDENT_RECORD',
                    formData: {
                        title,
                        vesselId,
                        incidentType,
                        personsInvolved,
                        reportedById,
                        locationId,
                        startDate:
                            startDate?.format?.('YYYY-MM-DD') || startDate,
                        startTime,
                        endDate: endDate?.format?.('YYYY-MM-DD') || endDate,
                        endTime,
                    },
                    errorMessage: error.message,
                    errorCode: error.graphQLErrors?.[0]?.extensions?.code,
                    networkError: error.networkError,
                    graphQLErrors: error.graphQLErrors,
                }
                console.error('Error creating incident record:', errorContext)
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: `Error creating incident record: ${error.message}`,
                })
            },
        },
    )

    const [updateIncidentRecord, { loading: updateLoading }] = useMutation(
        UPDATE_INCIDENT_RECORD,
        {
            onCompleted: () => {
                try {
                    console.log(
                        'Update incident record completed successfully',
                        {
                            timestamp: new Date().toISOString(),
                            recordId,
                            attachmentsCount: attachments.length,
                        },
                    )

                    // Process any new attachments
                    if (attachments.length > 0) {
                        attachments.forEach((attachment: any) => {
                            if (!attachment.id) {
                                try {
                                    createAttachments({
                                        variables: {
                                            input: {
                                                title: attachment.title,
                                                incidentRecordID: recordId,
                                            },
                                        },
                                    })
                                } catch (attachmentError) {
                                    console.error(
                                        'Error creating attachment after update:',
                                        {
                                            timestamp: new Date().toISOString(),
                                            recordId,
                                            attachmentTitle: attachment.title,
                                            error: attachmentError,
                                        },
                                    )
                                }
                            }
                        })
                    }

                    router.push('/incident-records')
                } catch (completionError) {
                    console.error('Error in update completion handler:', {
                        timestamp: new Date().toISOString(),
                        recordId,
                        error: completionError,
                    })
                }
            },
            onError: (error) => {
                const errorContext = {
                    timestamp: new Date().toISOString(),
                    operation: 'UPDATE_INCIDENT_RECORD',
                    recordId,
                    formData: {
                        title,
                        vesselId,
                        incidentType,
                        personsInvolved,
                        reportedById,
                        locationId,
                        startDate:
                            startDate?.format?.('YYYY-MM-DD') || startDate,
                        startTime,
                        endDate: endDate?.format?.('YYYY-MM-DD') || endDate,
                        endTime,
                    },
                    errorMessage: error.message,
                    errorCode: error.graphQLErrors?.[0]?.extensions?.code,
                    networkError: error.networkError,
                    graphQLErrors: error.graphQLErrors,
                }
                console.error('Error updating incident record:', errorContext)
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: `Error updating incident record: ${error.message}`,
                })
            },
        },
    )

    const [deleteIncidentRecords, { loading: deleteLoading }] = useMutation(
        DELETE_INCIDENT_RECORDS,
        {
            onCompleted: () => {
                try {
                    console.log(
                        'Delete incident record completed successfully',
                        {
                            timestamp: new Date().toISOString(),
                            recordId,
                        },
                    )
                    setOpenDeleteConfirmation(false)
                    router.push('/incident-records')
                } catch (completionError) {
                    console.error('Error in delete completion handler:', {
                        timestamp: new Date().toISOString(),
                        recordId,
                        error: completionError,
                    })
                    setOpenDeleteConfirmation(false)
                }
            },
            onError: (error) => {
                const errorContext = {
                    timestamp: new Date().toISOString(),
                    operation: 'DELETE_INCIDENT_RECORDS',
                    recordId,
                    errorMessage: error.message,
                    errorCode: error.graphQLErrors?.[0]?.extensions?.code,
                    networkError: error.networkError,
                    graphQLErrors: error.graphQLErrors,
                }
                console.error('Error deleting incident record:', errorContext)
                setOpenDeleteConfirmation(false)
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: `Error deleting incident record: ${error.message}`,
                })
            },
        },
    )

    // Create attachments mutation
    const [createAttachments, { loading: attachmentsLoading }] = useMutation(
        CREATE_R2FILE,
        {
            onCompleted: (response) => {
                try {
                    console.log('Create attachment completed successfully', {
                        timestamp: new Date().toISOString(),
                        recordId,
                        attachmentData: response.createR2File,
                    })

                    const data = response.createR2File
                    const newAttachments = attachments.map(
                        (attachment: any) => {
                            if (attachment.title === data.title) {
                                return {
                                    ...attachment,
                                    id: data.id,
                                }
                            }
                            return attachment
                        },
                    )
                    setAttachments(newAttachments)
                } catch (completionError) {
                    console.error(
                        'Error in attachment creation completion handler:',
                        {
                            timestamp: new Date().toISOString(),
                            recordId,
                            response,
                            error: completionError,
                        },
                    )
                }
            },
            onError: (error) => {
                const errorContext = {
                    timestamp: new Date().toISOString(),
                    operation: 'CREATE_R2FILE',
                    recordId,
                    attachmentsCount: attachments.length,
                    errorMessage: error.message,
                    errorCode: error.graphQLErrors?.[0]?.extensions?.code,
                    networkError: error.networkError,
                    graphQLErrors: error.graphQLErrors,
                }
                console.error('Error creating attachments:', errorContext)
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: `Error creating attachments: ${error.message}`,
                })
            },
        },
    )

    // GraphQL query to fetch incident record for editing
    const [getIncidentRecord] = useLazyQuery(GET_INCIDENT_RECORD, {
        fetchPolicy: 'network-only',
        onCompleted: (data) => {
            try {
                console.log('Get incident record completed successfully', {
                    timestamp: new Date().toISOString(),
                    recordId,
                    hasData: !!data?.readOneIncidentRecord,
                })

                if (data && data.readOneIncidentRecord) {
                    const record = data.readOneIncidentRecord

                    // Parse dates and times from the format "YYYY-MM-DD HH:mm"
                    if (record.startDate) {
                        try {
                            // Parse the date and extract just the date part
                            setStartDate(dayjs(record.startDate))
                            // Format the time to HH:mm using dayjs
                            setStartTime(
                                dayjs(record.startDate).format('HH:mm'),
                            )
                        } catch (dateError) {
                            console.error('Error parsing start date:', {
                                timestamp: new Date().toISOString(),
                                recordId,
                                startDate: record.startDate,
                                error: dateError,
                            })
                        }
                    }

                    if (record.endDate) {
                        try {
                            // Parse the date and extract just the date part
                            setEndDate(dayjs(record.endDate))
                            // Format the time to HH:mm using dayjs
                            setEndTime(dayjs(record.endDate).format('HH:mm'))
                        } catch (dateError) {
                            console.error('Error parsing end date:', {
                                timestamp: new Date().toISOString(),
                                recordId,
                                endDate: record.endDate,
                                error: dateError,
                            })
                        }
                    }

                    try {
                        // Set other form fields
                        console.log('Setting form fields from loaded record:', {
                            timestamp: new Date().toISOString(),
                            recordId,
                            title: record.title,
                            vessel: {
                                id: record.vessel?.id,
                                name: record.vessel?.title,
                                fullVesselObject: record.vessel,
                            },
                            reportedBy: {
                                id: record.reportedBy?.id,
                                name:
                                    record.reportedBy?.firstName +
                                    ' ' +
                                    record.reportedBy?.surname,
                                fullReportedByObject: record.reportedBy,
                            },
                            description: {
                                length: record.description?.length || 0,
                                preview:
                                    record.description?.substring(0, 100) || '',
                                fullDescription: record.description,
                            },
                        })

                        setTitle(record.title || '')

                        // Set locationId as a string (not a number)
                        if (record.location?.id) {
                            // Store the location ID as a string to match exactly what's in the geoLocations array
                            setLocationId(record.location.id)
                            console.log('Location ID set:', {
                                timestamp: new Date().toISOString(),
                                locationId: record.location.id,
                                locationName: record.location?.title,
                            })
                        } else {
                            setLocationId(null)
                            console.log(
                                'Location ID cleared (no location in record)',
                            )
                        }

                        // Set vessel ID with detailed logging
                        const vesselIdToSet = record.vessel?.id || null

                        // Check if vessel ID is "0" which is invalid
                        if (vesselIdToSet === '0') {
                            console.warn(
                                'Invalid vessel ID "0" detected - this vessel does not exist in the system:',
                                {
                                    timestamp: new Date().toISOString(),
                                    vesselId: vesselIdToSet,
                                    vesselObject: record.vessel,
                                    recordId,
                                    recommendation:
                                        'Set vessel ID to null to clear the invalid reference',
                                },
                            )
                            // Set to null instead of "0" to clear the invalid reference
                            setVesselId(null)
                        } else {
                            setVesselId(vesselIdToSet)
                        }

                        console.log('Vessel ID set:', {
                            timestamp: new Date().toISOString(),
                            originalVesselId: record.vessel?.id,
                            finalVesselId:
                                vesselIdToSet === '0' ? null : vesselIdToSet,
                            vesselTitle: record.vessel?.title,
                            vesselName:
                                record.vessel?.title || record.vessel?.name,
                            vesselObject: record.vessel,
                            previousVesselId: vesselId,
                            // Log all vessel properties to see what's available
                            vesselProperties: record.vessel
                                ? Object.keys(record.vessel)
                                : [],
                            // Check if vessel ID is string "0" which might be the issue
                            vesselIdType: typeof vesselIdToSet,
                            vesselIdAsNumber: vesselIdToSet
                                ? Number(vesselIdToSet)
                                : null,
                            wasInvalidIdCorrected: vesselIdToSet === '0',
                        })

                        setIncidentType(record.incidentType || 'incident')
                        setPersonsInvolved(record.personsInvolved || 0)

                        // Set reported by ID with detailed logging
                        const reportedByIdToSet = record.reportedBy?.id || null
                        setReportedById(reportedByIdToSet)
                        console.log('Reported by ID set:', {
                            timestamp: new Date().toISOString(),
                            reportedById: reportedByIdToSet,
                            reportedByName: record.reportedBy
                                ? `${record.reportedBy.firstName || ''} ${record.reportedBy.surname || ''}`.trim()
                                : 'No name',
                            reportedByObject: record.reportedBy,
                            previousReportedById: reportedById,
                        })

                        // Set description with detailed logging
                        const descriptionToSet = record.description || ''
                        setDescription(descriptionToSet)
                        console.log('Description set:', {
                            timestamp: new Date().toISOString(),
                            originalDescription: record.description,
                            descriptionLength: descriptionToSet.length,
                            descriptionPreview: descriptionToSet.substring(
                                0,
                                200,
                            ),
                            hasDescription: !!descriptionToSet,
                            previousDescriptionLength: description.length,
                            wasNull: record.description === null,
                            wasUndefined: record.description === undefined,
                        })

                        setTreatment(record.treatment || 'none')
                        setContributingFactor(
                            record.contributingFactor || 'none',
                        )
                        setRiskAssessmentReviewed(
                            record.riskAssessmentReviewed || false,
                        )
                        setNotifiable(record.notifiable || false)

                        // Set members to notify
                        if (
                            record.membersToNotify &&
                            record.membersToNotify.nodes
                        ) {
                            setMembersToNotify(
                                record.membersToNotify.nodes.map(
                                    (member: any) => member.id,
                                ),
                            )
                        }

                        // Set attachments
                        if (record.attachments && record.attachments.nodes) {
                            setAttachments(record.attachments.nodes)
                        }
                    } catch (stateUpdateError) {
                        console.error(
                            'Error updating form state from record data:',
                            {
                                timestamp: new Date().toISOString(),
                                recordId,
                                record,
                                error: stateUpdateError,
                            },
                        )
                    }

                    setLoading(false)
                } else {
                    console.warn('No incident record data received:', {
                        timestamp: new Date().toISOString(),
                        recordId,
                        data,
                    })
                    setLoading(false)
                }
            } catch (completionError) {
                console.error(
                    'Error in get incident record completion handler:',
                    {
                        timestamp: new Date().toISOString(),
                        recordId,
                        data,
                        error: completionError,
                    },
                )
                setLoading(false)
            }
        },
        onError: (error) => {
            const errorContext = {
                timestamp: new Date().toISOString(),
                operation: 'GET_INCIDENT_RECORD',
                recordId,
                errorMessage: error.message,
                errorCode: error.graphQLErrors?.[0]?.extensions?.code,
                networkError: error.networkError,
                graphQLErrors: error.graphQLErrors,
            }
            console.error('Error fetching incident record:', errorContext)
            toast({
                variant: 'destructive',
                title: 'Error',
                description: `Error fetching incident record: ${error.message}`,
            })
            setLoading(false)
        },
    })

    // Load incident record data if editing
    useEffect(() => {
        try {
            // Check if we have a valid recordId from props or from selectedEvent
            let effectiveRecordId = recordId

            // If recordId is 0, try to get it from selectedEvent
            if (effectiveRecordId === 0 && selectedEvent) {
                effectiveRecordId = selectedEvent.incidentRecordID || 0
            }

            console.log('Loading incident record data:', {
                timestamp: new Date().toISOString(),
                recordId,
                effectiveRecordId,
                hasSelectedEvent: !!selectedEvent,
                selectedEventId: selectedEvent?.incidentRecordID,
            })

            if (effectiveRecordId > 0) {
                // Only set loading to true for edit forms
                setLoading(true)
                getIncidentRecord({ variables: { id: effectiveRecordId } })
            }
            // For new forms, we keep loading as false
        } catch (effectError) {
            console.error('Error in load incident record useEffect:', {
                timestamp: new Date().toISOString(),
                recordId,
                selectedEvent,
                error: effectError,
            })
            setLoading(false)
        }
    }, [recordId, selectedEvent, getIncidentRecord])

    // Set vessel ID from URL if in logbook context
    useEffect(() => {
        try {
            console.log('Setting vessel ID from logbook context:', {
                timestamp: new Date().toISOString(),
                inLogbook,
                currentTripVesselID: currentTrip?.vesselID,
                urlVesselId: searchParams.get('vesselID'),
            })

            if (inLogbook && currentTrip?.vesselID) {
                setVesselId(currentTrip.vesselID.toString())
            } else if (inLogbook) {
                // If we don't have currentTrip.vesselID, try to get it from the URL
                const urlVesselId = searchParams.get('vesselID')
                if (urlVesselId) {
                    setVesselId(urlVesselId)
                } else {
                    console.warn('No vessel ID found in logbook context:', {
                        timestamp: new Date().toISOString(),
                        inLogbook,
                        currentTrip,
                        searchParams: Object.fromEntries(
                            searchParams.entries(),
                        ),
                    })
                }
            }
        } catch (effectError) {
            console.error('Error in vessel ID useEffect:', {
                timestamp: new Date().toISOString(),
                inLogbook,
                currentTrip,
                error: effectError,
            })
        }
    }, [inLogbook, currentTrip, searchParams])

    // Debug useEffect to track vessel, reportedBy, and description state changes
    useEffect(() => {
        console.log('State values changed - vessel, reportedBy, description:', {
            timestamp: new Date().toISOString(),
            vesselId,
            reportedById,
            description: {
                length: description.length,
                preview: description.substring(0, 100),
                hasContent: !!description,
            },
            recordId,
            loading,
        })
    }, [vesselId, reportedById, description, recordId, loading])

    // Handle form submission
    const handleSubmit = () => {
        try {
            console.log('Starting form submission:', {
                timestamp: new Date().toISOString(),
                recordId,
                isUpdate: recordId > 0,
                formState: {
                    title: title?.length || 0,
                    vesselId,
                    incidentType,
                    personsInvolved,
                    reportedById,
                    locationId,
                    attachmentsCount: attachments.length,
                },
            })

            // Check if title is empty
            if (isEmpty(trim(title))) {
                console.error('Form validation failed - title is required:', {
                    timestamp: new Date().toISOString(),
                    title,
                    trimmedTitle: trim(title),
                })
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Title is required',
                })
                return
            }

            // Combine date and time in the format "YYYY-MM-DD HH:mm"
            let startDateTime: string
            let endDateTime: string

            try {
                startDateTime = `${dayjs(startDate).format('YYYY-MM-DD')} ${startTime}`
                endDateTime = `${dayjs(endDate).format('YYYY-MM-DD')} ${endTime}`

                console.log('Date/time formatting successful:', {
                    timestamp: new Date().toISOString(),
                    startDate,
                    startTime,
                    endDate,
                    endTime,
                    startDateTime,
                    endDateTime,
                })
            } catch (dateError) {
                console.error('Error formatting dates/times:', {
                    timestamp: new Date().toISOString(),
                    startDate,
                    startTime,
                    endDate,
                    endTime,
                    error: dateError,
                })
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Invalid date or time format',
                })
                return
            }

            // Ensure we have a vessel ID if in logbook context
            if (inLogbook && !vesselId && currentTrip?.vesselID) {
                console.log('Setting vessel ID from current trip:', {
                    timestamp: new Date().toISOString(),
                    currentTripVesselID: currentTrip.vesselID,
                })
                setVesselId(currentTrip.vesselID.toString())
            } else if (inLogbook && !vesselId) {
                const urlVesselId = searchParams.get('vesselID')
                if (urlVesselId) {
                    console.log('Setting vessel ID from URL:', {
                        timestamp: new Date().toISOString(),
                        urlVesselId,
                    })
                    setVesselId(urlVesselId)
                } else {
                    console.error(
                        'Vessel ID is required for logbook context:',
                        {
                            timestamp: new Date().toISOString(),
                            inLogbook,
                            vesselId,
                            currentTrip,
                            searchParams: Object.fromEntries(
                                searchParams.entries(),
                            ),
                        },
                    )
                    toast({
                        variant: 'destructive',
                        title: 'Error',
                        description: 'Vessel ID is required',
                    })
                    return
                }
            }

            const formData = {
                title: title,
                startDate: startDateTime,
                endDate: endDateTime,
                locationID: locationId, // This is a string ID from the database
                vesselID: vesselId,
                incidentType: incidentType,
                personsInvolved: personsInvolved,
                reportedByID: reportedById,
                description: description,
                treatment: treatment,
                contributingFactor: contributingFactor,
                riskAssessmentReviewed: riskAssessmentReviewed,
                notifiable: notifiable,
                membersToNotify: membersToNotify.join(','),
            }

            console.log('Form data prepared for submission:', {
                timestamp: new Date().toISOString(),
                formData,
                recordId,
                isUpdate: recordId > 0,
            })

            if (recordId > 0) {
                // Update existing record
                console.log('Updating existing incident record:', {
                    timestamp: new Date().toISOString(),
                    recordId,
                })
                updateIncidentRecord({
                    variables: {
                        input: {
                            id: recordId,
                            ...formData,
                        },
                    },
                    onCompleted: () => {
                        try {
                            console.log(
                                'Update mutation completed, processing attachments:',
                                {
                                    timestamp: new Date().toISOString(),
                                    recordId,
                                    attachmentsCount: attachments.length,
                                },
                            )
                            // Process any new attachments
                            if (attachments.length > 0) {
                                attachments.forEach((attachment: any) => {
                                    if (!attachment.id) {
                                        createAttachments({
                                            variables: {
                                                input: {
                                                    title: attachment.title,
                                                    incidentRecordID: recordId,
                                                },
                                            },
                                        })
                                    }
                                })
                            }
                        } catch (attachmentProcessingError) {
                            console.error(
                                'Error processing attachments after update:',
                                {
                                    timestamp: new Date().toISOString(),
                                    recordId,
                                    error: attachmentProcessingError,
                                },
                            )
                        }
                    },
                })
            } else {
                // Create new record
                console.log('Creating new incident record:', {
                    timestamp: new Date().toISOString(),
                })
                createIncidentRecord({
                    variables: {
                        input: formData,
                    },
                    onCompleted: (response) => {
                        try {
                            const newRecordId = response.createIncidentRecord.id
                            console.log('Create mutation completed:', {
                                timestamp: new Date().toISOString(),
                                newRecordId,
                                attachmentsCount: attachments.length,
                            })

                            // Create attachments for the new record
                            if (attachments.length > 0 && newRecordId) {
                                attachments.forEach((attachment: any) => {
                                    if (!attachment.id) {
                                        createAttachments({
                                            variables: {
                                                input: {
                                                    title: attachment.title,
                                                    incidentRecordID:
                                                        newRecordId,
                                                },
                                            },
                                        })
                                    }
                                })
                            }

                            if (activeTab == 'trip-log') {
                                if (closeModal) {
                                    closeModal()
                                }
                            } else {
                                router.push('/incident-records')
                            }
                        } catch (createCompletionError) {
                            console.error(
                                'Error in create completion handler:',
                                {
                                    timestamp: new Date().toISOString(),
                                    response,
                                    error: createCompletionError,
                                },
                            )
                        }
                    },
                })
            }
        } catch (submitError) {
            console.error('Error in form submission:', {
                timestamp: new Date().toISOString(),
                recordId,
                formData: {
                    title: title?.length || 0,
                    vesselId,
                    incidentType,
                },
                error: submitError,
            })
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'An error occurred while submitting the form',
            })
        }
    }

    // Handle delete
    const handleDelete = () => {
        try {
            console.log('Starting delete operation:', {
                timestamp: new Date().toISOString(),
                recordId,
            })

            if (recordId > 0) {
                deleteIncidentRecords({
                    variables: {
                        ids: [recordId],
                    },
                })
            } else {
                console.error('Cannot delete - invalid record ID:', {
                    timestamp: new Date().toISOString(),
                    recordId,
                })
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Cannot delete - invalid record ID',
                })
            }
        } catch (deleteError) {
            console.error('Error in delete handler:', {
                timestamp: new Date().toISOString(),
                recordId,
                error: deleteError,
            })
            toast({
                variant: 'destructive',
                title: 'Error',
                description: 'An error occurred while deleting the record',
            })
        }
    }

    // Incident type options
    const incidentTypeOptions = [
        { value: 'accident', label: 'Accident' },
        { value: 'incident', label: 'Incident' },
        { value: 'nearMiss', label: 'Near Miss' },
        { value: 'safetyEvent', label: 'Safety Event' },
        { value: 'medical', label: 'Illness or Injury (Medical)' },
        { value: 'assistanceRendered', label: 'Assistance Rendered' },
        {
            value: 'mental',
            label: 'Behavioural Challenge / Mental Health and Wellness',
        },
        { value: 'other', label: 'Other' },
    ]

    // Treatment options
    const treatmentOptions = [
        { value: 'none', label: 'No Treatment' },
        { value: 'firstAidOnboard', label: 'First Aid Onboard' },
        { value: 'firstAidOnshore', label: 'First Aid Onshore' },
        { value: 'restrictedWorkInjury', label: 'Restricted Work Injury' },
        { value: 'lostTimeInjury', label: 'Lost Time Injury' },
        { value: 'doctorOrHospital', label: 'Doctor or Hospital' },
        { value: 'fatality', label: 'Fatality' },
        { value: 'other', label: 'Other' },
    ]

    // Contributing factor options
    const contributingFactorOptions = [
        { value: 'none', label: 'None' },
        { value: 'humanError', label: 'Human Error' },
        { value: 'equipmentProcedure', label: 'Equipment Procedure' },
        { value: 'environment', label: 'Environment' },
        { value: 'other', label: 'Other' },
    ]

    // Show loading component only when retrieving data
    if (loading) {
        return <Loading message={`Loading incident record...`} />
    }

    // Log current form field values before rendering
    console.log('Rendering form with current field values:', {
        timestamp: new Date().toISOString(),
        recordId,
        isEditMode: recordId > 0,
        formFieldValues: {
            vesselId: {
                value: vesselId,
                type: typeof vesselId,
                isZeroString: vesselId === '0',
                isZeroNumber: Number(vesselId) === 0,
                asNumber: vesselId ? Number(vesselId) : null,
            },
            reportedById: {
                value: reportedById,
                type: typeof reportedById,
            },
            description: {
                length: description.length,
                preview: description.substring(0, 50),
                hasContent: !!description,
            },
            title: title?.substring(0, 50),
            locationId,
        },
        loading,
    })

    return (
        <>
            <H1>
                {recordId > 0 ? 'Edit Incident Record' : 'New Incident Record'}
            </H1>

            <div className="py-5 space-y-8">
                <div>
                    <H3 className="mb-4">
                        Incident – Accident – Near Miss – Safety Event
                    </H3>
                    <P className="mb-6">
                        This form is designed to be simple and practical enough
                        to be completed during or shortly after a high-stress
                        situation. It does not replace any official reporting
                        forms required by Authorities, nor does it substitute
                        your internal documentation or investigation processes.
                    </P>
                </div>

                <div className="space-y-8">
                    <Label htmlFor="title" label="Title">
                        <Input
                            id="title"
                            type="text"
                            value={title}
                            onChange={(e) => setTitle(e.target.value)}
                            placeholder="Enter incident title"
                        />
                    </Label>

                    <Label htmlFor="vessel" label="Vessel">
                        <VesselDropdown
                            value={vesselId}
                            onChange={(option: any) => {
                                const newVesselId = option?.value || null
                                setVesselId(newVesselId)
                            }}
                            placeholder="Select Vessel"
                            isClearable={true}
                            multi={false}
                        />
                    </Label>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="start-date" label="Start Date">
                            <DateField
                                date={startDate}
                                handleDateChange={setStartDate}
                                dateID="start-date"
                                fieldName="Start Date"
                            />
                        </Label>

                        <Label htmlFor="start-time" label="Start Time">
                            <TimeField
                                time={startTime}
                                handleTimeChange={(newTime: any) => {
                                    try {
                                        // Use dayjs to format the time to HH:mm
                                        const formattedTime =
                                            dayjs(newTime).format('HH:mm')
                                        setStartTime(formattedTime)
                                        console.log('Start time updated:', {
                                            timestamp: new Date().toISOString(),
                                            newTime,
                                            formattedTime,
                                        })
                                    } catch (timeError) {
                                        console.error(
                                            'Error formatting start time:',
                                            {
                                                timestamp:
                                                    new Date().toISOString(),
                                                newTime,
                                                error: timeError,
                                            },
                                        )
                                    }
                                }}
                                timeID="start-time"
                                fieldName="Start Time"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="end-date" label="End Date">
                            <DateField
                                date={endDate}
                                handleDateChange={setEndDate}
                                dateID="end-date"
                                fieldName="End Date"
                            />
                        </Label>

                        <Label htmlFor="end-time" label="End Time">
                            <TimeField
                                time={endTime}
                                handleTimeChange={(newTime: any) => {
                                    try {
                                        // Use dayjs to format the time to HH:mm
                                        const formattedTime =
                                            dayjs(newTime).format('HH:mm')
                                        setEndTime(formattedTime)
                                        console.log('End time updated:', {
                                            timestamp: new Date().toISOString(),
                                            newTime,
                                            formattedTime,
                                        })
                                    } catch (timeError) {
                                        console.error(
                                            'Error formatting end time:',
                                            {
                                                timestamp:
                                                    new Date().toISOString(),
                                                newTime,
                                                error: timeError,
                                            },
                                        )
                                    }
                                }}
                                timeID="end-time"
                                fieldName="End Time"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="location" label="Location">
                            <LocationField
                                handleLocationChange={(value: any) => {
                                    try {
                                        console.log(
                                            'Location change triggered:',
                                            {
                                                timestamp:
                                                    new Date().toISOString(),
                                                value,
                                                currentLocationId: locationId,
                                            },
                                        )

                                        // If value is null or undefined, clear the location
                                        if (!value) {
                                            setLocationId(null)
                                            console.log('Location cleared')
                                            return
                                        }

                                        // Check if the value is from dropdown selection (has 'value' property)
                                        if (value.value) {
                                            // Handle location selected from dropdown
                                            setLocationId(value.value)
                                            console.log(
                                                'Location set from dropdown:',
                                                {
                                                    timestamp:
                                                        new Date().toISOString(),
                                                    locationId: value.value,
                                                },
                                            )
                                        } else if (
                                            value.latitude !== undefined &&
                                            value.longitude !== undefined
                                        ) {
                                            // For incident records, we only support location IDs, not coordinates
                                            // If coordinates are provided, we clear the location ID
                                            console.warn(
                                                'Coordinates provided but not supported for incident records:',
                                                {
                                                    timestamp:
                                                        new Date().toISOString(),
                                                    latitude: value.latitude,
                                                    longitude: value.longitude,
                                                },
                                            )
                                            setLocationId(null)
                                        } else {
                                            console.warn(
                                                'Unrecognized location value format:',
                                                {
                                                    timestamp:
                                                        new Date().toISOString(),
                                                    value,
                                                },
                                            )
                                        }
                                    } catch (locationError) {
                                        console.error(
                                            'Error handling location change:',
                                            {
                                                timestamp:
                                                    new Date().toISOString(),
                                                value,
                                                error: locationError,
                                            },
                                        )
                                    }
                                }}
                                setCurrentLocation={() => {}}
                                currentEvent={{
                                    geoLocationID: locationId,
                                }}
                                showAddNewLocation={false}
                                showUseCoordinates={false}
                                showCurrentLocation={false}
                            />
                        </Label>

                        <Label htmlFor="incident-type" label="Incident Type">
                            <Combobox
                                id="incident-type"
                                options={incidentTypeOptions}
                                value={
                                    incidentTypeOptions.find(
                                        (option) =>
                                            option.value === incidentType,
                                    ) || null
                                }
                                onChange={(option: any) =>
                                    setIncidentType(option?.value || 'incident')
                                }
                                placeholder="Select Incident Type"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label
                            htmlFor="persons-involved"
                            label="Person(s) Involved">
                            <Input
                                id="persons-involved"
                                type="number"
                                value={personsInvolved}
                                onChange={(e) => {
                                    try {
                                        const newValue =
                                            parseInt(e.target.value) || 0
                                        setPersonsInvolved(newValue)
                                        console.log(
                                            'Persons involved updated:',
                                            {
                                                timestamp:
                                                    new Date().toISOString(),
                                                rawValue: e.target.value,
                                                parsedValue: newValue,
                                            },
                                        )
                                    } catch (parseError) {
                                        console.error(
                                            'Error parsing persons involved:',
                                            {
                                                timestamp:
                                                    new Date().toISOString(),
                                                value: e.target.value,
                                                error: parseError,
                                            },
                                        )
                                        setPersonsInvolved(0)
                                    }
                                }}
                                min="0"
                            />
                        </Label>

                        <Label htmlFor="reported-by" label="Reported By">
                            <CrewDropdown
                                value={reportedById}
                                onChange={(option: any) => {
                                    const newReportedById =
                                        option?.value || null
                                    setReportedById(newReportedById)
                                }}
                                placeholder="Select Crew Member"
                                isClearable={true}
                            />
                        </Label>
                    </div>

                    <Label htmlFor="description" label="Description">
                        <Editor
                            id="description"
                            placeholder="Provide a detailed narrative (include info on treatment refusals, police or emergency service feedback, etc.)"
                            className="w-full"
                            content={description}
                            handleEditorChange={(newDescription: string) => {
                                console.log(
                                    'Description Editor onChange triggered:',
                                    {
                                        timestamp: new Date().toISOString(),
                                        previousDescriptionLength:
                                            description.length,
                                        newDescriptionLength:
                                            newDescription.length,
                                        descriptionPreview:
                                            newDescription.substring(0, 100),
                                    },
                                )
                                setDescription(newDescription)
                            }}
                            autoResize
                        />
                    </Label>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <Label htmlFor="treatment" label="Treatment">
                            <Combobox
                                id="treatment"
                                options={treatmentOptions}
                                value={
                                    treatmentOptions.find(
                                        (option) => option.value === treatment,
                                    ) || null
                                }
                                onChange={(option: any) =>
                                    setTreatment(option?.value || 'none')
                                }
                                placeholder="Select Treatment"
                            />
                        </Label>

                        <Label
                            htmlFor="contributing-factor"
                            label="Contributing Factor">
                            <Combobox
                                id="contributing-factor"
                                options={contributingFactorOptions}
                                value={
                                    contributingFactorOptions.find(
                                        (option) =>
                                            option.value === contributingFactor,
                                    ) || null
                                }
                                onChange={(option: any) =>
                                    setContributingFactor(
                                        option?.value || 'none',
                                    )
                                }
                                placeholder="Select Contributing Factor"
                            />
                        </Label>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                        <CheckFieldLabel
                            id="risk-assessment"
                            isRadioStyle
                            variant="warning"
                            label="Risk Assessment Reviewed"
                            checked={riskAssessmentReviewed}
                            onCheckedChange={(checked) =>
                                setRiskAssessmentReviewed(checked === true)
                            }
                        />

                        <CheckFieldLabel
                            id="notifiable"
                            isRadioStyle
                            variant="warning"
                            label="Notifiable to Authorities"
                            checked={notifiable}
                            onCheckedChange={(checked) =>
                                setNotifiable(checked === true)
                            }
                        />
                    </div>

                    <Label
                        htmlFor="team-members"
                        label="Team Members to Notify">
                        <CrewMultiSelectDropdown
                            value={membersToNotify}
                            onChange={(selected: any) => {
                                try {
                                    const memberIds = selected.map(
                                        (item: any) => item.value,
                                    )
                                    setMembersToNotify(memberIds)
                                    console.log(
                                        'Team members to notify updated:',
                                        {
                                            timestamp: new Date().toISOString(),
                                            selectedCount: selected.length,
                                            memberIds,
                                        },
                                    )
                                } catch (selectionError) {
                                    console.error(
                                        'Error updating team members to notify:',
                                        {
                                            timestamp: new Date().toISOString(),
                                            selected,
                                            error: selectionError,
                                        },
                                    )
                                }
                            }}
                        />
                    </Label>

                    <Label htmlFor="attachments" label="Attachments">
                        <UploadCloudFlare
                            files={attachments}
                            setFiles={setAttachments}
                        />
                    </Label>

                    <div className="flex justify-end space-x-3">
                        <Button
                            variant="back"
                            iconLeft={ArrowLeft}
                            onClick={
                                activeTab == 'trip-log'
                                    ? closeModal
                                    : () => router.push('/incident-records')
                            }
                            disabled={
                                createLoading ||
                                updateLoading ||
                                deleteLoading ||
                                attachmentsLoading
                            }>
                            Cancel
                        </Button>
                        {recordId > 0 && (
                            <Button
                                variant="destructive"
                                onClick={() => setOpenDeleteConfirmation(true)}
                                disabled={
                                    createLoading ||
                                    updateLoading ||
                                    deleteLoading ||
                                    attachmentsLoading
                                }>
                                Delete
                            </Button>
                        )}
                        <Button
                            variant="primary"
                            onClick={() => handleSubmit()}
                            iconRight={Check}
                            disabled={
                                createLoading ||
                                updateLoading ||
                                deleteLoading ||
                                attachmentsLoading
                            }
                            isLoading={createLoading || updateLoading}>
                            {recordId > 0 ? 'Update' : 'Save'}
                        </Button>
                    </div>
                </div>
            </div>

            <AlertDialogNew
                openDialog={openDeleteConfirmation}
                setOpenDialog={setOpenDeleteConfirmation}
                title="Delete Incident Record"
                description="Are you sure you want to delete this incident record? This action cannot be undone."
                cancelText="Cancel"
                destructiveActionText="Delete"
                handleDestructiveAction={handleDelete}
                showDestructiveAction={true}
                variant="danger"
                destructiveLoading={deleteLoading}>
                {deleteLoading && (
                    <P className="text-blue-500 mt-2">
                        Deleting incident record...
                    </P>
                )}
            </AlertDialogNew>
        </>
    )
}

export default IncidentRecordForm
