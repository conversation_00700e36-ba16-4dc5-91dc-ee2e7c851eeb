import gql from 'graphql-tag'

export const ReadOneTripReportSchedule = gql`
    query ReadOneTripReportSchedule($id: ID!) {
        readOneTripReportSchedule(filter: { id: { eq: $id } }) {
            id
            archived
            title
            departTime
            departureBerth
            arriveTime
            arrivalBerth
            expectedContactTime
            start
            end
            transitRouteID
            transitTripID
            scheduleType
            fromLocationID
            fromLocation {
                id
                title
                lat
                long
            }
            toLocationID
            toLocation {
                id
                title
                lat
                long
            }
            tripScheduleServiceID
            tripScheduleService {
                id
                title
            }
            vehicles {
                nodes {
                    id
                    title
                }
            }
            tripReportScheduleStops {
                nodes {
                    id
                    title
                    arriveTime
                    departTime
                    tripReportScheduleID
                    stopLocationID
                    stopLocation {
                        id
                        title
                    }
                }
            }
            tripReport_LogBookEntrySections {
                nodes {
                    id
                }
            }
        }
    }
`
