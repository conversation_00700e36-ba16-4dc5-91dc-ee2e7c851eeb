'use client'

import { DELETE_KEY_CONTACTS } from '@/app/lib/graphQL/mutation'
import { AlertDialogNew, H2 } from '@/components/ui'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { toast } from '@/hooks/use-toast'
import { useMutation } from '@apollo/client'
import { useRouter } from 'next/navigation'
import React, { useState } from 'react'

export default function DeleteKeyContact({
    id,
    fullName,
}: {
    id: number
    fullName: string
}) {
    const router = useRouter()
    const [openConfirm, setOpenConfirm] = useState(false)

    const [mutationDelete] = useMutation(DELETE_KEY_CONTACTS, {
        onCompleted: (response: any) => {
            if (
                response.deleteKeyContacts &&
                response.deleteKeyContacts.length > 0
            ) {
                router.push('/key-contacts')
            } else {
                toast({
                    description: 'Error deleting key contact',
                    variant: 'destructive',
                })
            }
        },
        onError: (error: any) => {
            console.error('mutationDeleteKeyContacts error:', error.message)
        },
    })

    const handleDelete = () => {
        mutationDelete({
            variables: {
                ids: [id],
            },
        })
    }

    return (
        <>
            <SeaLogsButton
                text="Delete"
                type="secondary"
                color="rose"
                action={() => setOpenConfirm(true)}
            />
            <AlertDialogNew
                openDialog={openConfirm}
                setOpenDialog={setOpenConfirm}
                handleCreate={handleDelete}
                actionText="Delete Key Contact">
                <H2
                    slot="title"
                    className="text-2xl font-light leading-6 my-2 ">
                    Delete Key Contact
                </H2>
                <div className="my-4 flex items-center">
                    Are you sure you want to delete {fullName}?
                </div>
            </AlertDialogNew>
        </>
    )
}
