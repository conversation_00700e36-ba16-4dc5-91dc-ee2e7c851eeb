'use client'

import { useState, useEffect } from 'react'
import { Combobox } from '@/components/ui/comboBox'

interface TrainingStatusDropdownProps {
    value?: any
    onChange: (value: any) => void
    isClearable?: boolean
    placeholder?: string
}

const TrainingStatusDropdown = ({
    value,
    onChange,
    isClearable = false,
    placeholder = 'Training Status',
}: TrainingStatusDropdownProps) => {
    const [selectedValue, setSelectedValue] = useState(value)
    const [isLoading, setIsLoading] = useState(true)

    const statusOptions = [
        { value: 'Good', label: 'Good' },
        { value: 'Overdue', label: 'Overdue' },
        { value: 'Due Soon', label: 'Due Soon' },
    ]

    useEffect(() => {
        setIsLoading(false)
    }, [])

    useEffect(() => {
        setSelectedValue(value)
    }, [value])

    const handleChange = (selectedOption: any) => {
        setSelectedValue(selectedOption)
        onChange(selectedOption)
    }

    return (
        <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
            <div className="flex items-center">
                {!isLoading && (
                    <Combobox
                        options={statusOptions}
                        value={selectedValue}
                        onChange={handleChange}
                        title={placeholder}
                        placeholder={placeholder}
                    />
                )}
            </div>
        </div>
    )
}

export default TrainingStatusDropdown
