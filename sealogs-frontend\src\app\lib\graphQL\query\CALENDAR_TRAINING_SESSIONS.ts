import gql from 'graphql-tag'

export const CALENDAR_TRAINING_SESSIONS = gql`
    query ReadTrainingSessions(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TrainingSessionFilterFields = {}
    ) {
        readTrainingSessions(limit: $limit, offset: $offset, filter: $filter) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                date
                trainingSummary
                vessel {
                    id
                    title
                }
            }
        }
    }
`
