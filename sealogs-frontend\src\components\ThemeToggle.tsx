'use client'

import React from 'react'
import { <PERSON>, Sun, Monitor } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import {
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarMenu,
    SidebarMenuButton,
    SidebarMenuItem,
} from '@/components/ui/sidebar'
import { useTheme } from 'next-themes'

const SidebarTheme = () => {
    const { theme, setTheme } = useTheme()

    const getThemeIcon = () => {
        switch (theme) {
            case 'dark':
                return <Moon className="h-4 w-4 group-data-[collapsible=icon]:w-9 group-data-[collapsible=icon]:h-9" />
            case 'light':
                return <Sun className="h-4 w-4 group-data-[collapsible=icon]:w-9 group-data-[collapsible=icon]:h-9" />
            default:
                return <Monitor className="h-4 w-4 group-data-[collapsible=icon]:w-9 group-data-[collapsible=icon]:h-9" />
        }
    }

    const getThemeLabel = () => {
        switch (theme) {
            case 'dark':
                return 'Dark'
            case 'light':
                return 'Light'
            default:
                return 'System'
        }
    }

    return (
        <SidebarGroup>
            <SidebarGroupLabel>Appearance</SidebarGroupLabel>
            <SidebarGroupContent>
                <SidebarMenu>
                    <SidebarMenuItem>
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <SidebarMenuButton className="group-data-[collapsible=icon]:gap-0 group-data-[collapsible=icon]:!p-1">
                                    {getThemeIcon()}
                                    <span className="group-data-[collapsible=icon]:hidden">Theme: {getThemeLabel()}</span>
                                </SidebarMenuButton>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent
                                align="start"
                                className="w-[200px]">
                                <DropdownMenuItem
                                    onClick={() => setTheme('light')}>
                                    <Sun />
                                    <span>Light</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setTheme('dark')}>
                                    <Moon />
                                    <span>Dark</span>
                                </DropdownMenuItem>
                                <DropdownMenuItem
                                    onClick={() => setTheme('system')}>
                                    <Monitor />
                                    <span>System</span>
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    </SidebarMenuItem>
                </SidebarMenu>
            </SidebarGroupContent>
        </SidebarGroup>
    )
}

export default SidebarTheme
