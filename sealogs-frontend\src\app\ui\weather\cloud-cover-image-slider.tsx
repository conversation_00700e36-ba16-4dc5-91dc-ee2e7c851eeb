'use client'

import React, { useRef, useState } from 'react'

interface IProps {
    value: number
    onChange: (value: number) => void
    scale?: number // New prop to scale the component
}

const CloudCoverImageSlider = ({ value, onChange }: IProps) => {
    const svgRef = useRef<SVGSVGElement | null>(null)

    const viewBoxWidth = 126 // match this to your cloud's logical width
    const viewBoxHeight = 80

    const path =
        'M103.841,31.572a21.814,21.814,0,0,0-5.578.724,28.436,28.436,0,0,0-39.287-18.9A21.42,21.42,0,0,0,18.089,19.1a21.076,21.076,0,0,0,2.357,12.532A21.953,21.953,0,0,0,8.705,36.2,20.835,20.835,0,0,0,6.074,66.968a21.5,21.5,0,0,0,15.821,6.913h81.958a21.156,21.156,0,1,0-.012-42.309Z'

    const handleDrag = (clientX: number) => {
        if (!svgRef.current) return
        const bounds = svgRef.current.getBoundingClientRect()
        const mouseX = clientX - bounds.left
        const percent = Math.max(
            0,
            Math.min(100, (mouseX / bounds.width) * 100),
        )
        onChange(Number(percent.toFixed(0)))
    }

    const handleMouseDown = (e: React.MouseEvent<SVGSVGElement>) => {
        if (!svgRef.current) return

        handleDrag(e.clientX)

        const moveHandler = (e: MouseEvent) => handleDrag(e.clientX)
        const upHandler = () => {
            document.removeEventListener('mousemove', moveHandler)
            document.removeEventListener('mouseup', upHandler)
        }

        document.addEventListener('mousemove', moveHandler)
        document.addEventListener('mouseup', upHandler)
    }

    const handleTouchStart = (e: React.TouchEvent<SVGSVGElement>) => {
        if (e.touches.length > 0) {
            handleDrag(e.touches[0].clientX)

            const moveHandler = (e: TouchEvent) => {
                if (e.touches.length > 0) handleDrag(e.touches[0].clientX)
            }

            const endHandler = () => {
                document.removeEventListener('touchmove', moveHandler)
                document.removeEventListener('touchend', endHandler)
            }

            document.addEventListener('touchmove', moveHandler)
            document.addEventListener('touchend', endHandler)
        }
    }

    const fillWidth = Number(((value / 100) * viewBoxWidth).toFixed(0))

    const thumbX = fillWidth

    return (
        <div className="max-w-sm mx-auto">
            <svg
                ref={svgRef}
                viewBox={`0 0 ${viewBoxWidth} ${viewBoxHeight}`}
                width="100%"
                height="auto"
                className="w-full max-w-full h-auto cursor-pointer select-none"
                preserveAspectRatio="xMidYMid meet"
                onMouseDown={handleMouseDown}
                onTouchStart={handleTouchStart}>
                {/* Clip path for fill */}
                <clipPath id="cloudSliderClip">
                    <path d={path} />
                </clipPath>

                {/* Filled part */}
                <rect
                    x="0"
                    y="0"
                    width={fillWidth}
                    height={viewBoxHeight}
                    fill="#1669aa" // Tailwind blue-600
                    clipPath="url(#cloudSliderClip)"
                />

                {/* Cloud outline */}
                <path
                    d={path}
                    fill="none"
                    stroke="#1e3a8a" // Tailwind blue-900
                    strokeWidth="0.5"
                />

                {/* Thumb */}
                <circle
                    cx={thumbX}
                    cy="55"
                    r="6"
                    fill="white"
                    stroke="#333"
                    strokeWidth="0.5"
                />
                <text
                    x={thumbX}
                    y="57"
                    fontSize="8"
                    textAnchor="middle"
                    fill="#333">
                    ||
                </text>
            </svg>
        </div>
    )
}

export default CloudCoverImageSlider
