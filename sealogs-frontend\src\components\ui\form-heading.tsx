import { cn } from '@/app/lib/utils'

interface FormHeadingProps {
    /** The text (or content) for the heading. */
    children: React.ReactNode
    /**
     * The visual variant for the heading.
     * - `"default"` renders a standard heading (e.g. Vessel Details, Vessel configuration).
     * - `"large"` renders a larger heading (e.g. Crew, Department).
     */
    variant?: 'default' | 'large'
    /** Additional class names to apply */
    className?: string
}

export const FormHeading: React.FC<FormHeadingProps> = ({
    children,
    variant = 'default',
    className,
}) => {
    // Define default classes for the heading container based on the variant.
    const variantClasses = variant === 'large' ? '' : 'text-l' // default is text-l
    return (
        <div className={cn('mb-4 w-full', variantClasses, className)}>{children}</div>
    )
}

// ===== FormDescription ===== //
interface FormDescriptionProps {
    /** The description text */
    children: React.ReactNode
    /**
     * Variant determines the default padding and margin.
     *
     * Variants:
     * - `"default"`: `pl-2 mt-4` (for the first description paragraph)
     * - `"subsequent"`: `pl-2 mt-2` (for subsequent paragraphs in the same group)
     * - `"indent"`: `pl-4 mt-4` (for headings that are visually indented, e.g. Crew)
     * - `"indentSubsequent"`: `pl-4 mt-2` (for subsequent indented paragraphs)
     */
    variant?: 'default' | 'subsequent' | 'indent' | 'indentSubsequent'
    /** Additional class names to apply */
    className?: string
}

export const FormDescription: React.FC<FormDescriptionProps> = ({
    children,
    variant = 'default',
    className,
}) => {
    // Base classes for readability
    const baseClasses = ' max-w-[25rem] leading-loose '
    // Adjust spacing based on the variant
    let variantClasses: string
    switch (variant) {
        case 'subsequent':
            variantClasses = 'pl-2 mt-2'
            break
        case 'indent':
            variantClasses = 'pl-4 mt-4'
            break
        case 'indentSubsequent':
            variantClasses = 'pl-4 mt-2'
            break
        case 'default':
        default:
            variantClasses = 'pl-2 mt-4'
            break
    }
    return (
        <p className={cn(baseClasses, variantClasses, className)}>{children}</p>
    )
}
