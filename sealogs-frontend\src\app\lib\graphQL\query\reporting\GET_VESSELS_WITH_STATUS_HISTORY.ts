import gql from 'graphql-tag'

export const GET_VESSELS_WITH_STATUS_HISTORY = gql`
  query GET_VESSELS_WITH_STATUS_HISTORY($vesselFilter: VesselFilterFields = {}, $statusFilter: VesselStatusFilterFields = {}){
    readVessels(filter: $vesselFilter){
      nodes{
        id
        title
        archived
        statusHistory(
          filter: $statusFilter,
          sort:{
            date: ASC
          }
        ){
          nodes{
            id
            date
            status
            created
            comment
            reason
            otherReason
            expectedReturn
          }
        }
      }
    }
  }
`