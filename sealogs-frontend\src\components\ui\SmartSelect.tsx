'use client'

import * as React from 'react'
import { Check, ChevronsUpDown, PlusCircle, X } from 'lucide-react'

import { <PERSON><PERSON> } from '@/components/ui/button'
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandInput,
    CommandItem,
    CommandList,
} from '@/components/ui/command'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/app/lib/utils'

/**
 * SmartSelect Component
 *
 * A highly customizable selection component built using React that supports both single
 * and multi-select modes. It includes features such as filtering via a search input,
 * customizable placeholders, and handling of disabled items.
 *
 * ## Use Cases:
 *
 * - **Single Select:** Provide a single string or number value as the `value` prop.
 *   The component will display the selected item's label.
 *
 * - **Multi-Select:** Set the `multiple` prop to `true` and pass an array of values as
 *   the `value` prop. The component allows multiple items to be selected and displays
 *   each selected item as a badge. Selected badges can be removed individually.
 *
 * - **Searchable Dropdown:** When the number of items exceeds the `comboboxThreshold`,
 *   a search input is displayed to filter the list of items. The filtering checks
 *   both the item label and value.
 *
 * - **Customizable Texts:** Customize the placeholder text, empty message for when
 *   no items are found, and the search input placeholder by passing appropriate props.
 *
 * - **Disabled State:** The component supports a `disabled` prop to disable interaction.
 *
 * - **Disable Specific Items:** Use the `disableItem` prop to disable one or multiple
 *   options by providing an array of item values.
 *
 * - **Default Selection:** When used uncontrolled, the `defaultSelected` prop sets
 *   the initial selected item(s).
 *
 * ## Props:
 *
 * - `items`: Array of selection items (`{ label, value, disabled? }`) that are displayed
 *   as options.
 * - `placeholder`: Text to display when no item is selected (default: "Select item...").
 * - `emptyMessage`: Message to display when the search returns no items (default: "No items found.").
 * - `searchPlaceholder`: Placeholder text for the search input (default: "Search items...").
 * - `multiple`: Boolean flag to enable multi-select mode.
 * - `comboboxThreshold`: Determines if the search input should be shown based on the number of items (default: 10).
 * - `value`: Controlled value(s) for the selection, which can be a single string/number or an array of them.
 * - `defaultSelected`: Initial selection for uncontrolled usage.
 * - `onChange`: Callback function invoked when the selected value(s) change.
 * - `className`: Additional CSS classes for custom styling.
 * - `disabled`: Boolean flag to disable the component.
 * - `disableItem`: Array of item values (string or number) to disable in the options list.
 *
 * ## Example Usage:
 *
 * ```jsx
 * const items = [
 *   { label: 'Apple', value: 'apple' },
 *   { label: 'Banana', value: 'banana' },
 *   { label: 'Cherry', value: 'cherry' },
 * ];
 *
 * // Controlled Single Select Example with disabled items:
 * <SmartSelect
 *   items={items}
 *   value="apple"
 *   disableItem={['banana', 'cherry']}
 *   onChange={(value) => console.log(value)}
 * />
 *
 * // Uncontrolled Multi-Select Example with default selection:
 * <SmartSelect
 *   items={items}
 *   multiple
 *   defaultSelected={['apple', 'banana']}
 *   disableItem={['cherry']}
 *   onChange={(value) => console.log(value)}
 * />
 * ```
 */

export type SelectionItem = {
    label: string
    value: string | number
    disabled?: boolean
}

type SmartSelectProps = {
    items: SelectionItem[]
    placeholder?: string
    emptyMessage?: string
    searchPlaceholder?: string
    multiple?: boolean
    comboboxThreshold?: number
    value?: string | string[]
    defaultSelected?: string | string[]
    onChange: (value: string | string[]) => void
    className?: string
    disabled?: boolean
    disableItem?: (string | number)[]
}

export function SmartSelect({
    items,
    placeholder = 'Select item...',
    emptyMessage = 'No items found.',
    searchPlaceholder = 'Search items...',
    multiple = false,
    comboboxThreshold = 10,
    value,
    defaultSelected,
    onChange,
    className,
    disabled = false,
    disableItem,
}: SmartSelectProps) {
    // Determine if the component is controlled or uncontrolled.
    const isControlled = value !== undefined

    // For uncontrolled usage, initialize internal state with defaultSelected (or an empty value).
    const [internalValue, setInternalValue] = React.useState<string | string[]>(
        () =>
            defaultSelected !== undefined
                ? defaultSelected
                : multiple
                  ? []
                  : '',
    )

    // Use the effective value (controlled value takes precedence over internal state).
    const effectiveValue = isControlled ? value : internalValue

    const [open, setOpen] = React.useState(false)
    const [searchQuery, setSearchQuery] = React.useState('')

    // Convert the effective value to an array for consistent handling and filter out empty strings.
    const selectedValues = React.useMemo(() => {
        let values: string[] = []
        if (multiple) {
            values = Array.isArray(effectiveValue)
                ? effectiveValue
                : effectiveValue
                  ? [effectiveValue]
                  : []
        } else {
            values = effectiveValue ? [effectiveValue.toString()] : []
        }
        return values.filter((val) => val !== '')
    }, [effectiveValue, multiple])

    // Filter items based on search query.
    const filteredItems = React.useMemo(() => {
        if (!searchQuery) return items

        return items.filter(
            (item) =>
                item.label.toLowerCase().includes(searchQuery.toLowerCase()) ||
                item.value
                    .toString()
                    .toLowerCase()
                    .includes(searchQuery.toLowerCase()),
        )
    }, [items, searchQuery])

    // Get selected item labels for display.
    const selectedItemLabels = React.useMemo(() => {
        return items
            .filter((item) => selectedValues.includes(item.value.toString()))
            .map((item) => item)
    }, [items, selectedValues])

    // Handle selection of an item.
    const handleSelect = React.useCallback(
        (itemValue: string) => {
            if (multiple) {
                const newValues = selectedValues.includes(itemValue)
                    ? selectedValues.filter((i) => i !== itemValue)
                    : [...selectedValues, itemValue]
                if (!isControlled) setInternalValue(newValues)
                onChange(newValues)

                // Keep popover open for multi-select.
                setSearchQuery('')
            } else {
                if (!isControlled) setInternalValue(itemValue)
                onChange(itemValue)
                setOpen(false)
                setSearchQuery('')
            }
        },
        [multiple, onChange, selectedValues, isControlled],
    )

    // Remove a selected item (for multi-select).
    const removeItem = React.useCallback(
        (itemValue: string, e?: React.MouseEvent) => {
            e?.preventDefault()
            e?.stopPropagation()

            if (multiple) {
                const newValues = selectedValues.filter((i) => i !== itemValue)
                if (!isControlled) setInternalValue(newValues)
                onChange(newValues)
            }
        },
        [multiple, onChange, selectedValues, isControlled],
    )

    // Show combobox if items exceed threshold.
    const showCombobox = items.length > comboboxThreshold

    return (
        <Popover open={open} onOpenChange={setOpen}>
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className={cn(
                        'w-full h-fit border-dashed justify-between',
                        className,
                    )}
                    disabled={disabled}>
                    {multiple && <PlusCircle />}
                    <div className="flex-1 flex flex-wrap gap-1 items-start">
                        {selectedValues.length > 0 ? (
                            multiple ? (
                                <div className="flex flex-wrap gap-1.5 w-full max-w-[90%] overflow-hidden py-0.5">
                                    {selectedItemLabels.map((item) => (
                                        <Badge
                                            key={item.value.toString()}
                                            className="w-fit text-xs h-6 border-none bg-secondary/40">
                                            {item.label}
                                            <div
                                                role="button"
                                                tabIndex={-1}
                                                className="ml-1 rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
                                                onMouseDown={(e) => {
                                                    e.preventDefault()
                                                    e.stopPropagation()
                                                }}
                                                onClick={(e) =>
                                                    removeItem(
                                                        item.value.toString(),
                                                        e,
                                                    )
                                                }>
                                                <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                                                <span className="sr-only">
                                                    Remove {item.label}
                                                </span>
                                            </div>
                                        </Badge>
                                    ))}
                                </div>
                            ) : (
                                <span className="truncate text-start flex-1 max-w-[90%]">
                                    {selectedItemLabels[0]?.label}
                                </span>
                            )
                        ) : (
                            <span className="text-start text-secondary flex-1">
                                {placeholder}
                            </span>
                        )}
                    </div>
                    <ChevronsUpDown className="size-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full min-w-[var(--radix-popover-trigger-width)] p-0">
                <Command shouldFilter={!showCombobox}>
                    {showCombobox && (
                        <CommandInput
                            placeholder={searchPlaceholder}
                            value={searchQuery}
                            onValueChange={setSearchQuery}
                            className="h-9"
                        />
                    )}
                    <CommandList>
                        <CommandEmpty>{emptyMessage}</CommandEmpty>
                        <CommandGroup className="max-h-[300px] overflow-auto">
                            {filteredItems.map((item) => {
                                // Determine if the current item should be disabled.
                                const isItemDisabled =
                                    item.disabled ||
                                    disableItem?.some(
                                        (val) =>
                                            val.toString() ===
                                            item.value.toString(),
                                    )
                                return (
                                    <CommandItem
                                        key={item.value.toString()}
                                        value={item.value.toString()}
                                        onSelect={handleSelect}
                                        disabled={isItemDisabled}
                                        className={cn(
                                            'flex items-center gap-2',
                                            isItemDisabled &&
                                                'opacity-50 cursor-not-allowed',
                                        )}>
                                        <div
                                            className={cn(
                                                'flex h-4 w-4 items-center justify-center rounded-sm border',
                                                selectedValues.includes(
                                                    item.value.toString(),
                                                )
                                                    ? 'bg-primary text-foreground border-primary'
                                                    : 'border-primary/50 opacity-50',
                                            )}>
                                            {selectedValues.includes(
                                                item.value.toString(),
                                            ) && <Check className="h-3 w-3" />}
                                        </div>
                                        <span>{item.label}</span>
                                    </CommandItem>
                                )
                            })}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}
