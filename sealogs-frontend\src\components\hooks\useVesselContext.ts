// src/app/hooks/useVesselContext.ts
import { usePathname, useSearchParams } from 'next/navigation'
import { useVesselName } from '@/app/hooks/use-vessel-name'

export function useVesselContext() {
  const pathname = usePathname()
  const searchParams = useSearchParams()

  let vesselID = 0
  let vesselNameParam = searchParams.get('name') || ''

  if (searchParams.get('vesselID')) {
    vesselID = +searchParams.get('vesselID')!
  } else if (searchParams.get('id') && pathname.includes('/vessel/')) {
    vesselID = +searchParams.get('id')!
  }

  if (pathname.startsWith('/maintenance') && !vesselID) {
    const redirectTo = searchParams.get('redirect_to')
    if (redirectTo) {
      const params = new URLSearchParams(
        redirectTo.split('?')[1]?.split('%26')[0] || ''
      )
      if (params.get('id')) {
        vesselID = +params.get('id')!
      }
      if (params.get('name')) {
        vesselNameParam = params.get('name')!
      }
    }
  }

  const { vesselName } = useVesselName(vesselID)

  return { vesselID, vesselNameParam, vesselName, pathname }
}
