import gql from 'graphql-tag'

export const GET_VESSELS_WITH_LATEST_STATUS = gql`
  query GET_VESSELS_WITH_LATEST_STATUS($vesselFilter: VesselFilterFields = {}, $statusFilter: VesselStatusFilterFields = {}){
    readVessels(filter: $vesselFilter){
      nodes{
        id
        title
        archived
        statusHistory(
          filter: $statusFilter,
          sort:{
            date: DESC
            created: DESC
          },
          limit: 1
        ){
          nodes{
            id
            date
            status
            created
            comment
            reason
            otherReason
            expectedReturn
          }
        }
      }
    }
  }
`