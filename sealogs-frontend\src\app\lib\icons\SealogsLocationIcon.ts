const React = require('react')

export function SealogsLocationIcon({ ...props }) {
  return /*#__PURE__*/ React.createElement(
    'svg',
    Object.assign(
      {
        className: 'w-7 h-7 sm:size-9',
        viewBox: '0 0 78 91.1323',
        stroke: '#022450',
        fill: '#ffffff',
        strokeMiterlimit: '10',
        strokeWidth: '.75px',
        'aria-hidden': 'true',
        'data-slot': 'icon',
      },
      props,
    ),
    /*#__PURE__*/ React.createElement('path', {
      d: 'M16.3623,67.2474c-.3242-.4395-.9844-.6133-1.438-.6133h-.8486c-7.2979,0-13.2354-5.9375-13.2354-13.2354V13.7357C.8403,6.4373,6.7778.4998,14.0757.4998h49.772c7.2979,0,13.2354,5.9375,13.2354,13.2358v39.6631c0,7.2979-5.9375,13.2354-13.2354,13.2354h-.8496c-.5312,0-1.041.2168-1.4336.6094l-22.5649,23.1729-22.6372-23.1689Z',
      fill: '#f4fafe',
      strokeWidth: '0px',
    }),
    /*#__PURE__*/ React.createElement('path', {
      d: 'M63.8472,1c7.0223,0,12.7354,5.7131,12.7354,12.7354v39.663c0,7.0223-5.7131,12.7354-12.7354,12.7354h-.8488c-.6652,0-1.3.2684-1.7968.7654l-22.2029,22.8009-22.242-22.7644c-.4819-.5897-1.2558-.8019-1.8324-.8019h-.8488c-7.0223,0-12.7354-5.7131-12.7354-12.7354V13.7354C1.3401,6.7131,7.0532,1,14.0756,1h49.7717M63.8472,0H14.0756C6.5134,0,.3401,6.1732,.3401,13.7354v39.663c0,7.5622,6.1732,13.7354,13.7354,13.7354h.8488c.3858,0,.8488.1543,1.0803.463l22.9953,23.5354,22.9181-23.5354c.3087-.3087.6945-.463,1.0803-.463h.8488c7.5622,0,13.7354-6.1732,13.7354-13.7354V13.7354c0-7.5622-6.1732-13.7354-13.7354-13.7354h0Z',
      fill: '#022450',
      strokeWidth: '0.5px',
    }),
    /*#__PURE__*/ React.createElement('path', {
      d: 'M30.5667,71.4197c.0454.0732.126.1182.2124.1182h16.8809c.0864,0,.167-.0449.2124-.1182l19.064-30.7402c.0405-.0649.0488-.1445.0229-.2163-.0259-.0723-.0835-.1279-.1558-.1523l-7.9189-2.6618v-15.9271c0-.1382-.1118-.25-.25-.25h-7.0308v-13.0513c0-.1382-.1118-.25-.25-.25h-24.2686c-.1382,0-.25.1118-.25.25v13.0513h-7.0308c-.1382,0-.25.1118-.25.25v15.9271l-7.9189,2.6618c-.0723.0244-.1299.0801-.1558.1523-.0259.0718-.0176.1514.0229.2163l19.064,30.7402ZM66.345,40.6839l-18.8242,30.354h-8.0513V31.6502l26.8755,9.0336ZM27.3352,8.6707h23.7686v12.8013h-23.7686v-12.8013ZM20.0545,21.9719h38.3301v15.509l-19.0854-6.4152c-.0155-.0052-.0317.0021-.0477,0-.012-.0018-.0194-.0132-.0319-.0132s-.0199.0114-.0319.0132c-.016.0021-.0322-.0052-.0477,0l-19.0854,6.4152v-15.509ZM38.9695,31.6502v39.3876h-8.0513l-18.8242-30.354,26.8755-9.0336Z',
    }),
    /*#__PURE__*/ React.createElement('polygon', {
      points: '66.1255 40.6839 47.3013 71.0379 39.25 71.0379 39.25 31.6502 66.1255 40.6839',
    }),
    /*#__PURE__*/ React.createElement('polygon', {
      points: '38.75 31.6502 38.75 71.0379 30.6987 71.0379 11.8745 40.6839 38.75 31.6502',
    }),
    /*#__PURE__*/ React.createElement('path', {
      d: 'M19.835,21.9719h38.3301v15.509l-19.0854-6.4152c-.0155-.0052-.0317.0021-.0477,0-.012-.0018-.0194-.0132-.0319-.0132s-.0199.0114-.0319.0132c-.016.0021-.0322-.0052-.0477,0l-19.0854,6.4152v-15.509Z',
    }),
    /*#__PURE__*/ React.createElement('path', {
      d: 'M44.2812,14.8215h-3.0493c-.1382,0-.25.1118-.25.25s.1118.25.25.25h3.0493c.1382,0,.25-.1118.25-.25s-.1118-.25-.25-.25Z',
      fill: '#022450',
    }),
    /*#__PURE__*/ React.createElement('path', {
      d: 'M36.7681,14.8215h-3.0493c-.1382,0-.25.1118-.25.25s.1118.25.25.25h3.0493c.1382,0,.25-.1118.25-.25s-.1118-.25-.25-.25Z',
      fill: '#022450',
    }),
  );
}
