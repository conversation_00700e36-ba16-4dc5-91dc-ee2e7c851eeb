import gql from 'graphql-tag'

export const CREW_DETAIL_WITH_TRAINING_STATUS = gql`
    query GetCrewDetailWithTrainingStatus($crewMemberID: ID!) {
        readOneSeaLogsMember(filter: { id: { eq: $crewMemberID } }) {
            id
            archived
            isArchived
            firstName
            isCrew
            departments {
                nodes {
                    id
                }
            }
            primaryDutyID
            primaryDuty {
                id
                title
            }
            surname
            vehicles {
                nodes {
                    id
                    title
                }
            }
            trainingSessionsDue {
                nodes {
                    id
                    dueDate
                    memberID
                    member {
                        id
                        firstName
                        surname
                    }
                    vesselID
                    vessel {
                        id
                        title
                    }
                    trainingTypeID
                    trainingType {
                        id
                        title
                    }
                }
            }
            trainingSessions {
                nodes {
                    id
                    members {
                        nodes {
                            id
                            firstName
                            surname
                        }
                    }
                }
            }
        }
    }
`
