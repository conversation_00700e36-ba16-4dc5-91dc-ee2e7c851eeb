'use client'

import { Dispatch, useMemo } from 'react'
import { useEngineFields } from '../../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../../actions'
import PostEngineG<PERSON>Fields from './post-engine-group-fields'
import PostEngineStrainersFields from './post-engine-strainers-fields'
import {
    Check<PERSON>ield,
    CheckFieldContent,
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'
import { Card } from '@/components/ui'

interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    updateVesselDailyCheck_LogBookEntrySection: Function
}

function PostEngineFields({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    updateVesselDailyCheck_LogBookEntrySection,
}: IProps) {
    const { postEngineFields, postEngineStrainersFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const shouldDisplay = useMemo(() => {
        return (
            getFilteredFields(postEngineFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ).length > 0 ||
            getFilteredFields(
                postEngineStrainersFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ).length > 0 ||
            displayField('ForwardReverse', logBookConfig)
        )
    }, [logBookConfig, postEngineFields, postEngineStrainersFields])

    const filteredPostEngineFields = useMemo(
        () =>
            getFilteredFields(postEngineFields, true, logBookConfig)?.filter(
                (groupField: any) =>
                    displayField(groupField.name, logBookConfig),
            ) ?? [],
        [postEngineFields, logBookConfig],
    )

    const filteredPostEngineStrainersFields = useMemo(
        () =>
            getFilteredFields(
                postEngineStrainersFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ) ?? [],
        [postEngineStrainersFields, logBookConfig],
    )

    if (!shouldDisplay) {
        return <></>
    }

    return (
        <>
            <CheckField>
                {/* <CheckFieldTopContent /> */}
                <CheckFieldContent>
                    {logBookConfig && vesselDailyCheck && (
                        <>
                            {filteredPostEngineFields.map((groupField: any) => (
                                <PostEngineGroupFields
                                    key={groupField.name}
                                    groupField={groupField}
                                    edit_logBookEntry={edit_logBookEntry}
                                    getComment={getComment}
                                    handleEngineChecks={handleEngineChecks}
                                    locked={locked}
                                    logBookConfig={logBookConfig}
                                    setDescriptionPanelContent={
                                        setDescriptionPanelContent
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    showCommentPopup={showCommentPopup}
                                />
                            ))}
                            {filteredPostEngineStrainersFields.map(
                                (groupField: any) => (
                                    <PostEngineStrainersFields
                                        key={groupField.name}
                                        groupField={groupField}
                                        edit_logBookEntry={edit_logBookEntry}
                                        getComment={getComment}
                                        handleEngineChecks={handleEngineChecks}
                                        locked={locked}
                                        logBookConfig={logBookConfig}
                                        setDescriptionPanelContent={
                                            setDescriptionPanelContent
                                        }
                                        setDescriptionPanelHeading={
                                            setDescriptionPanelHeading
                                        }
                                        setOpenDescriptionPanel={
                                            setOpenDescriptionPanel
                                        }
                                        showCommentPopup={showCommentPopup}
                                    />
                                ),
                            )}
                            <DailyCheckField
                                locked={locked || !edit_logBookEntry}
                                displayField={displayField(
                                    'ForwardReverse',
                                    logBookConfig,
                                )}
                                displayDescription={displayDescription(
                                    'ForwardReverse',
                                    logBookConfig,
                                )}
                                setOpenDescriptionPanel={
                                    setOpenDescriptionPanel
                                }
                                setDescriptionPanelHeading={
                                    setDescriptionPanelHeading
                                }
                                displayLabel={getFieldLabel(
                                    'ForwardReverse',
                                    logBookConfig,
                                )}
                                inputId={'forwardReverse'}
                                handleNoChange={() =>
                                    handleEngineChecks(false, 'forwardReverse')
                                }
                                defaultNoChecked={
                                    vesselDailyCheck?.forwardReverse ===
                                    'Not_Ok'
                                }
                                handleYesChange={() =>
                                    handleEngineChecks(true, 'forwardReverse')
                                }
                                defaultYesChecked={
                                    vesselDailyCheck?.forwardReverse === 'Ok'
                                }
                                commentAction={() =>
                                    showCommentPopup(
                                        getComment('ForwardReverse'),
                                        composeField(
                                            'ForwardReverse',
                                            logBookConfig,
                                        ),
                                    )
                                }
                                comment={getComment('ForwardReverse')?.comment}
                            />
                        </>
                    )}
                </CheckFieldContent>
            </CheckField>
        </>
    )
}

export default PostEngineFields
