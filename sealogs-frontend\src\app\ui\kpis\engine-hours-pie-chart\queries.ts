import gql from 'graphql-tag'

export const ReadComponentMaintenanceChecks = gql`
    query ReadComponentMaintenanceChecks(
        $limit: Int = 500
        $offset: Int = 0
        $filter: ComponentMaintenanceCheckFilterFields = {}
    ) {
        readComponentMaintenanceChecks(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                archived
                startDate
                dateCompleted
                completed
                expires
                status
                maintenanceSchedule {
                    occursEveryType
                    occursEvery
                    engineUsage {
                        nodes {
                            isScheduled
                            lastScheduleHours
                            engine {
                                currentHours
                            }
                        }
                    }
                }
            }
        }
    }
`
