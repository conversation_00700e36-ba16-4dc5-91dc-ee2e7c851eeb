'use client'
import { getV<PERSON>elList } from '@/app/lib/actions'
import { useEffect, useState } from 'react'
import UploadCloudFlare from '../logbook/components/upload-cf'
import { isEmpty, trim } from 'lodash'
import { useRouter, useSearchParams } from 'next/navigation'
import { useLazyQuery, useMutation } from '@apollo/client'
import {
    CREATE_R2FILE,
    CreateTripScheduleImport,
    DeleteTripScheduleImports,
    UpdateTripScheduleImport,
} from '@/app/lib/graphQL/mutation'
import { ReadOneTripScheduleImport } from '@/app/lib/graphQL/query'
import { H3, H4, P } from '@/components/ui/typography'

import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'
import { Save, X } from 'lucide-react'
import { Combobox, Option } from '@/components/ui/comboBox'
import { Separator } from '@/components/ui/separator'
import { Button } from '@/components/ui'
const ImportTripSchedule = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const id = searchParams.get('id') ?? 0
    const { toast } = useToast()
    const scheduleTypes = [
        {
            value: 'WeekDays',
            label: 'Days of the Week',
        },
        // {
        //     value: 'MonthDays',
        //     label: 'Days of the Month',
        // },
        // {
        //     value: 'EveryDay',
        //     label: 'Every Day',
        // },
    ]
    const [importData, setImportData] = useState<any>({
        id: id,
        title: '',
        scheduleType: scheduleTypes[0].value,
        // hasHeaderRow: false,
    })
    const [selectedScheduleType, setSelectedScheduleType] = useState<any>(
        scheduleTypes[0],
    )
    const [vessels, setVessels] = useState<any>([])
    const [selectedVessels, setSelectedVessels] = useState<any>([])
    const [importFile, setImportFile] = useState<any>([])
    // const [hasHeaderRow, setHasHeaderRow] = useState(false)
    const [archiveTripReportSchedule, setArchiveTripReportSchedule] =
        useState(false)
    const [archiveTripScheduleService, setArchiveTripScheduleService] =
        useState(false)
    const [archiveLocation, setArchiveLocation] = useState(false)
    const [openConfirmDeleteDialog, setOpenConfirmDeleteDialog] =
        useState(false)

    const confirmDeleteImport = () => {
        setOpenConfirmDeleteDialog(true)
    }

    const [
        deleteTripScheduleImport,
        { loading: deleteTripScheduleImportLoading },
    ] = useMutation(DeleteTripScheduleImports, {
        onCompleted: (response: any) => {
            if (response.deleteTripScheduleImports) {
                setOpenConfirmDeleteDialog(false) // Close the dialog first
                toast({
                    title: 'Success',
                    description: 'Trip Schedule Import deleted successfully',
                })
                router.push('/trip-schedules')
            } else {
                setOpenConfirmDeleteDialog(false) // Close the dialog first
                toast({
                    variant: 'destructive',
                    title: 'Error',
                    description: 'Error deleting Trip Schedule Import',
                })
            }
        },
        onError: (error: any) => {
            setOpenConfirmDeleteDialog(false) // Close the dialog first
            toast({
                variant: 'destructive',
                title: 'Error',
                description: `Error: ${error.message}`,
            })
            console.error('deleteTripScheduleImport onError', error.message)
        },
    })

    const handleDeleteImport = async () => {
        await deleteTripScheduleImport({
            variables: {
                ids: [id],
            },
        })
    }

    const handleOnChangeTitle = (e: any) => {
        setImportData({ ...importData, title: e.target.value })
    }
    const handleOnChangeScheduleType = (option: Option | Option[] | null) => {
        if (option && !Array.isArray(option)) {
            setSelectedScheduleType(option)
            setImportData({ ...importData, scheduleType: option.value })
        }
    }
    const handleVesselChange = (options: Option | Option[] | null) => {
        if (options && Array.isArray(options)) {
            setSelectedVessels(options)
            setImportData({
                ...importData,
                vehicles: options.map((v) => v.value).join(','),
            })
        }
    }
    const handleSetVessels = (e: any) => {
        const vesselList = e.map((vessel: any) => ({
            label: vessel.title,
            value: vessel.id,
        }))
        setVessels(vesselList)
    }
    const validateInputs = () => {
        let errorMessage = ''
        if (isEmpty(trim(importData.title))) {
            errorMessage += '\nThe title is required.'
        }
        if (isEmpty(selectedVessels)) {
            errorMessage += '\nAt least one vessel is required.'
        }
        if (isEmpty(trim(importData.R2FileID))) {
            errorMessage += '\nPlease select a CSV file to import.'
        }
        if (!isEmpty(trim(errorMessage))) {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description: trim(errorMessage),
            })
            return false
        } else {
            return true
        }
    }

    const [
        createTripScheduleImport,
        { loading: createTripScheduleImportLoading },
    ] = useMutation(CreateTripScheduleImport, {
        onCompleted: () => {
            router.push('/trip-schedules')
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
            console.error('createTripScheduleImport onError', error.message)
        },
    })
    const [
        updateTripScheduleImport,
        { loading: updateTripScheduleImportLoading },
    ] = useMutation(UpdateTripScheduleImport, {
        onCompleted: () => {
            router.push('/trip-schedules')
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
            console.error('updateTripScheduleImport onError', error.message)
        },
    })

    const saveImport = async () => {
        // Validate and save the import
        const validated = validateInputs()
        if (!validated) return
        if (+id === 0) {
            // create
            await createTripScheduleImport({
                variables: { input: importData },
            })
        } else {
            if (archiveTripReportSchedule) {
                importData.archiveTripReportSchedule = archiveTripReportSchedule
            }
            if (archiveTripScheduleService) {
                importData.archiveTripScheduleService =
                    archiveTripScheduleService
            }
            if (archiveLocation) {
                importData.archiveLocation = archiveLocation
            }
            // update
            await updateTripScheduleImport({
                variables: { input: importData },
            })
        }
    }
    const [readOneTripScheduleImport] = useLazyQuery(
        ReadOneTripScheduleImport,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readOneTripScheduleImport
                if (data) {
                    const vehicles = data.vehicles.nodes.map((v: any) => v.id)
                    setImportData({
                        id: data.id,
                        title: data.title,
                        scheduleType: data.scheduleType,
                        R2FileID: data.R2FileID,
                        vehicles: vehicles.join(','),
                        // hasHeaderRow: data.hasHeaderRow,
                    })
                    setImportFile([data.R2File])
                }
            },
            onError: (error: any) => {
                console.error('readOneTripScheduleImport error', error)
            },
        },
    )
    getVesselList(handleSetVessels)
    const loadTripScheduleImport = async () => {
        await readOneTripScheduleImport({ variables: { id: id } })
    }
    const [createR2File, { loading: createR2FileLoading }] = useMutation(
        CREATE_R2FILE,
        {
            onCompleted: (response) => {
                const data = response.createR2File
                const newFiles = importFile.map((file: any) => {
                    if (file.title === data.title) {
                        return {
                            ...file,
                            id: data.id,
                        }
                    }
                    return file
                })
                setImportFile(newFiles)
                setImportData({ ...importData, R2FileID: newFiles[0].id })
            },
            onError: (error) => {
                console.error('Error creating R2 file', error)
            },
        },
    )

    // const handleSetHasHeaderRow = (e: any) => {
    //     setHasHeaderRow(e.target.checked)
    //     setImportData({ ...importData, hasHeaderRow: e.target.checked })
    // }
    useEffect(() => {
        if (importFile.length > 0) {
            importFile.map((file: any) => {
                if (!file.id) {
                    createR2File({
                        variables: {
                            input: {
                                title: file.title,
                            },
                        },
                    })
                }
            })
            // const file = importFile[0] // it should only accept a single file
            // if (!file.id) {
            //     createR2File({
            //         variables: {
            //             input: {
            //                 title: file.title,
            //             },
            //         },
            //     })
            // }
        }
    }, [importFile])
    useEffect(() => {
        if (+id !== 0) {
            loadTripScheduleImport()
        }
    }, [id])
    useEffect(() => {
        if (!isEmpty(vessels) && !isEmpty(importData.vehicles)) {
            const sv = importData.vehicles
                .split(',')
                .map((v: any) =>
                    vessels.find((vessel: any) => +vessel.value === +v),
                )
            setSelectedVessels(sv)
        }
    }, [vessels, importData])

    return (
        <div className="w-full">
            <div className="space-y-6">
                <div className="flex justify-between items-center">
                    <H3>{+id > 0 ? 'Edit' : 'New'} Trip Schedule Import</H3>
                </div>
                <Separator />
                <div className="space-y-2">
                    <Label htmlFor="title">Title</Label>
                    <Input
                        id="title"
                        defaultValue={importData.title}
                        onChange={handleOnChangeTitle}
                        type="text"
                        placeholder="Import Title"
                    />
                </div>
                <div className="space-y-2">
                    <Combobox
                        id="schedule-type"
                        options={scheduleTypes.map((option: any) => ({
                            ...option,
                            value: String(option.value),
                        }))}
                        value={selectedScheduleType}
                        onChange={handleOnChangeScheduleType}
                        placeholder="Schedule Type"
                        label="Schedule Type"
                    />
                </div>
                <div className="space-y-2">
                    <Combobox
                        id="vessel-list"
                        options={vessels.map((option: any) => ({
                            ...option,
                            value: String(option.value),
                        }))}
                        value={selectedVessels}
                        onChange={handleVesselChange}
                        placeholder="Select Vessels"
                        label="Vessels"
                        multi={true}
                    />
                </div>
                <div className="space-y-2">
                    <Label htmlFor="import-file">Import File</Label>
                    <UploadCloudFlare
                        files={importFile}
                        setFiles={setImportFile}
                        multipleUpload={false}
                    />
                </div>
                {/* <div className={`flex items-center mt-6 mb-4 w-full`}>
                    <Label
                        className={`relative flex items-center pr-3 w-full rounded-full cursor-pointer`}
                        htmlFor="client-use-department"
                        data-ripple="true"
                        data-ripple-color="dark"
                        data-ripple-dark="true">
                        <Input
                            type="checkbox"
                            id="client-use-department"
                            className="before:content[''] peer relative h-5 w-5 cursor-pointer p-3 appearance-none rounded-full border border-sky-400 transition-all before:absolute before:top-2/4 before:left-2/4 before:block before:h-12 before:w-12 before:-translate-y-2/4 before:-translate-x-2/4 before:rounded-full before:bg-blue-sky-500 before:opacity-0 before:transition-opacity checked:border-sky-700 checked:bg-sky-700 before:bg-sky-700 hover:before:opacity-10"
                            checked={hasHeaderRow}
                            onChange={(e: any) => {
                                handleSetHasHeaderRow(e)
                            }}
                        />
                        <span className="absolute text-white transition-opacity opacity-0 pointer-events-none top-2/4 left-1/3 -translate-y-2/4 -translate-x-2/4 peer-checked:opacity-100"></span>
                        <span className="ml-3 text-sm font-semibold uppercase">
                            Ignore Header Row
                        </span>
                    </Label>
                </div> */}
                <Separator className="my-6" />
                <div className="space-y-4">
                    <H4>Import File Guidelines</H4>
                    <ul className="list-disc pl-6 space-y-3">
                        <li>Must be a comma separated (.csv) file.</li>
                        <li>
                            <P className="mb-2">
                                Depending on Schedule Type selected above the
                                column format should be:
                            </P>
                            <ul className="list-disc pl-8 space-y-4">
                                <li>
                                    <p className="font-medium mb-1">
                                        Every Day:
                                    </p>
                                    <div className="bg-muted p-2 rounded">
                                        <code className="text-xs">
                                            "Service Name","Transit Route
                                            ID","Transit Trip ID","Depart
                                            Location","Depart
                                            Time","Destination","Arrival
                                            Time","Start Date","End Date","Stop1
                                            Name","Stop1 ArriveTime","Stop1
                                            DepartTime","Stop1 PickUp","Stop1
                                            DropOff","Stop2 Name","Stop2
                                            ArriveTime","Stop2
                                            DepartTime","Stop2 PickUp","Stop2
                                            DropOff"
                                        </code>
                                    </div>
                                </li>
                                <li>
                                    <p className="font-medium mb-1">
                                        Days of the Week:
                                    </p>
                                    <div className="bg-muted p-2 rounded">
                                        <code className="text-xs">
                                            "Service Name","Transit Route
                                            ID","Transit Trip ID","Depart
                                            Location","Depart
                                            Time","Destination","Arrival
                                            Time","Start Date","End
                                            Date","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday","Sunday","Public
                                            Holidays","Stop1 Name","Stop1
                                            ArriveTime","Stop1
                                            DepartTime","Stop1 PickUp","Stop1
                                            DropOff","Stop2 Name","Stop2
                                            ArriveTime","Stop2
                                            DepartTime","Stop2 PickUp","Stop2
                                            DropOff"
                                        </code>
                                    </div>
                                </li>
                            </ul>
                        </li>
                        <li>
                            Service Name must be included but if there is no
                            Service Name set this to 0.
                        </li>
                        <li>
                            Every column up to the Stops columns is required,
                            the Days of Week/Month should be set to 1 for on and
                            0 for off.
                        </li>
                        <li>
                            The "Start Date" and "End Date" should be in the
                            format YYYY-MM-DD or in the format DD/MM/YYYY.
                        </li>
                        <li>
                            Stops are optional. If including Stops, they must
                            come in column sets of five as demonstrated above
                            even if some of the columns are empty but the Stop
                            Name is required to create a Stop.
                        </li>
                        <li>
                            All rows must have the same number of columns (this
                            will happen automatically when exporting from a
                            spreadsheet).
                        </li>
                        <li>
                            Column values do not need to be enclosed in quotes.
                        </li>
                        <li>
                            Remove any empty rows from the bottom of the
                            spreadsheet before exporting.
                        </li>
                    </ul>
                </div>
                <Separator className="my-6" />
                <div className="flex justify-end gap-2">
                    <Button
                        variant="text"
                        iconLeft={X}
                        onClick={() => router.push('/trip-schedules')}>
                        Cancel
                    </Button>
                    <Button
                        iconLeft={Save}
                        isLoading={
                            createTripScheduleImportLoading ||
                            updateTripScheduleImportLoading ||
                            deleteTripScheduleImportLoading ||
                            createR2FileLoading
                        }
                        onClick={saveImport}>
                        {+id > 0 ? 'Update' : 'Save'} Import
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default ImportTripSchedule
