import gql from 'graphql-tag'

export const GET_FUELLOGS_BY_LBE = gql`
    query GetFuelLogs($filter: FuelLogFilterFields = {}) {
        readFuelLogs(filter: $filter) {
            nodes {
                id
                fuelAdded
                fuelBefore
                fuelAfter
                date
                type
                typeID
                fuelTank {
                    id
                    capacity
                    safeFuelCapacity
                    currentLevel
                    title
                }
            }
        }
    }
`
