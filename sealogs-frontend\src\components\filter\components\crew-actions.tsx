'use client'

import * as React from 'react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import { useState } from 'react'
import Link from 'next/link'
import { SealogsCogIcon } from '@/app/lib/icons'

interface CrewDutyDropdownProps {
    onChange: (value: any) => void
}

export const CrewFilterActions = ({ onChange }: CrewDutyDropdownProps) => {
    const { isMobile } = useSidebar()
    const [showActiveUsers, setShowActiveUsers] = useState(true)

    const handleOnChange = () => {
        setShowActiveUsers((prev) => {
            const newValue = !prev
            onChange(newValue)
            return newValue
        })
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <DropdownMenuItem onClick={() => handleOnChange()}>
                    {showActiveUsers ? 'Archived Crews' : 'Active Crews'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <Link href={'/user/create'}>
                    <DropdownMenuItem>Add Crew</DropdownMenuItem>
                </Link>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
