'use client'

import * as React from 'react'
import { format, isAfter, isBefore, isValid, parse } from 'date-fns'
import { CalendarIcon, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { cn } from '@/app/lib/utils'
import { Calendar } from './calendar'
import { DateRange } from 'react-day-picker'

export type DateFormat =
    | 'MM/dd/yyyy'
    | 'dd/MM/yyyy'
    | 'yyyy-MM-dd'
    | 'MMM dd, yyyy'
    | string

export type DateValidation = {
    minDate?: Date
    maxDate?: Date
    disabledDates?: Date[]
    disabledDaysOfWeek?: number[] // 0 = Sunday, 6 = Saturday
}

export type SmartDatePickerProps = {
    /** Selected date or date range */
    value?: Date | Date[] | null
    /** Callback when date(s) change */
    onChange: (date: Date | Date[] | null) => void
    /** Enable date range selection */
    mode?: 'single' | 'range' | 'multiple' | 'default'
    /** Format to display the date(s) */
    dateFormat?: DateFormat
    /** Placeholder text when no date is selected */
    placeholder?: string
    /** Date validation rules */
    validation?: DateValidation
    /** Allow manual input via text field */
    allowManualInput?: boolean
    /** Label for the date picker */
    label?: string
    /** Helper text below the date picker */
    helperText?: string
    /** Error message to display */
    error?: string
    /** Whether the date picker is disabled */
    disabled?: boolean
    /** Additional class names */
    className?: string
    /** Number of months to display */
    numberOfMonths?: number
    /** Default calendar month to open to */
    defaultMonth?: Date
    /** Whether to close the popover when a date is selected (single mode only) */
    closeOnSelect?: boolean
    /** Whether to show week numbers */
    showWeekNumbers?: boolean
    /** ID for the input field */
    id?: string
}

export function SmartDatePicker({
    value,
    onChange,
    mode = 'single',
    dateFormat = 'MMM dd, yyyy',
    placeholder = 'Select date',
    validation,
    allowManualInput = false,
    label,
    helperText,
    error,
    disabled = false,
    className,
    numberOfMonths = 1,
    defaultMonth,
    closeOnSelect = true,
    showWeekNumbers = false,
    id,
}: SmartDatePickerProps) {
    const [open, setOpen] = React.useState(false)
    const [inputValue, setInputValue] = React.useState<string>('')
    const [inputError, setInputError] = React.useState<string>('')

    // Normalize value to array for consistent handling
    function isValidDate(d: any) {
        return d instanceof Date && !isNaN(d.getTime())
    }

    const dates = React.useMemo(() => {
        if (!value) return []
        return Array.isArray(value)
            ? value.map((v) => new Date(v)).filter(isValidDate)
            : isValidDate(new Date(value))
              ? [new Date(value)]
              : []
    }, [value])

    // Format date(s) for display
    const formattedDate = React.useMemo(() => {
        if (dates.length === 0) return ''

        if (mode === 'range' && dates.length === 2) {
            return `${format(dates[0], dateFormat)} - ${format(dates[1], dateFormat)}`
        }

        if (mode === 'multiple') {
            return dates.map((date) => format(date, dateFormat)).join(', ')
        }

        return format(dates[0], dateFormat)
    }, [dates, dateFormat, mode])

    // Update input value when dates change
    React.useEffect(() => {
        setInputValue(formattedDate)
    }, [formattedDate])

    // Validate a date against the validation rules
    const validateDate = React.useCallback(
        (date: Date): string => {
            if (!validation) return ''

            const { minDate, maxDate, disabledDates, disabledDaysOfWeek } =
                validation

            if (minDate && isBefore(date, minDate)) {
                return `Date must be after ${format(minDate, dateFormat)}`
            }

            if (maxDate && isAfter(date, maxDate)) {
                return `Date must be before ${format(maxDate, dateFormat)}`
            }

            if (
                disabledDates?.some(
                    (disabledDate) =>
                        format(disabledDate, 'yyyy-MM-dd') ===
                        format(date, 'yyyy-MM-dd'),
                )
            ) {
                return 'This date is not available'
            }

            if (disabledDaysOfWeek?.includes(date.getDay())) {
                return `${format(date, 'EEEE')}s are not available`
            }

            return ''
        },
        [validation, dateFormat],
    )

    // Handle manual input
    const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value
        setInputValue(value)

        if (!value) {
            onChange(null)
            setInputError('')
            return
        }

        try {
            // For range mode, try to parse two dates
            if (mode === 'range' && value.includes('-')) {
                const [startStr, endStr] = value.split('-').map((s) => s.trim())
                const startDate = parse(startStr, dateFormat, new Date())
                const endDate = parse(endStr, dateFormat, new Date())

                if (isValid(startDate) && isValid(endDate)) {
                    const startError = validateDate(startDate)
                    const endError = validateDate(endDate)

                    if (startError) {
                        setInputError(startError)
                        return
                    }

                    if (endError) {
                        setInputError(endError)
                        return
                    }

                    if (isAfter(startDate, endDate)) {
                        setInputError('End date must be after start date')
                        return
                    }

                    onChange([startDate, endDate])
                    setInputError('')
                    return
                }
            } else {
                // For single mode, parse one date
                const date = parse(value, dateFormat, new Date())

                if (isValid(date)) {
                    const error = validateDate(date)

                    if (error) {
                        setInputError(error)
                        return
                    }

                    onChange(mode === 'single' ? date : [date])
                    setInputError('')
                    return
                }
            }

            setInputError(`Please enter a valid date in ${dateFormat} format`)
        } catch (err) {
            setInputError(`Please enter a valid date in ${dateFormat} format`)
        }
    }

    // Handle calendar date selection
    const handleCalendarSelect = (selected: Date | DateRange | undefined) => {
        if (!selected) {
            onChange(null)
            return
        }

        if (Array.isArray(selected)) {
            // Handle range/multiple mode...
        } else if (selected instanceof Date) {
            // For single mode
            const error = validateDate(selected)

            if (error) {
                setInputError(error)
                return
            }

            onChange(selected)
            setInputError('')

            // Close popover for single mode if closeOnSelect is true
            if (mode === 'single' && closeOnSelect) {
                setOpen(false)
            }
        }
    }

    // Clear the selected date(s)
    const handleClear = (e: React.MouseEvent) => {
        e.stopPropagation()
        onChange(null)
        setInputValue('')
        setInputError('')
    }

    // Determine if the clear button should be shown
    const showClearButton = dates.length > 0 && !disabled

    return (
        <div className={cn('space-y-2', className)}>
            {label && (
                <Label htmlFor={id} className="text-sm font-medium">
                    {label}
                </Label>
            )}

            <div className="relative">
                <Popover open={open} onOpenChange={setOpen}>
                    <PopoverTrigger asChild>
                        <div className="relative w-full">
                            {allowManualInput ? (
                                <Input
                                    id={id}
                                    value={inputValue}
                                    onChange={handleInputChange}
                                    placeholder={placeholder}
                                    disabled={disabled}
                                    className={cn(
                                        'pr-10',
                                        error || inputError
                                            ? 'border-destructive'
                                            : '',
                                    )}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter') {
                                            setOpen(false)
                                        }
                                    }}
                                />
                            ) : (
                                <Button
                                    id={id}
                                    variant="outline"
                                    className={cn(
                                        'w-full justify-start h-fit border-dashed text-left font-normal',
                                        !dates.length &&
                                            'text-muted-foreground',
                                        error || inputError
                                            ? 'border-destructive'
                                            : '',
                                    )}
                                    disabled={disabled}>
                                    <CalendarIcon className="mr-2 h-4 w-4" />
                                    {dates.length > 0
                                        ? formattedDate
                                        : placeholder}
                                </Button>
                            )}

                            {showClearButton && (
                                <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent"
                                    onClick={handleClear}
                                    disabled={disabled}>
                                    <X className="h-4 w-4 text-muted-foreground hover:text-foreground" />
                                    <span className="sr-only">Clear</span>
                                </Button>
                            )}
                        </div>
                    </PopoverTrigger>

                    <PopoverContent className="w-auto p-0" align="start">
                        {mode === 'range' ? (
                            <Calendar
                                mode="range"
                                selected={value as unknown as DateRange}
                                onSelect={
                                    handleCalendarSelect as (
                                        value: DateRange | undefined,
                                    ) => void
                                }
                                disabled={(date) => {
                                    if (!validation) return false
                                    const {
                                        minDate,
                                        maxDate,
                                        disabledDates,
                                        disabledDaysOfWeek,
                                    } = validation

                                    if (minDate && isBefore(date, minDate))
                                        return true
                                    if (maxDate && isAfter(date, maxDate))
                                        return true

                                    if (
                                        disabledDates?.some(
                                            (disabledDate) =>
                                                format(
                                                    disabledDate,
                                                    'yyyy-MM-dd',
                                                ) ===
                                                format(date, 'yyyy-MM-dd'),
                                        )
                                    ) {
                                        return true
                                    }

                                    if (
                                        disabledDaysOfWeek?.includes(
                                            date.getDay(),
                                        )
                                    )
                                        return true

                                    return false
                                }}
                                numberOfMonths={numberOfMonths}
                                defaultMonth={defaultMonth}
                                showWeekNumber={showWeekNumbers}
                                initialFocus
                            />
                        ) : mode === 'multiple' ? (
                            <Calendar
                                mode="multiple"
                                selected={value as Date[]}
                                onSelect={
                                    handleCalendarSelect as (
                                        value: Date[] | undefined,
                                    ) => void
                                }
                                disabled={(date) => {
                                    if (!validation) return false
                                    const {
                                        minDate,
                                        maxDate,
                                        disabledDates,
                                        disabledDaysOfWeek,
                                    } = validation

                                    if (minDate && isBefore(date, minDate))
                                        return true
                                    if (maxDate && isAfter(date, maxDate))
                                        return true

                                    if (
                                        disabledDates?.some(
                                            (disabledDate) =>
                                                format(
                                                    disabledDate,
                                                    'yyyy-MM-dd',
                                                ) ===
                                                format(date, 'yyyy-MM-dd'),
                                        )
                                    ) {
                                        return true
                                    }

                                    if (
                                        disabledDaysOfWeek?.includes(
                                            date.getDay(),
                                        )
                                    )
                                        return true

                                    return false
                                }}
                                numberOfMonths={numberOfMonths}
                                defaultMonth={defaultMonth}
                                showWeekNumber={showWeekNumbers}
                                initialFocus
                            />
                        ) : (
                            <Calendar
                                mode="single"
                                selected={value as Date}
                                onSelect={
                                    handleCalendarSelect as (
                                        value: Date | undefined,
                                    ) => void
                                }
                                disabled={(date) => {
                                    if (!validation) return false
                                    const {
                                        minDate,
                                        maxDate,
                                        disabledDates,
                                        disabledDaysOfWeek,
                                    } = validation

                                    if (minDate && isBefore(date, minDate))
                                        return true
                                    if (maxDate && isAfter(date, maxDate))
                                        return true

                                    if (
                                        disabledDates?.some(
                                            (disabledDate) =>
                                                format(
                                                    disabledDate,
                                                    'yyyy-MM-dd',
                                                ) ===
                                                format(date, 'yyyy-MM-dd'),
                                        )
                                    ) {
                                        return true
                                    }

                                    if (
                                        disabledDaysOfWeek?.includes(
                                            date.getDay(),
                                        )
                                    )
                                        return true

                                    return false
                                }}
                                numberOfMonths={numberOfMonths}
                                defaultMonth={defaultMonth}
                                showWeekNumber={showWeekNumbers}
                                initialFocus
                            />
                        )}
                    </PopoverContent>
                </Popover>
            </div>

            {(error || inputError || helperText) && (
                <p
                    className={cn(
                        'text-sm',
                        error || inputError
                            ? 'text-destructive'
                            : 'text-muted-foreground',
                    )}>
                    {error || inputError || helperText}
                </p>
            )}
        </div>
    )
}
