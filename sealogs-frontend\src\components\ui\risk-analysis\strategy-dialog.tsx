'use client'
import React, { useCallback } from 'react'
import { AlertDialogNew } from '@/components/ui'
import { Label } from '@/components/ui/label'
import { <PERSON>ert, AlertTitle, AlertDescription } from '@/components/ui/alert'
import { AlertCircle } from 'lucide-react'
import { CheckFieldLabel } from '@/components/ui/check-field-label'
import Editor from '@/app/ui/editor'

export interface StrategyDialogProps {
    /**
     * Whether the dialog is open
     */
    open: boolean

    /**
     * Callback when the dialog open state changes
     */
    onOpenChange: (open: boolean) => void

    /**
     * Callback when a strategy is saved
     */
    onSave: () => void

    /**
     * The current risk being edited
     */
    currentRisk: any

    /**
     * The recommended strategies
     */
    recommendedStrategies: Array<any> | false

    /**
     * The current strategies
     */
    currentStrategies: Array<any>

    /**
     * Callback when a strategy is selected
     */
    onStrategySelect: (strategy: any) => void

    /**
     * The current editor content
     */
    content: string

    /**
     * Callback when the editor content changes
     */
    onEditorChange: (content: string) => void
}

export function StrategyDialog({
    open,
    onOpenChange,
    onSave,
    currentRisk,
    recommendedStrategies,
    currentStrategies,
    onStrategySelect,
    content,
    onEditorChange,
}: StrategyDialogProps) {
    const statusChecked = useCallback(
        (strategy: any) => {
            const checked = currentStrategies.some(
                (item) => item.id == strategy.id,
            )

            console.info(
                'statusChecked',
                strategy.id,
                currentStrategies,
                checked,
            )

            return checked
        },
        [currentStrategies],
    )

    return (
        <AlertDialogNew
            openDialog={open}
            setOpenDialog={onOpenChange}
            handleCreate={onSave}
            title="Recommended strategy"
            actionText="Save"
            variant="default"
            size="xl"
            position="center"
            cancelText="Cancel"
            description="Select an existing strategy or create a new one">
            <div className="my-2 flex items-center gap-4 flex-wrap mb-6">
                {currentRisk?.mitigationStrategy?.nodes?.length > 0 && (
                    <>
                        {currentRisk.mitigationStrategy.nodes
                            .filter((s: any) => s.strategy != null)
                            .map((s: any) => (
                                <CheckFieldLabel
                                    key={s.id}
                                    id={`strategy-${s.id}`}
                                    type="checkbox"
                                    checked={statusChecked(s)}
                                    onCheckedChange={() => onStrategySelect(s)}
                                    value={s.id}
                                    variant={'warning'}
                                    label={
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: s.strategy,
                                            }}></div>
                                    }
                                />
                            ))}
                    </>
                )}
                {recommendedStrategies ? (
                    <>
                        {recommendedStrategies
                            ?.filter(
                                (s: any) =>
                                    s.strategy &&
                                    !currentRisk?.mitigationStrategy?.nodes?.some(
                                        (node: any) => node.id === s.id,
                                    ),
                            )
                            ?.map((risk: any) => (
                                <CheckFieldLabel
                                    id={`strategy-${risk.id}`}
                                    key={risk.id}
                                    type="checkbox"
                                    value={risk.id}
                                    checked={statusChecked(risk)}
                                    variant={'warning'}
                                    onCheckedChange={() =>
                                        onStrategySelect(risk)
                                    }
                                    label={
                                        <div
                                            dangerouslySetInnerHTML={{
                                                __html: risk?.strategy,
                                            }}></div>
                                    }
                                />
                            ))}
                    </>
                ) : (
                    <Alert
                        variant="default"
                        className="border-blue-100 bg-blue-50">
                        <AlertCircle className="h-4 w-4 text-blue-600" />
                        <AlertTitle>No recommendations available</AlertTitle>
                        <AlertDescription>
                            You can create a new mitigation strategy below.
                        </AlertDescription>
                    </Alert>
                )}
            </div>
            <Label label="Create a new strategy" htmlFor="strategy">
                <Editor
                    id="strategy"
                    placeholder="Mitigation strategy"
                    className="w-full"
                    content={content}
                    handleEditorChange={onEditorChange}
                />
            </Label>
        </AlertDialogNew>
    )
}
