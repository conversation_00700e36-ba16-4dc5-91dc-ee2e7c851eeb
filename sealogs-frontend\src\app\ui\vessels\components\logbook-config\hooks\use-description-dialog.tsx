import { useState } from 'react'

export const useDescriptionDialog = () => {
    const [open, setOpen] = useState(false)

    const [title, setTitle] = useState('')
    const [content, setContent] = useState('')

    return {
        title,
        content,
        open,
        onOpenChange(open: boolean) {
            setOpen(open)
        },
        openDialog(title: string, content?: string) {
            setTitle(title)
            setContent(content ?? '')

            setOpen(true)
        },
        closeDialog() {
            setOpen(false)
        },
    }
}
