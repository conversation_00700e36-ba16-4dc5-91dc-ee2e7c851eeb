'use client'

import { Dispatch } from 'react'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
} from '../../../actions'
import { Button } from '@/components/ui'
import { AlertCircle } from 'lucide-react'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'

interface IProps {
    groupField: any
    logBookConfig: any
    locked: boolean
    edit_logBookEntry: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
}

export default function PostEngineStrainersFields({
    groupField,
    logBookConfig,
    locked,
    edit_logBookEntry,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
}: IProps) {
    return (
        <>
            {/* Group field heading */}
            {/* <p className="mt-6 uppercase text-left">
                {getFieldLabel(
                    groupField.name,
                    logBookConfig,
                )}
            </p> */}

            {/* Field items */}
            {groupField?.items?.filter((field: any) =>
                displayField(field.name, logBookConfig),
            ).length > 0 && <CheckFieldTopContent />}
            {groupField?.items
                ?.filter((field: any) =>
                    displayField(field.name, logBookConfig),
                )
                ?.map((field: any, index: number) => {
                    /* Old field label rendering */
                    {
                        /* <span
                        key={`${field.label}-${index}`}
                        className=" lg:">
                        {index <
                        groupField
                            .items
                            .length -
                            1
                            ? field.label +
                              ' -'
                            : field.label}
                        {displayDescription(
                            field.name,
                            logBookConfig,
                        ) && (
                            <SeaLogsButton
                                icon="alert"
                                className="w-6 h-6 sup -mt-2 ml-0.5"
                                action={() => {
                                    setDescriptionPanelContent(
                                        displayDescription(
                                            field.name,
                                            logBookConfig,
                                        ),
                                    )
                                    setOpenDescriptionPanel(
                                        true,
                                    )
                                    setDescriptionPanelHeading(
                                        field.name,
                                    )
                                }}
                            />
                        )}{' '}
                    </span> */
                    }

                    return (
                        <DailyCheckField
                            locked={locked || !edit_logBookEntry}
                            key={index}
                            displayField={displayField(
                                field.name,
                                logBookConfig,
                            )}
                            displayDescription={displayDescription(
                                field.name,
                                logBookConfig,
                            )}
                            setOpenDescriptionPanel={setOpenDescriptionPanel}
                            setDescriptionPanelHeading={
                                setDescriptionPanelHeading
                            }
                            displayLabel={getFieldLabel(
                                field.name,
                                logBookConfig,
                            )}
                            inputId={field.value}
                            handleNoChange={() =>
                                // field.handleChange(
                                //     false,
                                // )
                                handleEngineChecks(false, field.value)
                            }
                            defaultNoChecked={field.checked === 'Not_Ok'}
                            handleYesChange={() =>
                                // field.handleChange(
                                //     true,
                                // )
                                handleEngineChecks(true, field.value)
                            }
                            defaultYesChecked={field.checked === 'Ok'}
                            commentAction={() =>
                                showCommentPopup(
                                    getComment(field.name),
                                    composeField(field.name, logBookConfig),
                                )
                            }
                            comment={getComment(field.name)?.comment}
                        />
                    )
                })}

            {/* Description button */}
            {displayDescription(groupField.name, logBookConfig) && (
                <Button
                    variant="text"
                    iconLeft={AlertCircle}
                    onClick={() => {
                        setDescriptionPanelContent(
                            displayDescription(groupField.name, logBookConfig),
                        )
                        setOpenDescriptionPanel(true)
                        setDescriptionPanelHeading(groupField.name)
                    }}
                />
            )}

            {/* DailyCheckGroupField section */}
            {/* <div className="flex text-left">
                <DailyCheckGroupField
                    locked={
                        locked ||
                        !edit_logBookEntry
                    }
                    groupField={groupField?.items?.filter(
                        (field: any) =>
                            displayField(
                                field.name,
                                logBookConfig,
                            ),
                    )}
                    handleYesChange={() =>
                        handleGroupYesChange(
                            groupField?.items?.filter(
                                (
                                    field: any,
                                ) =>
                                    displayField(
                                        field.name,
                                        logBookConfig,
                                    ),
                            ),
                            groupField,
                        )
                    }
                    handleNoChange={() =>
                        handleGroupNoChange(
                            groupField?.items?.filter(
                                (
                                    field: any,
                                ) =>
                                    displayField(
                                        field.name,
                                        logBookConfig,
                                    ),
                            ),
                            groupField,
                        )
                    }
                    defaultNoChecked={groupField?.items
                        ?.filter(
                            (field: any) =>
                                displayField(
                                    field.name,
                                    logBookConfig,
                                ),
                        )
                        ?.every(
                            (field: any) =>
                                field.checked ===
                                'Not_Ok',
                        )}
                    defaultYesChecked={groupField?.items
                        ?.filter(
                            (field: any) =>
                                displayField(
                                    field.name,
                                    logBookConfig,
                                ),
                        )
                        ?.every(
                            (field: any) =>
                                field.checked ===
                                'Ok',
                        )}
                    commentAction={() =>
                        showCommentPopup(
                            getComment(
                                groupField.name,
                            ),
                            composeField(
                                groupField.name,
                                logBookConfig,
                            ),
                        )
                    }
                    comment={
                        getComment(
                            groupField.name,
                        )?.comment
                    }
                />
                {groupField?.items?.map(
                    (
                        field: any,
                        index: number,
                    ) => (
                        <DailyCheckField
                            locked={
                                locked ||
                                !edit_logBookEntry
                            }
                            className={`lg:!grid-cols-2 hidden`}
                            innerWrapperClassName={`lg:!col-span-1`}
                            key={index}
                            displayField={displayField(
                                field.name,
                                logBookConfig,
                            )}
                            displayDescription={displayDescription(
                                field.name,
                                logBookConfig,
                            )}
                            displayLabel={getFieldLabel(
                                field.name,
                                logBookConfig,
                            )}
                            inputId={
                                field.value
                            }
                            handleNoChange={() =>
                                handleEngineChecks(
                                    false,
                                    field.value,
                                )
                            }
                            defaultNoChecked={
                                field.checked ===
                                'Not_Ok'
                            }
                            handleYesChange={() =>
                                handleEngineChecks(
                                    true,
                                    field.value,
                                )
                            }
                            defaultYesChecked={
                                field.checked ===
                                'Ok'
                            }
                            commentAction={() =>
                                showCommentPopup(
                                    getComment(
                                        field.name,
                                    ),
                                    composeField(
                                        field.name,
                                        logBookConfig,
                                    ),
                                )
                            }
                            comment={
                                getComment(
                                    field.name,
                                )?.comment
                            }
                        />
                    ),
                )}
            </div> */}
        </>
    )
}
