'use client'
import { useState, useEffect } from 'react'
import { useLazyQuery } from '@apollo/client'
import { VESSEL_LIST } from '@/app/lib/graphQL/query'

/**
 * Custom hook to fetch and manage complete vessel data
 * Returns a map of vessel ID to complete vessel data including position and icon information
 */
export const useVesselIconData = () => {
    const [vesselIconData, setVesselIconData] = useState<any>({})
    const [loading, setLoading] = useState(true)

    const [queryVesselsWithIcons] = useLazyQuery(VESSEL_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            if (response.readVessels.nodes) {
                const vesselIconMap: any = {}
                response.readVessels.nodes.forEach((vessel: any) => {
                    vesselIconMap[vessel.id] = {
                        ...vessel,
                        // Transform vehiclePositions to vesselPosition for compatibility
                        vesselPosition:
                            vessel.vehiclePositions?.nodes?.[0] || null,
                    }
                })
                setVesselIconData(vesselIconMap)
                setLoading(false)
            }
        },
        onError: (error: any) => {
            console.error('queryVesselsWithIcons error', error)
            setLoading(false)
        },
    })

    useEffect(() => {
        queryVesselsWithIcons({
            variables: {
                filter: { archived: { eq: false } },
            },
        })
    }, [])

    /**
     * Get complete vessel data including position and icon information
     * @param vesselId - The vessel ID
     * @param fallbackVessel - Fallback vessel data if complete data is not available
     * @returns Complete vessel data including position and icon information
     */
    const getVesselWithIcon = (
        vesselId: string | number,
        fallbackVessel?: any,
    ) => {
        return (
            vesselIconData[vesselId] ||
            fallbackVessel || { id: vesselId, title: 'Unknown Vessel' }
        )
    }

    return {
        vesselIconData,
        loading,
        getVesselWithIcon,
    }
}

/**
 * Utility function to enhance vessel objects with icon data
 * @param vessels - Array of vessel objects
 * @param vesselIconData - Map of vessel ID to icon data
 * @returns Array of vessels enhanced with icon data
 */
export const enhanceVesselsWithIcons = (
    vessels: any[],
    vesselIconData: any,
) => {
    return vessels.map((vessel: any) => {
        const iconData = vesselIconData[vessel.id]
        if (iconData) {
            return {
                ...vessel,
                icon: iconData.icon,
                iconMode: iconData.iconMode,
                photoID: iconData.photoID,
            }
        }
        return vessel
    })
}
