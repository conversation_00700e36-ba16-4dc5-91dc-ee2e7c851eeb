'use client'

import React, { useEffect, useState } from 'react'
import {
    Button,
    Input,
    Label,
    Sheet,
    SheetBody,
    SheetContent,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    Sheet<PERSON>it<PERSON>,
} from '@/components/ui'
import Editor from '@/app/ui/editor'
import { IConfigForm } from './types'

interface IProps {
    title: string
    field: any
    isOpen: boolean
    defaultValues: IConfigForm
    onOpenChange: (open: boolean) => void
    onSave: (field: any, form: IConfigForm) => void
    isFieldGroup: boolean
}

export function EditConfigDialog({
    title,
    field,
    isOpen,
    onOpenChange,
    defaultValues,
    isFieldGroup,
    onSave,
}: IProps) {
    const [form, setForm] = useState<IConfigForm>(defaultValues)

    useEffect(() => {
        setForm(defaultValues)
    }, [defaultValues])

    return (
        <Sheet open={isOpen} onOpenChange={onOpenChange}>
            <SheetContent>
                <SheetHeader>
                    <SheetTitle>
                        Field - <span className="font-thin">{title}</span>
                    </SheetTitle>
                </SheetHeader>
                <SheetBody className="flex flex-col gap-4">
                    {!isFieldGroup && (
                        <>
                            <Label label="Customised Field Name">
                                <Input
                                    type="text"
                                    id="field-name"
                                    placeholder="(Optional)"
                                    value={form.fieldName}
                                    onChange={(e) =>
                                        setForm((prev) => ({
                                            ...prev,
                                            fieldName: e.target.value,
                                        }))
                                    }
                                />
                            </Label>
                            <Label label="Sort Order">
                                <Input
                                    type="number"
                                    min={1}
                                    id="field-sort-order"
                                    value={form.sortOrder}
                                    onChange={(e) =>
                                        setForm((prev) => ({
                                            ...prev,
                                            sortOrder: +e.target.value,
                                        }))
                                    }
                                />
                            </Label>
                        </>
                    )}
                    <Label label="Description">
                        <Editor
                            id="field-description"
                            placeholder="Description (Optional)"
                            content={form.description}
                            handleEditorChange={(value: any) =>
                                setForm((prev) => ({
                                    ...prev,
                                    description: value,
                                }))
                            }
                        />
                    </Label>
                </SheetBody>
                <SheetFooter>
                    <Button
                        variant="primary"
                        onClick={() => onSave(field, form)}>
                        Save
                    </Button>
                </SheetFooter>
            </SheetContent>
        </Sheet>
    )
}
