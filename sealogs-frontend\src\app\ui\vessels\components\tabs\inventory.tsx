'use client'
import React from 'react'
import { Button } from '@/components/ui/button'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'
import Link from 'next/link'
import { usePathname, useSearchParams } from 'next/navigation'
import { DataTable } from '@/components/filteredTable'
import { DataTableSortHeader } from '@/components/data-table-sort-header'
import { Badge, P } from '@/components/ui'
import { isOverDueTask } from '@/app/lib/actions'

export default function InventoryTab({ inventories }: { inventories: any }) {
    const pathname = usePathname()
    const searchParams = useSearchParams()
    const columns = [
        {
            accessorKey: 'title',
            header: 'Item',
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <Link
                        href={`/inventory/view/?id=${inventory.id}&redirect_to=${pathname}?${searchParams.toString()}&tab=inventory`}
                        className="flex items-center">
                        {inventory.quantity + ' x ' + inventory.item}
                    </Link>
                )
            },
        },
        {
            accessorKey: 'maintenance',
            header: 'Maintenance',
            cell: ({ row }: { row: any }) => {
                const inventory = row.original

                // Calculate maintenance status from componentMaintenanceChecks
                const getMaintenanceStatus = (inventory: any) => {
                    const checks =
                        inventory.componentMaintenanceChecks?.nodes || []

                    if (checks.length === 0) {
                        return null
                    }

                    // Filter active tasks (not archived)
                    const activeTasks = checks.filter(
                        (task: any) => !task?.archived,
                    )

                    // Count overdue tasks using the same logic as inventory view
                    const overdueTasks = activeTasks.filter((task: any) => {
                        const overDueInfo = isOverDueTask(task)
                        const isOverdue =
                            task.status !== 'Completed' &&
                            task.status !== 'Save_As_Draft' &&
                            overDueInfo.status !== 'Completed' &&
                            overDueInfo.status !== 'Upcoming'

                        return isOverdue
                    })

                    if (overdueTasks.length > 0) {
                        return { type: 'overdue', count: overdueTasks.length }
                    }

                    // If there are maintenance checks but none are overdue, show good status
                    return { type: 'good' }
                }

                const maintenanceStatus = getMaintenanceStatus(inventory)

                return (
                    <>
                        {maintenanceStatus?.type === 'overdue' ? (
                            <Badge variant="destructive">
                                {maintenanceStatus.count}
                            </Badge>
                        ) : maintenanceStatus?.type === 'good' ? (
                            <Badge variant="success">
                                <svg
                                    className={`h-5 w-5`}
                                    viewBox="0 0 20 20"
                                    fill="#27AB83"
                                    aria-hidden="true">
                                    <path
                                        fillRule="evenodd"
                                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                                        clipRule="evenodd"
                                    />
                                </svg>
                            </Badge>
                        ) : null}
                    </>
                )
            },
        },
        {
            accessorKey: 'categories',
            header: 'Categories',
            cellAlignment: 'left' as const,
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex gap-2 items-center">
                        {inventory.categories?.nodes
                            ?.slice(0, 2)
                            .map((cat: any, idx: number) => (
                                <Badge
                                    key={String(idx)}
                                    type="normal"
                                    variant="outline"
                                    className="font-normal">
                                    {cat.name}
                                </Badge>
                            ))}
                        {inventory.categories?.nodes?.length > 2 && (
                            <Popover>
                                <PopoverTrigger asChild>
                                    <Button
                                        variant="outline"
                                        className="!p-2 bg-transparent">
                                        +{' '}
                                        {inventory.categories.nodes.length - 2}{' '}
                                        more
                                    </Button>
                                </PopoverTrigger>
                                <PopoverContent className="w-80">
                                    <div className="space-y-2">
                                        <P className="font-medium text-sm">
                                            All Categories
                                        </P>
                                        <div className="flex flex-wrap gap-2">
                                            {inventory.categories.nodes.map(
                                                (cat: any, idx: number) => (
                                                    <Badge
                                                        key={String(idx)}
                                                        type="normal"
                                                        variant="outline"
                                                        className="font-normal">
                                                        {cat.name}
                                                    </Badge>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        )}
                    </div>
                )
            },
        },
        {
            accessorKey: 'suppliers',
            header: 'Suppliers',
            cellAlignment: 'right' as const,
            cell: ({ row }: { row: any }) => {
                const inventory = row.original
                return (
                    <div className="flex flex-col">
                        {inventory.suppliers?.nodes?.map((supplier: any) => (
                            <div key={String(supplier.id)}>
                                <Link
                                    href={`/inventory/suppliers/view?id=${supplier.id}`}>
                                    {supplier.name}
                                </Link>
                            </div>
                        ))}
                    </div>
                )
            },
        },
    ]
    return (
        <div className="block">
            {inventories?.length > 0 ? (
                <DataTable
                    columns={columns}
                    showToolbar={false}
                    data={inventories}
                    pageSize={20}
                />
            ) : (
                <div className="flex justify-center items-center h-96">
                    <div className="flex flex-col items-center text-3xl ">
                        <div>Inventory</div>
                        <div className="text-2xl">No data available</div>
                    </div>
                </div>
            )}
        </div>
    )
}
