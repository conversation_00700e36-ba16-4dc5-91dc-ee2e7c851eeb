import gql from 'graphql-tag'

export const CREATE_INCIDENT_RECORD = gql`
    mutation CreateIncidentRecord($input: CreateIncidentRecordInput!) {
        createIncidentRecord(input: $input) {
            id
        }
    }
`

export const UPDATE_INCIDENT_RECORD = gql`
    mutation UpdateIncidentRecord($input: UpdateIncidentRecordInput!) {
        updateIncidentRecord(input: $input) {
            id
        }
    }
`

export const DELETE_INCIDENT_RECORDS = gql`
    mutation DeleteIncidentRecords($ids: [ID]!) {
        deleteIncidentRecords(ids: $ids)
    }
`
