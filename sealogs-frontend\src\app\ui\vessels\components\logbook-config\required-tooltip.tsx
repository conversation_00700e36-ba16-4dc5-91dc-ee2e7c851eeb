'use client'

import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Content, TooltipTrigger } from '@/components/ui'
import { AsteriskIcon } from 'lucide-react'
import React from 'react'

export function RequiredTooltip({ message }: { message: string }) {
    return (
        <Tooltip>
            <TooltipTrigger>
                <AsteriskIcon className="text-destructive" size={18} />
            </TooltipTrigger>
            <TooltipContent>
                <p>{message}</p>
            </TooltipContent>
        </Tooltip>
    )
}
