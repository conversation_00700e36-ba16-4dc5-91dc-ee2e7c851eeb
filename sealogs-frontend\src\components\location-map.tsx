import dynamic from 'next/dynamic'
import 'leaflet/dist/leaflet.css'
import 'leaflet-defaulticon-compatibility/dist/leaflet-defaulticon-compatibility.css'
import { Tooltip, useMapEvents, useMap } from 'react-leaflet'
import { useEffect, useState } from 'react'

import { useLazyQuery } from '@apollo/client'
import L from 'leaflet'
import { GET_FILES } from '@/app/lib/graphQL/query'
import { useDebounceFn } from '@reactuses/core'
import { debounce } from 'lodash'

const MapContainer = dynamic(
    () => import('react-leaflet').then((mod) => mod.MapContainer),
    { ssr: false },
)
const Marker = dynamic(
    () => import('react-leaflet').then((mod) => mod.Marker),
    { ssr: false },
)
const TileLayer = dynamic(
    () => import('react-leaflet').then((mod) => mod.TileLayer),
    { ssr: false },
)
// Define custom icon using Leaflet's L.Icon
// Esthon: I commented this out because of "windows not defined" build error
/* const customIcon = L.icon({
    iconUrl:'/vessel-location-marker.png',
    iconSize: [40, 40], // Adjust icon size as needed
  }); */

const vesselIcon = (vessel: any) => {
    const [vesselPhoto, setVesselPhoto] = useState<any>([])

    useEffect(() => {
        if (vessel) {
            if (vessel.iconMode === 'Photo') {
                loadVesselPhoto(vessel.photoID)
            }
        }
    }, [vessel])

    const loadVesselPhoto = async (id: string) => {
        await queryFiles({
            variables: {
                id: [id],
            },
        })
    }

    const [queryFiles] = useLazyQuery(GET_FILES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readFiles.nodes[0]
            setVesselPhoto([data])
        },
        onError: (error: any) => {
            console.error('queryFilesEntry error', error)
        },
    })

    // Dynamically load Leaflet on the client side
    const L = typeof window !== 'undefined' ? require('leaflet') : null

    // Only create the icon if we are on the client side
    let icon = ``
    if (vessel?.iconMode === 'Photo' && vesselPhoto.length > 0) {
        icon = `${process.env.FILE_BASE_URL}${vesselPhoto[0]?.fileFilename}`
    } else if (vessel?.iconMode === 'Icon' && vessel?.icon != null) {
        icon = `/vessel-icons/${vessel?.icon}.svg`
    } else {
        icon = `/vessel.svg`
    }

    if (L) {
        // Create and return the icon only if L (Leaflet) is available
        return L.icon({
            iconUrl: icon,
            className: ' ring-1  rounded-full',
            iconSize: [40, 40], // Adjust the size as needed
        })
    } else {
        return null // During SSR, return null or a fallback
    }
}

// Component to handle map resizing when container size changes
const MapResizeHandler = ({ debounceMs = 150 }: { debounceMs?: number }) => {
    const map = useMap()

    // Debounced resize handler to improve performance
    const { run: debouncedResize } = useDebounceFn(() => {
        if (map) {
            // Invalidate map size to recalculate dimensions
            map.invalidateSize()
        }
    }, debounceMs)

    useEffect(() => {
        // Get the map container element
        const mapContainer = map.getContainer()
        if (!mapContainer) return

        // Set up ResizeObserver to watch for container size changes
        const resizeObserver = new ResizeObserver((entries) => {
            // Check if any observed element actually changed size
            const hasResized = entries.some((entry) => {
                const { width, height } = entry.contentRect
                return width > 0 && height > 0 // Only trigger if dimensions are valid
            })

            if (hasResized) {
                debouncedResize()
            }
        })

        // Observe the map container
        resizeObserver.observe(mapContainer)

        // Also observe the parent container if it exists
        const parentContainer = mapContainer.parentElement
        if (parentContainer) {
            resizeObserver.observe(parentContainer)
        }

        // Initial size check after a short delay to ensure proper initialization
        const timeoutId = setTimeout(() => {
            if (map) {
                map.invalidateSize()
            }
        }, 100)

        // Cleanup
        return () => {
            resizeObserver.disconnect()
            clearTimeout(timeoutId)
        }
    }, [map, debouncedResize])

    return null // This component doesn't render anything
}

interface LocationMapProps {
    position: [number, number]
    vessel?: any
    vessels?: any[] // Support for multiple vessels
    zoom?: number
    onPositionChange?: (position: [number, number]) => void
    className?: string
    enableResize?: boolean // Allow disabling resize behavior if needed
    scrollWheelZoom?: boolean // Allow enabling/disabling scroll wheel zoom
    style?: React.CSSProperties // Allow custom inline styles
    resizeDebounceMs?: number // Customize debounce delay for resize events
    enableClickToSetPosition?: boolean // Allow enabling/disabling click to set position
}

export default function LocationMap({
    position,
    vessel,
    vessels,
    zoom = 13,
    onPositionChange,
    className = 'h-full',
    enableResize = true,
    scrollWheelZoom = false,
    style,
    resizeDebounceMs = 150,
    enableClickToSetPosition = false,
}: LocationMapProps) {
    // Ensure position is always valid
    const validPosition: [number, number] = [
        isNaN(position[0]) ? 0 : position[0],
        isNaN(position[1]) ? 0 : position[1],
    ]
    // Debounce the position change to prevent rapid updates
    const debouncedPositionChange = debounce((lat: number, lng: number) => {
        if (
            typeof lat === 'number' &&
            typeof lng === 'number' &&
            !isNaN(lat) &&
            !isNaN(lng)
        ) {
            onPositionChange?.([lat, lng])
        }
    }, 100)

    const handleMapClick = (e: any) => {
        if (e?.latlng?.lat !== undefined && e?.latlng?.lng !== undefined) {
            const { lat, lng } = e.latlng
            debouncedPositionChange(lat, lng)
        }
    }
    const LocationMarker = (props: any) => {
        const { position, vessel: markerVessel } = props
        useMapEvents({
            dblclick(e) {
                // Only handle double-click if enableClickToSetPosition is true
                if (enableClickToSetPosition) {
                    handleMapClick(e)
                }
            },
        })

        if (typeof window === 'undefined') {
            return <Marker position={position}></Marker>
        } else {
            return (
                <Marker position={position} icon={vesselIcon(markerVessel)}>
                    {markerVessel?.title && (
                        <Tooltip>{markerVessel.title}</Tooltip>
                    )}
                </Marker>
            )
        }
    }

    // Get center position from vessels or fallback to provided position
    const getCenterPosition = (): [number, number] => {
        if (vessels && vessels.length > 0) {
            const firstVessel = vessels.find(
                (v: any) => v.vesselPosition?.lat && v.vesselPosition?.long,
            )
            if (firstVessel) {
                return [
                    firstVessel.vesselPosition.lat || 0,
                    firstVessel.vesselPosition.long || 0,
                ]
            }
        }
        return validPosition
    }

    const centerPosition = getCenterPosition()

    // Create a stable key based on position to prevent unnecessary re-renders
    const mapKey = `map-${Math.round(centerPosition[0] * 1000)}-${Math.round(centerPosition[1] * 1000)}`

    return (
        <MapContainer
            key={mapKey}
            center={centerPosition}
            zoom={zoom}
            scrollWheelZoom={scrollWheelZoom}
            className={className}
            style={{
                minHeight: '200px',
                height: '100%', // Ensure minimum height for proper rendering
                ...style, // Allow custom styles to override defaults
            }}>
            <TileLayer url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png" />

            {/* Render multiple vessels if provided */}
            {vessels && vessels.length > 0
                ? vessels
                      .filter(
                          (v: any) =>
                              v.vesselPosition?.lat != 0 ||
                              v.vesselPosition?.geoLocation?.id > 0,
                      )
                      .map(
                          (v: any, index: number) =>
                              v?.vesselPosition?.id > 0 && (
                                  <LocationMarker
                                      key={index}
                                      position={[
                                          (v.vesselPosition.lat || 0) +
                                              0.001 +
                                              Math.random() * (0.005 - 0.001),
                                          v.vesselPosition.long || 0,
                                      ]}
                                      vessel={v}
                                  />
                              ),
                      )
                : /* Render single vessel if provided */
                  vessel && (
                      <LocationMarker
                          position={validPosition}
                          vessel={vessel}
                      />
                  )}

            {enableResize && <MapResizeHandler debounceMs={resizeDebounceMs} />}
        </MapContainer>
    )
}
