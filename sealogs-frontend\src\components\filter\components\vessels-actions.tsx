'use client'

import * as React from 'react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import { useState } from 'react'
import Link from 'next/link'
import { SealogsCogIcon } from '@/app/lib/icons'

interface CrewDutyDropdownProps {
    onChange: (value: any) => void
}

export const VesselsFilterActions = ({ onChange }: CrewDutyDropdownProps) => {
    const { isMobile } = useSidebar()
    const [showActiveVessels, setShowActiveVessels] = useState(true)

    const handleOnChange = () => {
        setShowActiveVessels((prev) => {
            const newValue = !prev
            onChange(newValue)
            return newValue
        })
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <DropdownMenuItem onClick={() => handleOnChange()}>
                    {showActiveVessels
                        ? 'View archived vessels'
                        : 'View active vessels'}
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <Link href={'/vessel/create'}>
                    <DropdownMenuItem>Add vessel</DropdownMenuItem>
                </Link>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
