'use client'

import { CREATE_OTHER_COMPANY } from '@/app/lib/graphQL/mutation'
import { AlertDialogNew } from '@/components/ui'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { useMutation } from '@apollo/client'
import { useEffect, useRef, useState } from 'react'
import { Heading } from 'react-aria-components'

interface ICompanyForm {
    title: string
    phone: string
    email: string
    address: string
}

interface IProps {
    isOpen: boolean
    setIsOpen: Function
    onCreateSuccess: (newCompany: any) => void
}

export default function DialogCreateCompany({
    isOpen,
    setIsOpen,
    onCreateSuccess,
}: IProps) {
    const formRef = useRef<HTMLFormElement>(null)
    const [formValue, setFormValue] = useState<ICompanyForm>({
        title: '',
        phone: '',
        email: '',
        address: '',
    })

    const [mutationCreateSupplier] = useMutation(CREATE_OTHER_COMPANY, {
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('mutationCreateSupplier error', error)
        },
    })

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        const variables = { input: formValue }

        const { data } = await mutationCreateSupplier({ variables })
        if (data.createOtherCompany.id) {
            onCreateSuccess(data.createOtherCompany)
            setIsOpen(false)
        }
    }

    useEffect(() => {
        setFormValue({
            title: '',
            phone: '',
            email: '',
            address: '',
        })
    }, [isOpen])

    return (
        <AlertDialogNew
            openDialog={isOpen}
            setOpenDialog={(open) => setIsOpen(open)}
            handleCreate={() => formRef?.current?.requestSubmit()}
            actionText="Create Company"
            className="lg:max-w-lg">
            <Heading
                slot="title"
                className="text-2xl font-light leading-6 my-2 text-sldarkblue-800">
                Create New Company
            </Heading>
            <form ref={formRef} onSubmit={handleSubmit}>
                <div className="mt-4">
                    <div className="mb-4">
                        <Input
                            type="text"
                            placeholder="Company Name"
                            value={formValue.title}
                            required
                            onChange={(e) =>
                                setFormValue((prev) => ({
                                    ...prev,
                                    title: e.target.value,
                                }))
                            }
                        />
                    </div>
                    <div className="mb-4">
                        <Input
                            type="text"
                            placeholder="Phone"
                            value={formValue.phone}
                            onChange={(e) =>
                                setFormValue((prev) => ({
                                    ...prev,
                                    phone: e.target.value,
                                }))
                            }
                        />
                    </div>
                    <div className="mb-4">
                        <Input
                            type="email"
                            placeholder="Email"
                            value={formValue.email}
                            onChange={(e) =>
                                setFormValue((prev) => ({
                                    ...prev,
                                    email: e.target.value,
                                }))
                            }
                        />
                    </div>
                    <div>
                        <Textarea
                            rows={3}
                            placeholder="Address"
                            value={formValue.address}
                            onChange={(e) =>
                                setFormValue((prev) => ({
                                    ...prev,
                                    address: e.target.value,
                                }))
                            }
                        />
                    </div>
                </div>
            </form>
        </AlertDialogNew>
    )
}
