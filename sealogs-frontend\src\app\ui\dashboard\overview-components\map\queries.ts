import gql from 'graphql-tag'

export const ReadDashboardData = gql`
    query ReadDashboardData($archived: Int = 0) {
        readDashboardData(archived: $archived) {
            vessels {
                id
                showOnDashboard
                title
                icon
                iconMode
                photoID
                vesselPosition {
                    id
                    lat
                    long
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
            }
        }
    }
`

export const ReadVessels = gql`
    query ReadVessels(
        $vesselFilter: VesselFilterFields = {}
        $statusFilter: VesselStatusFilterFields = {}
    ) {
        readVessels(filter: $vesselFilter) {
            nodes {
                id
                title
                archived
                showOnDashboard
                statusHistory(
                    filter: $statusFilter
                    sort: { date: DESC, created: DESC }
                    limit: 1
                ) {
                    nodes {
                        id
                        date
                        status
                        created
                        comment
                        reason
                        otherReason
                        expectedReturn
                    }
                }
            }
        }
    }
`
