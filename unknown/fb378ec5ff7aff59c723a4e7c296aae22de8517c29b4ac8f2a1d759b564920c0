'use client'

import { useLazyQuery } from '@apollo/client'
import { isEmpty } from 'lodash'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Heading } from 'react-aria-components'
import { ReadTripReport_LogBookEntrySections } from './query/readTripReport_LogBookEntrySections'
import dayjs from 'dayjs'
import Loading from '@/app/loading'
import { formatDate } from '@/app/helpers/dateHelper'
import { exportCsv } from '@/app/helpers/csvHelper'
import Filter from '@/components/filter'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import TableWrapper from '@/components/ui/table-wrapper'

const TripReport = () => {
    const router = useRouter()
    const [filters, setFilters] = useState({} as any)
    const [tripReports, setTripReports] = useState([])

    const downloadCsv = () => {
        if (tripReports.length === 0) {
            return
        }

        const csvEntries: string[][] = [
            [
                'Transit Trip ID',
                'Vessel',
                'Date',
                'Scheduled Depart Time',
                'Depart Time',
                'Depart Location',
                'Adult (boarded at origin)',
                'Passengers Onboard (at final destination)',
                'Total Pax Carried',
                'Trip Type',
                'Scheduled Arrive Time',
                'Arrival Time',
                'Destination',
                'Masters Remarks',
                'Stop',
                'Arrive Time',
                'Pax Off',
                'Pax On',
                'Depart Time',
            ],
        ]

        tripReports.forEach((trip: any) => {
            const baseData = [
                trip.tripReportSchedule?.transitTripID ?? '', // Transit Trip ID
                trip.logBookEntry.vehicle.title, // Vessel
                formatDate(trip.logBookEntry.startDate, false), // Date
                trip.tripScheduleDepartTime ?? '', // Scheduled Depart Time
                trip.departTime ?? '', // Depart Time
                trip.fromLocation.title ?? '', // Depart Location
                trip.pob > 0 ? trip.pob.toString() : '', // Adult (boarded at origin)
            ]

            if (trip.tripReport_Stops?.nodes?.length > 0) {
                trip.tripReport_Stops.nodes.forEach((stop: any) => {
                    const rowData = [...baseData]
                    rowData.push(
                        trip.pob - stop.paxDeparted + stop.paxJoined > 0
                            ? (
                                  trip.pob -
                                  stop.paxDeparted +
                                  stop.paxJoined
                              ).toString()
                            : '', // Passengers On Board (at final destination)
                        trip.pob + stop.paxJoined > 0
                            ? (trip.pob + stop.paxJoined).toString()
                            : '', // Total Pax Carried
                        trip.tripReportScheduleID > 0
                            ? 'Scheduled'
                            : 'Unscheduled', // Trip Type
                        trip.tripScheduleArriveTime ?? '', // Scheduled Arrive Time
                        trip.arriveTime ?? '', // Arrival Time
                        trip.toLocation?.title ?? '', // Destination
                        trip.comment ?? '', // Masters Remarks
                        stop.stopLocation?.title ?? '', // Stop
                        stop.arriveTime ?? '', // Arrive Time
                        stop.paxDeparted?.toString() ?? '', // Pax Off
                        stop.paxJoined?.toString() ?? '', // Pax On
                        stop.departTime ?? '', // Depart Time
                    )
                    csvEntries.push(rowData)
                })
            } else {
                const rowData = [...baseData]
                rowData.push(
                    trip.pob > 0 ? trip.pob.toString() : '', // Passengers On Board (at final destination)
                    trip.pob > 0 ? trip.pob.toString() : '', // Total Pax Carried
                    trip.tripReportScheduleID > 0 ? 'Scheduled' : 'Unscheduled', // Trip Type
                    trip.tripScheduleArriveTime ?? '', // Scheduled Arrive Time
                    trip.arriveTime ?? '', // Arrival Time
                    trip.toLocation?.title ?? '', // Destination
                    trip.comment ?? '', // Masters Remarks
                    '',
                    '',
                    '',
                    '',
                    '', // Empty values for stop columns
                )
                csvEntries.push(rowData)
            }
        })

        const csvFilename = `trip-report-${dayjs().format('YYYY-MM-DD-HHmmss')}.csv`
        exportCsv(csvEntries, csvFilename)
    }

    const handleFilterChange = (item: any) => {
        setFilters({ ...filters, [item.type]: item.data })
    }

    const [
        readTripReport_LogBookEntrySections,
        { loading: readTripReport_LogBookEntrySectionsLoading },
    ] = useLazyQuery(ReadTripReport_LogBookEntrySections, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripReport_LogBookEntrySections.nodes
            setTripReports(data)
        },
        onError: (error: any) => {
            console.error('readTripReport_LogBookEntrySections error', error)
        },
    })

    const loadTripReport = async () => {
        // vesselIds: filters.vessels?.map((vessel: any) => vessel.value),

        // departTimeFrom: filters.fromTime,
        // departTimeTo: filters.toTime,
        // limit: 100,
        // offset: 0,
        const filter: any = {
            archived: { eq: false },
        }
        // Process date range
        const fromDate = filters.dateRange?.startDate
            ? dayjs(filters.dateRange.startDate).format('YYYY-MM-DD')
            : null
        const toDate = filters.dateRange?.endDate
            ? dayjs(filters.dateRange.endDate).format('YYYY-MM-DD')
            : null

        if (fromDate && toDate) {
            filter.logBookEntry = {
                ...filter.logBookEntry,
                startDate: {
                    gte: fromDate,
                    lte: toDate,
                },
            }
        }
        // Process locations
        const fromLocationId = filters.fromLocation?.value
        const toLocationId = filters.toLocation?.value
        if (fromLocationId) {
            filter.fromLocationID = { eq: fromLocationId }
        }
        if (toLocationId) {
            filter.toLocationID = { eq: toLocationId }
        }
        // Process departure time
        const fromTime = filters.fromTime
        const toTime = filters.toTime
        if (fromTime && toTime) {
            filter.departTime = {
                gte: fromTime,
                lte: toTime,
            }
        } else if (fromTime) {
            filter.departTime = {
                gte: fromTime,
            }
        } else if (toTime) {
            filter.departTime = {
                lte: toTime,
            }
        }
        // Add numberPax filter if noPax is true
        if (filters.noPax) {
            filter.numberPax = { eq: 0 }
        }
        // Process vessels
        if (filters.vessels?.length > 0) {
            filter.logBookEntry = {
                ...filter.logBookEntry,
                vehicleID: {
                    in: filters.vessels.map((vessel: any) => vessel.value),
                },
            }
        }
        await readTripReport_LogBookEntrySections({
            variables: {
                filter: filter,
                limit: 100,
                offset: 0,
            },
        })
    }

    useEffect(() => {
        if (!isEmpty(filters)) loadTripReport()
    }, [filters])

    return (
        <div className="w-full p-0  ">
            <div className="flex justify-between pb-4 pt-3 items-center">
                <Heading className="font-light font-monasans text-3xl  ">
                    Trip report
                </Heading>
                <div className="flex">
                    <SeaLogsButton
                        text="Back"
                        type="text"
                        // link="/reporting"
                        action={() => router.push('/reporting')}
                    />
                </div>
            </div>
            <Filter
                tripReportFilterData={filters}
                onChange={handleFilterChange}
            />
            <div>
                {readTripReport_LogBookEntrySectionsLoading ? (
                    <Loading />
                ) : (
                    <div>
                        {tripReports.length > 0 && (
                            <div className="flex justify-end gap-3">
                                <SeaLogsButton
                                    text="Download CSV"
                                    type="primary"
                                    action={downloadCsv}
                                />
                            </div>
                        )}
                        <TableWrapper
                            showHeader
                            headings={[
                                'Vessel',
                                'Date',
                                'Depart Location',
                                'Total Pax Carried',
                                'Total Vehicle Carried',
                                'Destination',
                            ]}>
                            {tripReports.map((trip: any) => (
                                <tr
                                    key={trip.id}
                                    className={`group border-b  hover:bg-white `}>
                                    <td className="px-2 py-3 lg:px-6 text-left ">
                                        {trip.logBookEntry.vehicle.title}
                                    </td>
                                    <td className="px-2 py-3 lg:px-6 text-left ">
                                        {formatDate(
                                            trip.logBookEntry.startDate,
                                        )}
                                    </td>
                                    <td className="px-2 py-3 lg:px-6 text-left ">
                                        {trip.fromLocation.title}
                                    </td>
                                    <td className="px-2 py-3 lg:px-6 text-left ">
                                        {trip.tripReport_Stops?.nodes?.length >
                                        0
                                            ? trip.pob +
                                              trip.tripReport_Stops.nodes.reduce(
                                                  (total: number, stop: any) =>
                                                      total +
                                                      (stop.paxJoined || 0),
                                                  0,
                                              )
                                            : trip.pob}
                                    </td>
                                    <td className="px-2 py-3 lg:px-6 text-left ">
                                        {trip.vob > 0 && trip.vob}
                                    </td>
                                    <td className="px-2 py-3 lg:px-6 text-left ">
                                        {trip.toLocation.title}
                                    </td>
                                </tr>
                            ))}
                        </TableWrapper>
                    </div>
                )}
            </div>
        </div>
    )
}

export default TripReport
