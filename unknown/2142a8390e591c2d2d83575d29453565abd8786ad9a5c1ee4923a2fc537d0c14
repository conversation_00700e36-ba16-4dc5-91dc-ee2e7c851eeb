'use client'
import { ReadTripScheduleServices } from '@/app/lib/graphQL/query'
import { Button } from '@/components/ui'

import { Label } from '@/components/ui/label'
import TableWrapper from '@/components/ui/table-wrapper'
import { useLazyQuery } from '@apollo/client'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'

const TripScheduleServiceList = () => {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [tripScheduleServices, setTripScheduleServices] = useState([])
    const [readTripScheduleServices] = useLazyQuery(ReadTripScheduleServices, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripScheduleServices
            if (data) {
                setTripScheduleServices(data.nodes)
            }
        },
        onError: (error: any) => {
            console.error('ReadTripScheduleServices error', error)
        },
    })
    const loadTripSchedules = async () => {
        await readTripScheduleServices()
    }
    useEffect(() => {
        if (isLoading) {
            loadTripSchedules()
            setIsLoading(false)
        }
    }, [isLoading])
    return (
        <div className="w-full">
            <div className="w-full">
                <TableWrapper headings={['Title', 'Code']}>
                    <tr className="font-medium border-b border-border hidden md:table-row">
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Title</Label>
                        </td>
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Internal Code</Label>
                        </td>
                    </tr>
                    {tripScheduleServices.map((tss: any) => (
                        <tr
                            key={tss.id}
                            className="border-b border-border text-foreground transition-colors hover:bg-muted/50 even:bg-muted/20">
                            <td className="py-4 px-[18px] text-left">
                                <Button
                                    variant="text"
                                    onClick={() =>
                                        router.push(
                                            `/trip-schedule-services/edit/?id=${tss.id}`,
                                        )
                                    }
                                    className="text-foreground hover:text-primary">
                                    {tss.title}
                                </Button>
                            </td>
                            <td className="py-4 px-[18px] text-left">
                                {tss.code}
                            </td>
                        </tr>
                    ))}
                </TableWrapper>
            </div>
        </div>
    )
}

export default TripScheduleServiceList
