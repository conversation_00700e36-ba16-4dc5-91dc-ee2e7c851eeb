'use client'

import { Dispatch } from 'react'
import PostEngineFields from './post-engine-fields'
import PostElectricalFields from './post-electrical-fields'
import PostSteeringFields from './post-steering-fields'
import { Card } from '@/components/ui'

interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    updateVesselDailyCheck_LogBookEntrySection: Function
}

export const PostStartupChecks = ({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    updateVesselDailyCheck_LogBookEntrySection,
}: IProps) => {
    return (
        <Card>
            <PostEngineFields
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                locked={locked}
                logBookConfig={logBookConfig}
                logentryID={logentryID}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                updateVesselDailyCheck_LogBookEntrySection={
                    updateVesselDailyCheck_LogBookEntrySection
                }
                vesselDailyCheck={vesselDailyCheck}
                offline={offline}
            />
            <PostElectricalFields
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                locked={locked}
                logBookConfig={logBookConfig}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                vesselDailyCheck={vesselDailyCheck}
            />
            <PostSteeringFields
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                locked={locked}
                logBookConfig={logBookConfig}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                vesselDailyCheck={vesselDailyCheck}
                logentryID={logentryID}
                updateVesselDailyCheck_LogBookEntrySection={
                    updateVesselDailyCheck_LogBookEntrySection
                }
            />
        </Card>
    )
}
