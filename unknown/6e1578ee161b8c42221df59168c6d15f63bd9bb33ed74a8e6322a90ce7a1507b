'use client'

import { ReadTripReportScheduleStops } from '@/app/lib/graphQL/query'

import { Label } from '@/components/ui/label'
import { useLazyQuery } from '@apollo/client'
import { Plus } from 'lucide-react'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import TableWrapper from '@/components/ui/table-wrapper'
import { Button } from '@/components/ui'

const TripReportScheduleStopList = ({
    tripReportScheduleID = 0,
}: {
    tripReportScheduleID: number
}) => {
    const router = useRouter()
    const [stops, setStops] = useState([])
    const [readTripReportScheduleStops] = useLazyQuery(
        ReadTripReportScheduleStops,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readTripReportScheduleStops
                if (data) {
                    setStops(data.nodes)
                }
            },
            onError: (error: any) => {
                console.error('ReadTripReportScheduleStops error', error)
            },
        },
    )
    const loadStops = async () => {
        await readTripReportScheduleStops({
            variables: {
                filter: { tripReportScheduleID: { eq: tripReportScheduleID } },
            },
        })
    }
    const handleCreateStop = () => {
        router.push(
            `/trip-report-schedule-stop/create?tripReportScheduleID=${tripReportScheduleID}`,
        )
    }
    useEffect(() => {
        if (tripReportScheduleID > 0) {
            loadStops()
        }
    }, [tripReportScheduleID])
    return (
        <div className="w-full">
            <div className="w-full">
                <div className="flex w-full justify-end my-4">
                    <Button
                        variant="secondary"
                        iconLeft={Plus}
                        onClick={handleCreateStop}>
                        Add Trip Stop
                    </Button>
                </div>
                <TableWrapper headings={[]}>
                    <tr className="font-medium border-b border-border hidden md:table-row">
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Stop Location</Label>
                        </td>
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Arrival Time</Label>
                        </td>
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Depart Time</Label>
                        </td>
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Pick Up</Label>
                        </td>
                        <td className="hidden md:table-cell py-4 px-[18px] text-left">
                            <Label>Drop Off</Label>
                        </td>
                    </tr>
                    {stops.map((stop: any) => (
                        <tr
                            key={stop.id}
                            className="border-b border-border text-foreground transition-colors hover:bg-muted/50 even:bg-muted/20">
                            <td className="py-4 px-[18px] text-left">
                                <Button
                                    variant="text"
                                    onClick={() =>
                                        router.push(
                                            `/trip-report-schedule-stop/edit/?id=${stop.id}`,
                                        )
                                    }
                                    className="text-foreground hover:text-primary">
                                    {stop.stopLocation.title}
                                </Button>
                            </td>
                            <td className="py-4 px-[18px] text-left">{`${dayjs(dayjs().format('YYYY-MM-DD') + ' ' + stop.arriveTime).format('HH:mma')}`}</td>
                            <td className="py-4 px-[18px] text-left">{`${dayjs(dayjs().format('YYYY-MM-DD') + ' ' + stop.departTime).format('HH:mma')}`}</td>
                            <td className="py-4 px-[18px] text-left">
                                {stop.pickUp ? 'Yes' : 'No'}
                            </td>
                            <td className="py-4 px-[18px] text-left">
                                {stop.dropOff ? 'Yes' : 'No'}
                            </td>
                        </tr>
                    ))}
                </TableWrapper>
            </div>
        </div>
    )
}

export default TripReportScheduleStopList
