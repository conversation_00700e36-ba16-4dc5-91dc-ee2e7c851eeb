'use client'
import {
    GET_GEO_LOCATIONS,
    ReadOneTripReportScheduleStop,
    ReadTripReportSchedules,
} from '@/app/lib/graphQL/query'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useRouter, useSearchParams } from 'next/navigation'
import { useEffect, useState } from 'react'
import TimeField from '../logbook/components/time'
import dayjs from 'dayjs'
import { isEmpty, trim } from 'lodash'
import {
    CreateTripReportScheduleStop,
    DeleteTripReportScheduleStop,
    UpdateTripReportScheduleStop,
} from '@/app/lib/graphQL/mutation'

// Shadcn UI components
import { Input } from '@/components/ui/input'
import { Checkbox } from '@/components/ui/checkbox'
import { Button } from '@/components/ui/button'
import { Combobox } from '@/components/ui/comboBox'
import { Label } from '@/components/ui/label'
import { Separator } from '@/components/ui/separator'
import { H2 } from '@/components/ui/typography'
import { useToast } from '@/hooks/use-toast'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'

// Lucide icons
import { Save, X, Trash2 } from 'lucide-react'

const TripReportScheduleStopForm = () => {
    const router = useRouter()
    const searchParams = useSearchParams()
    const id = searchParams.get('id') ?? 0
    const tripReportScheduleID = searchParams.get('tripReportScheduleID') ?? 0
    const [stop, setStop] = useState({} as any)
    const [tripReportSchedules, setTripReportSchedules] = useState([])
    const [selectedTripReportSchedule, setSelectedTripReportSchedule] =
        useState({} as any)
    const [geoLocations, setGeoLocations] = useState([])
    const [selectedStopLocation, setSelectedStopLocation] = useState({} as any)
    const [openConfirmDeleteDialog, setOpenConfirmDeleteDialog] =
        useState(false)
    const [
        readGeoLocations,
        // eslint-disable-next-line @typescript-eslint/no-unused-vars
        { loading: _ },
    ] = useLazyQuery(GET_GEO_LOCATIONS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readGeoLocations.nodes
            if (data) {
                setGeoLocations(
                    data.map((v: any) => {
                        return { value: v.id, label: v.title }
                    }),
                )
            }
        },
        onError: (error: any) => {
            console.error('readGeoLocations error', error)
        },
    })
    const [
        readOneTripReportScheduleStop,
        { loading: readOneTripReportScheduleStopLoading },
    ] = useLazyQuery(ReadOneTripReportScheduleStop, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = { ...response.readOneTripReportScheduleStop }
            if (data) {
                setStop(data)
            }
        },
        onError: (error: any) => {
            console.error('readOneTripReportScheduleStop error', error)
        },
    })
    const [readTripReportSchedules] = useLazyQuery(ReadTripReportSchedules, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripReportSchedules
            if (data) {
                const trs = data.nodes.map((v: any) => {
                    return { value: v.id, label: v.title }
                })
                setTripReportSchedules(trs)
            }
        },
        onError: (error: any) => {
            console.error('ReadTripReportSchedules error', error)
        },
    })
    const [
        updateTripReportScheduleStop,
        { loading: updateTripReportScheduleStopLoading },
    ] = useMutation(UpdateTripReportScheduleStop, {
        onCompleted: (_response: any) => {
            router.push(
                `/trip-report-schedules/edit/?id=${stop.tripReportScheduleID}`,
            )
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
            console.error('updateTripReportSchedule onError', error.message)
        },
    })
    const [
        deleteTripReportScheduleStop,
        { loading: deleteTripReportScheduleStopLoading },
    ] = useMutation(DeleteTripReportScheduleStop, {
        onCompleted: (_response: any) => {
            router.push(
                `/trip-report-schedules/edit/?id=${stop.tripReportScheduleID}`,
            )
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
            console.error('deleteTripReportSchedule onError', error.message)
        },
    })
    const [
        createTripReportScheduleStop,
        { loading: createTripReportScheduleStopLoading },
    ] = useMutation(CreateTripReportScheduleStop, {
        onCompleted: (_response: any) => {
            router.push(
                `/trip-report-schedules/edit/?id=${stop.tripReportScheduleID}`,
            )
        },
        onError: (error: any) => {
            toast({
                variant: 'destructive',
                title: 'Error',
                description: error.message,
            })
            console.error('createTripReportScheduleStop onError', error.message)
        },
    })
    const loadTripReportSchedules = async () => {
        await readTripReportSchedules()
    }
    const loadStop = async () => {
        await readOneTripReportScheduleStop({ variables: { id: id } })
    }
    const handleOnChangeTitle = (e: any) => {
        setStop({ ...stop, title: e.target.value })
    }
    const handleOnChangeArriveTime = (e: any) => {
        setStop({
            ...stop,
            arriveTime: dayjs(e).format('HH:mm:ss'),
        })
    }
    const handleOnChangeDepartTime = (e: any) => {
        setStop({
            ...stop,
            departTime: dayjs(e).format('HH:mm:ss'),
        })
    }
    const handleOnChangePickUp = (checked: boolean) => {
        setStop({
            ...stop,
            pickUp: checked,
        })
    }
    const handleOnChangeDropOff = (checked: boolean) => {
        setStop({
            ...stop,
            dropOff: checked,
        })
    }
    const handleOnChangeTransitID = (e: any) => {
        setStop({ ...stop, transitID: e.target.value })
    }
    const handleOnChangeTripReportSchedule = (e: any) => {
        setStop({ ...stop, tripReportScheduleID: e.value })
    }
    const handleOnChangeStopLocation = (e: any) => {
        setStop({ ...stop, stopLocationID: e.value })
    }
    const loadGeoLocations = async () => {
        await readGeoLocations()
    }
    const { toast } = useToast()

    const validateInputs = () => {
        let errorMessage = ''
        if (isEmpty(trim(stop.title))) {
            errorMessage += '\nThe title is required.'
        }

        if (!isEmpty(trim(errorMessage))) {
            toast({
                variant: 'destructive',
                title: 'Validation Error',
                description: trim(errorMessage),
            })
            return false
        } else {
            return true
        }
    }

    const saveStop = async () => {
        const validated = validateInputs()
        if (!validated) return
        const saveData = { ...stop }
        if (+id > 0) {
            delete saveData.__typename
            delete saveData.stopLocation
            delete saveData.tripReportSchedule
            // update
            await updateTripReportScheduleStop({
                variables: { input: saveData },
            })
        } else {
            // create
            await createTripReportScheduleStop({
                variables: { input: saveData },
            })
        }
    }
    const confirmDeleteStop = () => {
        setOpenConfirmDeleteDialog(true)
    }
    const handleDeleteStop = async () => {
        await deleteTripReportScheduleStop({
            variables: {
                id: [stop.id],
            },
        })
    }
    useEffect(() => {
        if (+id > 0) {
            loadStop()
        } else if (+tripReportScheduleID > 0) {
            const newStop = {
                id: 0,
                tripReportScheduleID: tripReportScheduleID,
                title: '',
                arriveTime: dayjs().format('HH:mm:ss'),
                departTime: dayjs().format('HH:mm:ss'),
                pickUp: true,
                dropOff: true,
                transitID: null,
                stopLocationID: 0,
            }
            setStop(newStop)
        }
        loadTripReportSchedules()
        loadGeoLocations()
    }, [id])
    useEffect(() => {
        if (!isEmpty(stop) && !isEmpty(tripReportSchedules)) {
            setSelectedTripReportSchedule(
                tripReportSchedules.find(
                    (v: any) => v.value === stop.tripReportScheduleID,
                ),
            )
        }
    }, [stop, tripReportSchedules])
    useEffect(() => {
        if (!isEmpty(stop) && !isEmpty(geoLocations)) {
            setSelectedStopLocation(
                geoLocations.find((v: any) => v.value === stop.stopLocationID),
            )
        }
    }, [stop, geoLocations])
    return (
        <div className="w-full mb-20 md:mb-0">
            <div className="px-2 lg:px-4 mt-2 ">
                <div className="flex md:flex-nowrap md:flex-row gap-3 flex-col-reverse flex-wrap justify-between md:items-center items-start">
                    <H2>
                        {+id > 0 ? 'Edit' : 'New'} Trip Report Schedule Stop
                    </H2>
                </div>
                <Separator className="my-4" />
                <div className="my-4">
                    <Label>Title</Label>
                    <Input
                        defaultValue={stop.title}
                        onChange={handleOnChangeTitle}
                        type="text"
                        placeholder="Title"
                    />
                </div>
                <div className="my-4">
                    <Label>Arrive Time</Label>
                    <TimeField
                        time={stop.arriveTime}
                        handleTimeChange={handleOnChangeArriveTime}
                        timeID="trs-arrive-time"
                        fieldName="Arrive Time"
                    />
                </div>
                <div className="my-4">
                    <Label>Depart Time</Label>
                    <TimeField
                        time={stop.departTime}
                        handleTimeChange={handleOnChangeDepartTime}
                        timeID="trs-depart-time"
                        fieldName="Depart Time"
                    />
                </div>
                <div className="flex items-center gap-2 my-4">
                    <Checkbox
                        id="pick-up"
                        checked={stop.pickUp}
                        onCheckedChange={handleOnChangePickUp}
                    />
                    <Label
                        htmlFor="pick-up"
                        className="font-medium cursor-pointer">
                        Pick Up
                    </Label>
                </div>
                <div className="flex items-center gap-2 my-4">
                    <Checkbox
                        id="drop-off"
                        checked={stop.dropOff}
                        onCheckedChange={handleOnChangeDropOff}
                    />
                    <Label
                        htmlFor="drop-off"
                        className="font-medium cursor-pointer">
                        Drop Off
                    </Label>
                </div>
                <div className="my-4">
                    <Label>Transit ID</Label>
                    <Input
                        defaultValue={stop.transitID}
                        onChange={handleOnChangeTransitID}
                        type="text"
                        placeholder="Transit ID"
                    />
                </div>
                <div className="my-4">
                    <Label>Trip Schedule</Label>
                    <Combobox
                        options={tripReportSchedules}
                        value={selectedTripReportSchedule}
                        onChange={handleOnChangeTripReportSchedule}
                        placeholder="Select Trip Report Schedule"
                    />
                </div>
                <div className="my-4">
                    <Label>Stop Location</Label>
                    <Combobox
                        options={geoLocations}
                        value={selectedStopLocation}
                        onChange={handleOnChangeStopLocation}
                        placeholder="Select Stop Location"
                    />
                </div>
                <Separator className="my-6" />

                <div className="flex justify-end gap-2">
                    <Button
                        variant="text"
                        iconLeft={X}
                        onClick={() => {
                            router.push(
                                `/trip-report-schedules/edit/?id=${stop.tripReportScheduleID}`,
                            )
                        }}>
                        Cancel
                    </Button>
                    {+id > 0 && (
                        <Button
                            variant="destructive"
                            iconLeft={Trash2}
                            onClick={confirmDeleteStop}>
                            Delete
                        </Button>
                    )}
                    <Button
                        iconLeft={Save}
                        onClick={saveStop}
                        isLoading={
                            readOneTripReportScheduleStopLoading ||
                            updateTripReportScheduleStopLoading ||
                            deleteTripReportScheduleStopLoading ||
                            createTripReportScheduleStopLoading
                        }>
                        {+id > 0 ? 'Update' : 'Save'} Changes
                    </Button>
                </div>
            </div>
            <AlertDialogNew
                openDialog={openConfirmDeleteDialog}
                setOpenDialog={setOpenConfirmDeleteDialog}
                handleCreate={handleDeleteStop}
                title="Delete Trip Stop"
                description="Are you sure you want to delete this stop?"
                actionText="Delete"
                variant="danger"
            />
        </div>
    )
}

export default TripReportScheduleStopForm
