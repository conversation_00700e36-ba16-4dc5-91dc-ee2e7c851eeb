import * as dateFns from 'date-fns'

export type VesselStatus = 'AvailableForVoyage' | 'OnVoyage' | 'OutOfService'

export interface IStatusHistory {
  id: number
  date: Date
  status: VesselStatus
  vesselID?: number,
  created?: Date
}

export interface IVesselServiceSummary {
  onVoyage: number
  availableForVoyage: number
  outOfService: number
}

export const getLatestStatusFromHistory = (statusHistory: IStatusHistory[]): VesselStatus => {
  const latest = [...statusHistory].sort((a, b) => new Date(b.created ?? b.date).getTime() - new Date(a.created ?? a.date).getTime()).shift();

  return latest?.status ?? "AvailableForVoyage";
}

export const getServiceSummary = (
  statusHistory: IStatusHistory[],
  startingStatus: VesselStatus,
  startDate: Date,
  endDate: Date,
): IVesselServiceSummary => {
  //if no history found
  if (statusHistory.length === 0) {
    let usedEndDate = endDate;

    const now = new Date;
    if (dateFns.differenceInCalendarDays(endDate, now) > 0) {
      usedEndDate = now;
    }

    const totalDays = dateFns.differenceInCalendarDays(usedEndDate, startDate) + 1;

    return {
      onVoyage: startingStatus === 'OnVoyage' ? totalDays : 0,
      availableForVoyage: startingStatus === 'AvailableForVoyage' ? totalDays : 0,
      outOfService: startingStatus === 'OutOfService' ? totalDays : 0,
    }
  }

  let onVoyage = startingStatus === 'OnVoyage' ? 1 : 0
  let availableForVoyage = startingStatus === 'AvailableForVoyage' ? 1 : 0
  let outOfService = startingStatus === 'OutOfService' ? 1 : 0

  let date: Date | null = null
  let currentStatus: VesselStatus | null = null

  const firstStatusEntry = statusHistory[0];
  const firstDate = dateFns.format(firstStatusEntry.date, 'yyyy-MM-dd');
  if (firstDate !== dateFns.format(startDate, 'yyyy-MM-dd')) {
    date = startDate;
    currentStatus = startingStatus;
  }

  statusHistory.forEach((status) => {
    if (date === null) {
      date = status.date
      currentStatus = status.status
      return
    }

    const diffInDays = dateFns.differenceInCalendarDays(status.date, date)

    if (diffInDays !== 0) {
      switch (currentStatus) {
        case 'OnVoyage':
          onVoyage += diffInDays
          break

        case 'AvailableForVoyage':
          availableForVoyage += diffInDays
          break

        case 'OutOfService':
          outOfService += diffInDays
          break
      }
    }

    date = status.date
    currentStatus = status.status
  })

  //if end date is not same as latest date in history
  if (dateFns.differenceInCalendarDays(endDate, date!) > 0) {
    let usedLastDate = endDate;

    const now = new Date;
    //if now is before than latest end date
    if (dateFns.differenceInCalendarDays(endDate, now) > 0) {
      usedLastDate = now;
    }

    const totalDiffDaysBeforeEnd = dateFns.differenceInCalendarDays(usedLastDate, date!);

    switch (currentStatus) {
      case 'OnVoyage':
        onVoyage += totalDiffDaysBeforeEnd
        break

      case 'AvailableForVoyage':
        availableForVoyage += totalDiffDaysBeforeEnd
        break

      case 'OutOfService':
        outOfService += totalDiffDaysBeforeEnd
        break
    }

  }

  return {
    onVoyage,
    availableForVoyage,
    outOfService,
  }
}