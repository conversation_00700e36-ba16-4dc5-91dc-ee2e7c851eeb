
export interface CrewMember {
    id: number | string;
    firstName?: string;
    surname?: string;
    profileImage?: string;
    trainingStatus?: {
        label: string;
    };
    primaryDutyID?: number;
}

export interface CrewDuty {
    id: number | string;
    title: string;
    archived?: boolean;
}

export interface CrewOption {
    label: string;
    value: string;
    data?: CrewMember & {
        trainingStatus?: {
            label: string;
            dues?: Array<{
                trainingType: { title: string };
                status: { label: string };
            }>;
        };
    };
    profile?: {
        firstName?: string;
        surname?: string;
        avatar?: string;
    };
}

// Table row types
export interface CrewMemberDuty {
    id: string | number;
    dutyPerformed: {
        id: number;
        title?: string;
    };
    punchIn: string | Date;
    punchOut: string | Date | null;
    workDetails?: string;
    dutyPerformedID: number;
    logBookEntryID: number;
}

export interface TableCrewMember {
    id: React.Key | null | undefined;
    crewMemberID: string | number;
    archived: boolean;
    crewMember: {
        trainingStatus: {
            label: string;
        };
        firstName: string | null | undefined;
        surname: string | null | undefined;
        profileImage?: string;
    };
    workDetails?: string;
    dutyPerformed: {
        id: number;
        title?: string;
    };
    punchIn: string | Date;
    punchOut: string | Date | null;
    duties?: CrewMemberDuty[];
}

export interface LogBookConfig {
    customisedLogBookComponents?: {
        nodes?: Array<{
            title: string;
            active: boolean;
            customisedComponentFields?: {
                nodes?: Array<{
                    fieldName: string;
                    status: string;
                    customisedFieldTitle?: string;
                }>;
            };
        }>;
    };
}

export interface Vessel {
    id: number | string;
    maxPOB?: number;
    minCrew?: number;
}

export interface CrewWelfare {
    id: string | number;
    logBookEntryID: number;
    fitness: string | null;
    imSafe: string | null;
    safetyActions: string | null;
    waterQuality: string | null;
    __typename: string;
}

export interface LogBookEntrySection {
    id: string | number;
    className: string;
}

export interface CrewProps {
    crewSections: TableCrewMember[] | false;
    allCrew: CrewMember[];
    logBookEntryID: number;
    locked: boolean;
    logBookConfig: LogBookConfig | false;
    setCrewMembers: (crew: TableCrewMember[]) => void;
    crewWelfareCheck: CrewWelfare | null;
    updateCrewWelfare: (data: CrewWelfare) => void;
    vessel: Vessel | false;
    masterID: number;
    logEntrySections: LogBookEntrySection[];
    offline?: boolean;
    crewMembersList: number[];
}

// Type guard to check if vessel is not false
export const isVessel = (vessel: Vessel | false): vessel is Vessel => {
    return vessel !== false;
};
