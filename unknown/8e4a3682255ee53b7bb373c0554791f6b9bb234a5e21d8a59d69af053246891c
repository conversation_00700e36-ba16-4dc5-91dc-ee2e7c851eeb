'use client'

import { Dispatch, useMemo } from 'react'
import { useEngineFields } from '../use-engine-fields'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import {
    DailyCheckField,
    CheckField,
    CheckFieldContent,
} from '@/components/daily-check-field'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'

interface IProps {
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
}

export default function EngineerTowlineWinchFields({
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
}: IProps) {
    // Create a wrapper function to handle the type mismatch
    const handleSetDescriptionPanelContent = (
        content: string | React.ReactNode,
    ) => {
        // Convert ReactNode to string if needed
        if (typeof content === 'string') {
            setDescriptionPanelContent(content)
        } else {
            // For ReactNode, convert to string or use a default value
            setDescriptionPanelContent(String(content) || '')
        }
    }
    const { engrTowlineWinchFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(
                engrTowlineWinchFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [logBookConfig, engrTowlineWinchFields])

    return (
        <>
            {logBookConfig && vesselDailyCheck && (
                <CheckField>
                    <CheckFieldContent>
                        {filteredFields.map((groupField: any) => (
                            <div key={groupField.name}>
                                {groupField?.items
                                    ?.filter((field: any) =>
                                        displayField(field.name, logBookConfig),
                                    )
                                    ?.map((field: any, index: number) => (
                                        <DailyCheckField
                                            locked={
                                                locked || !edit_logBookEntry
                                            }
                                            key={index}
                                            displayField={displayField(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            displayDescription={displayDescription(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            setDescriptionPanelContent={
                                                handleSetDescriptionPanelContent
                                            }
                                            setOpenDescriptionPanel={
                                                setOpenDescriptionPanel
                                            }
                                            setDescriptionPanelHeading={
                                                setDescriptionPanelHeading
                                            }
                                            displayLabel={getFieldLabel(
                                                field.name,
                                                logBookConfig,
                                            )}
                                            inputId={field.value}
                                            handleNoChange={() =>
                                                handleEngineChecks(
                                                    false,
                                                    field.value,
                                                )
                                            }
                                            defaultNoChecked={
                                                field.checked === 'Not_Ok'
                                            }
                                            handleYesChange={() =>
                                                handleEngineChecks(
                                                    true,
                                                    field.value,
                                                )
                                            }
                                            defaultYesChecked={
                                                field.checked === 'Ok'
                                            }
                                            commentAction={() =>
                                                showCommentPopup(
                                                    getComment(field.name),
                                                    composeField(
                                                        field.name,
                                                        logBookConfig,
                                                    ),
                                                )
                                            }
                                            comment={
                                                getComment(field.name)?.comment
                                            }
                                        />
                                    ))}
                                {displayDescription(
                                    groupField.name,
                                    logBookConfig,
                                ) && (
                                    <Button
                                        variant="text"
                                        iconLeft={AlertCircle}
                                        onClick={() => {
                                            handleSetDescriptionPanelContent(
                                                displayDescription(
                                                    groupField.name,
                                                    logBookConfig,
                                                ),
                                            )
                                            setOpenDescriptionPanel(true)
                                            setDescriptionPanelHeading(
                                                groupField.name,
                                            )
                                        }}
                                    />
                                )}
                            </div>
                        ))}
                    </CheckFieldContent>
                </CheckField>
            )}
        </>
    )
}
