'use client'

import Filter from '@/components/filter'
import { formatDate } from '@/app/helpers/dateHelper'
import { VESSEL_STATUS } from '@/app/lib/graphQL/query'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { H3, P } from '@/components/ui/typography'

import { ArrowLeft } from 'lucide-react'
import TableWrapper from '@/components/ui/table-wrapper'
import { Button } from '@/components/ui'

export default function ServiceReport() {
    const router = useRouter()
    const [vesselID, setVesselID] = useState('')
    const [dateRange, setDateRange] = useState<any>(false)
    const [vesselStatuses, setVesselStatuses] = useState([])

    const handleFilterChange = (filters: any) => {
        if (filters.type === 'vessel' && filters.data?.value) {
            setVesselID(filters.data?.value)
        }
        if (filters.type === 'dateRange') {
            setDateRange(filters.data)
        }
    }

    useEffect(() => {
        if (vesselID) {
            loadVesselStatus({
                variables: {
                    id: vesselID,
                },
            })
        }
    }, [vesselID])

    const [loadVesselStatus] = useLazyQuery(VESSEL_STATUS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response) => {
            const data = response.readVesselStatuss.nodes
            setVesselStatuses(data)
        },
        onError: (error) => {
            console.error(error)
        },
    })

    const filterByDate = (entry: any) => {
        if (dateRange) {
            return (
                dayjs(entry.created).isAfter(dayjs(dateRange.startDate)) &&
                dayjs(entry.created).isBefore(dayjs(dateRange.endDate))
            )
        }
        return true
    }

    const briefReport = () => {
        let lastTime = 0
        let OnVoyage = 0
        let AvailableForVoyage = 0
        let OutOfService = 0

        vesselStatuses
            ?.filter((entry: any) => filterByDate(entry))
            .forEach((entry: any) => {
                const entryTime = new Date(entry.date).getTime()

                if (lastTime > 0) {
                    const timeDiff = lastTime - entryTime

                    switch (entry.status) {
                        case 'OnVoyage':
                            OnVoyage += timeDiff
                            break
                        case 'AvailableForVoyage':
                            AvailableForVoyage += timeDiff
                            break
                        case 'OutOfService':
                            OutOfService += timeDiff
                            break
                    }
                }

                lastTime = entryTime
            })

        OnVoyage = OnVoyage > 0 ? OnVoyage / 86400000 : OnVoyage
        AvailableForVoyage =
            AvailableForVoyage > 0
                ? AvailableForVoyage / 86400000
                : AvailableForVoyage
        OutOfService = OutOfService > 0 ? OutOfService / 86400000 : OutOfService
        return (
            <div className="mt-4 space-y-2">
                <P>OnVoyage: {OnVoyage} days</P>
                <P>AvailableForVoyage: {AvailableForVoyage} days</P>
                <P>OutOfService: {OutOfService} days</P>
            </div>
        )
    }

    return (
        <div className="w-full">
            <div className="flex justify-between pb-4 pt-3 items-center">
                <H3>Service report</H3>
                <Button
                    variant="text"
                    iconLeft={ArrowLeft}
                    onClick={() => router.push('/reporting')}>
                    Back
                </Button>
            </div>
            <Filter onChange={handleFilterChange} />
            {vesselStatuses.length > 0 && (
                <>
                    <TableWrapper
                        headings={['Date:firstHead', 'Status', 'Reason']}>
                        {vesselStatuses
                            ?.filter((entry: any) => filterByDate(entry))
                            .map((entry: any) => (
                                <tr
                                    key={entry.id}
                                    className="border-b hover:bg-muted/50">
                                    <td className="p-3 text-left">
                                        {formatDate(entry.date)}
                                    </td>
                                    <td className="p-3">{entry.status}</td>
                                    <td className="p-3 text-center">
                                        {entry?.reason}
                                        {entry?.otherReason
                                            ? ' - ' + entry?.otherReason
                                            : ''}
                                    </td>
                                    <td className="p-3">
                                        {entry?.expectedReturn
                                            ? formatDate(entry.expectedReturn)
                                            : ''}
                                    </td>
                                    <td className="p-3">
                                        {entry?.comment && (
                                            <div
                                                dangerouslySetInnerHTML={{
                                                    __html: entry?.comment,
                                                }}></div>
                                        )}
                                    </td>
                                </tr>
                            ))}
                    </TableWrapper>
                    {briefReport()}
                </>
            )}
        </div>
    )
}
