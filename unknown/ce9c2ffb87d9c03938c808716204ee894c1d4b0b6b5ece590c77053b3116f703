'use client'

import { useMemo } from 'react'
import {
    getDriveShaftsChecksFields,
    getEngrElectronicsFields,
    getEngrGeneratorFields,
    getEngrMechanicalFields,
    getEngrTowlineWinchFields,
    getOtherEngineFieldFields,
    getOtherEngineRoomVisualInspectionFields,
    getOtherFuelSystemsFields,
    getOtherMainEngineCheckFields,
    getOtherPropulsionCheckFields,
    getPostElectricalFields,
    getPostEngineFields,
    getPostEngineStrainersFields,
    getPostSteeringFields,
    getPreElectricalFields,
    getPreElectricalVisualFields,
    getPreEngineFields,
    getPreEngineMountFields,
    getPreEngineOilFields,
    getPreFields,
} from '@/app/lib/dailyCheckFields'

export function useEngineFields(logBookConfig: any, vesselDailyCheck: any) {
    const fields = useMemo(() => {
        const preEngineFields = getPreEngineFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const preEngineOilFields = getPreEngineOilFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const preEngineMountFields = getPreEngineMountFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const preElectricalFields = getPreElectricalFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const preElectricalVisualFields = getPreElectricalVisualFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const preFields = getPreFields(logBookConfig, vesselDailyCheck)

        const postEngineFields = getPostEngineFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const postEngineStrainersFields = getPostEngineStrainersFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const postElectricalFields = getPostElectricalFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const postSteeringFields = getPostSteeringFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const otherDriveShaftFields = getDriveShaftsChecksFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const otherEngineFieldFields = getOtherEngineFieldFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const otherMainEngineCheckFields = getOtherMainEngineCheckFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const otherEngineRoomVisualInspectionFields =
            getOtherEngineRoomVisualInspectionFields(
                logBookConfig,
                vesselDailyCheck,
            )

        const otherFuelSystemsFields = getOtherFuelSystemsFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const otherPropulsionCheckFields = getOtherPropulsionCheckFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const engrMechanicalFields = getEngrMechanicalFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const engrGeneratorFields = getEngrGeneratorFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const engrElectronicsFields = getEngrElectronicsFields(
            logBookConfig,
            vesselDailyCheck,
        )

        const engrTowlineWinchFields = getEngrTowlineWinchFields(
            logBookConfig,
            vesselDailyCheck,
        )

        return {
            preEngineFields,
            preEngineOilFields,
            preEngineMountFields,
            preElectricalFields,
            preElectricalVisualFields,
            preFields,
            postEngineFields,
            postEngineStrainersFields,
            postElectricalFields,
            postSteeringFields,
            otherDriveShaftFields,
            otherEngineFieldFields,
            otherMainEngineCheckFields,
            otherEngineRoomVisualInspectionFields,
            otherFuelSystemsFields,
            otherPropulsionCheckFields,
            engrMechanicalFields,
            engrGeneratorFields,
            engrElectronicsFields,
            engrTowlineWinchFields,
        }
    }, [logBookConfig, vesselDailyCheck])

    return fields
}
