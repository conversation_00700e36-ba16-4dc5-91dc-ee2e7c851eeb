'use client'

import React, { Dispatch } from 'react'
import OtherDrive<PERSON><PERSON>Fields from './other-drive-shaft-fields'
import OtherEngineFieldFields from './other-engine-field-fields'
import OtherEngineMainCheckFields from './other-engine-main-check-fields'
import OtherEngineRoowVisualInspectionFields from './other-engine-roow-visual-inspection-fields'
import OtherFuelSystemsFields from './other-fuel-systems-fields'
import OtherPropulsionCheckFields from './other-propulsion-check-fields'
import { Card } from '@/components/ui'

interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: (content: string | React.ReactNode) => void
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    updateVesselDailyCheck_LogBookEntrySection: Function
}

export const OtherEngineChecks = ({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading = () => {},
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    updateVesselDailyCheck_LogBookEntrySection,
}: IProps) => {
    return (
        <Card>
            <OtherDriveShaftFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
            />
            <OtherEngineFieldFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
            />
            <OtherEngineMainCheckFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
            />
            <OtherEngineRoowVisualInspectionFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
            />
            <OtherFuelSystemsFields
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
            />
            <OtherPropulsionCheckFields
                logentryID={logentryID}
                logBookConfig={logBookConfig}
                vesselDailyCheck={vesselDailyCheck}
                locked={locked}
                edit_logBookEntry={edit_logBookEntry}
                offline={offline}
                getComment={getComment}
                handleEngineChecks={handleEngineChecks}
                setDescriptionPanelHeading={setDescriptionPanelHeading}
                setDescriptionPanelContent={setDescriptionPanelContent}
                setOpenDescriptionPanel={setOpenDescriptionPanel}
                showCommentPopup={showCommentPopup}
                updateVesselDailyCheck_LogBookEntrySection={
                    updateVesselDailyCheck_LogBookEntrySection
                }
            />
        </Card>
    )
}
