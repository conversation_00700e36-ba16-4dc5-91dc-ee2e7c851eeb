// useTrainingFilters.ts
import { useCallback, useState } from 'react'

export interface SearchFilter {
  // ↳ extend this with any other properties you already have
  vesselID?: { eq?: number; in?: number[] }
  trainingTypes?: { id: { contains?: number; in?: number[] } }
  trainer?: { id: { eq?: number; in?: number[] } }
  members?: { id: { eq?: number; in?: number[] } }
  date?: { gte: Date; lte: Date }
}

/**
 * Encapsulates filter state + `handleFilterChange`.
 *
 * @param opts.initialFilter  usually {}
 * @param opts.loadList       your `loadTrainingList` fn
 * @param opts.loadDues       your `loadTrainingSessionDues` fn
 * @param opts.toggleOverdue  setter returned by `useState(true)`
 */
export function useTrainingFilters(opts: {
  initialFilter: SearchFilter
  loadList: (page: number, f: SearchFilter) => void
  loadDues: (f: SearchFilter) => void
  toggleOverdue: React.Dispatch<React.SetStateAction<boolean>>
}) {
  const { initialFilter, loadList, loadDues, toggleOverdue } = opts
  const [filter, setFilter] = useState<SearchFilter>(initialFilter)

  const handleFilterChange = useCallback(
    ({ type, data }: { type: string; data: any }) => {
      const next: SearchFilter = { ...filter }

      /* ---- vessel ------------------------------------------------------- */
      if (type === 'vessel') {
        if (Array.isArray(data) && data.length) {
          next.vesselID = { in: data.map((d) => +d.value) }
        } else if (data && !Array.isArray(data)) {
          next.vesselID = { eq: +data.value }
        } else {
          delete next.vesselID
        }
      }

      /* ---- trainingType ------------------------------------------------- */
      if (type === 'trainingType') {
        if (Array.isArray(data) && data.length) {
          next.trainingTypes = { id: { in: data.map((d) => +d.value) } }
        } else if (data && !Array.isArray(data)) {
          next.trainingTypes = { id: { contains: +data.value } }
        } else {
          delete next.trainingTypes
        }
      }

      /* ---- trainer ------------------------------------------------------ */
      if (type === 'trainer') {
        if (Array.isArray(data) && data.length) {
          next.trainer = { id: { in: data.map((d) => +d.value) } }
        } else if (data && !Array.isArray(data)) {
          next.trainer = { id: { eq: +data.value } }
        } else {
          delete next.trainer
        }
      }

      /* ---- member ------------------------------------------------------- */
      if (type === 'member') {
        if (Array.isArray(data) && data.length) {
          next.members = { id: { in: data.map((d) => +d.value) } }
        } else if (data && !Array.isArray(data)) {
          next.members = { id: { eq: +data.value } }
        } else {
          delete next.members
        }
      }

      /* ---- dateRange ---------------------------------------------------- */
      if (type === 'dateRange') {
        if (data?.startDate && data?.endDate) {
          next.date = { gte: data.startDate, lte: data.endDate }
        } else {
          delete next.date
        }
      }

      /* ---- overdue switch ---------------------------------------------- */
      if (type === 'overdue' && data !== undefined) {
        toggleOverdue((prev) => !data)
      }

      // push state & refetch
      setFilter(next)
      loadDues(next)
      loadList(0, next)
    },
    [filter, loadDues, loadList, toggleOverdue],
  )

  return { filter, setFilter, handleFilterChange }
}
