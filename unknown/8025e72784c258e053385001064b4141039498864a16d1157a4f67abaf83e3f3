import gql from 'graphql-tag'

export const ReadTripScheduleServices = gql`
    query ReadTripScheduleServices(
        $limit: Int = 100
        $offset: Int = 0
        $filter: TripScheduleServiceFilterFields = {}
    ) {
        readTripScheduleServices(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { title: ASC, id: ASC }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
            }
        }
    }
`
