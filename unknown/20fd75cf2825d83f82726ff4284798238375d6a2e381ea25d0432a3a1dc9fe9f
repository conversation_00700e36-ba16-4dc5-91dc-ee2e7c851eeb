import { useEffect, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import Select from 'react-select'
import { ReadTripScheduleServices } from './query/readTripScheduleServices'
import { Skeleton } from '@/components/ui/skeleton'

const TripScheduleServiceDropdown = ({
    value,
    onChange,
    placeholder = 'Service',
    isClearable = false,
    isMulti = false,
    withNonScheduledOption = false,
}: any) => {
    const [tripScheduleServices, setTripScheduleServices] = useState([] as any)
    const [allTripScheduleServices, setAllTripScheduleServices] = useState(
        [] as any,
    )
    const nonScheduledOption = {
        value: 'non-scheduled',
        label: 'Non-Scheduled',
    }
    const [currentOffset, setCurrentOffset] = useState(0)
    const [hasMorePages, setHasMorePages] = useState(true)
    const [isLoadingMore, setIsLoadingMore] = useState(false)
    const [selectedValue, setSelectedValue] = useState(value)
    const limit = 100 // Default limit from the query

    const [
        readTripScheduleServices,
        { loading: readTripScheduleServicesLoading },
    ] = useLazyQuery(ReadTripScheduleServices, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripScheduleServices.nodes
            const pageInfo = response.readTripScheduleServices.pageInfo

            if (data) {
                const gls = data.map((v: any) => {
                    return { value: +v.id, label: v.title }
                })

                // Accumulate results
                const updatedTss = [...allTripScheduleServices, ...gls]
                setAllTripScheduleServices(updatedTss)

                // Add non-scheduled option at the top if enabled
                if (withNonScheduledOption) {
                    setTripScheduleServices([nonScheduledOption, ...updatedTss])
                } else {
                    setTripScheduleServices(updatedTss)
                }

                // Check if there are more pages to load
                if (pageInfo.hasNextPage) {
                    // Prepare for next page
                    const nextOffset = currentOffset + limit
                    setCurrentOffset(nextOffset)

                    // Load next page
                    setIsLoadingMore(true)
                    readTripScheduleServices({
                        variables: {
                            limit: limit,
                            offset: nextOffset,
                        },
                    })
                } else {
                    setHasMorePages(false)
                    setIsLoadingMore(false)
                }
            }
        },
        onError: (error: any) => {
            console.error('readTripScheduleServices error', error)
            setIsLoadingMore(false)
        },
    })

    const loadTripScheduleServices = async () => {
        // Reset pagination state
        setAllTripScheduleServices([])
        setCurrentOffset(0)
        setHasMorePages(true)
        setIsLoadingMore(true)

        // Initial query with offset 0
        await readTripScheduleServices({
            variables: {
                limit: limit,
                offset: 0,
            },
        })
    }

    useEffect(() => {
        loadTripScheduleServices()
    }, [])

    // Add non-scheduled option when the component initializes
    useEffect(() => {
        if (withNonScheduledOption && allTripScheduleServices.length > 0) {
            setTripScheduleServices([
                nonScheduledOption,
                ...allTripScheduleServices,
            ])

            // If the current value is 'non-scheduled', ensure it's the only selected option
            if (Array.isArray(value)) {
                const hasNonScheduled = value.some(
                    (v) =>
                        v === 'non-scheduled' ||
                        (v && v.value === 'non-scheduled'),
                )

                if (hasNonScheduled) {
                    setSelectedValue([nonScheduledOption])
                }
            } else if (
                value === 'non-scheduled' ||
                (value && value.value === 'non-scheduled')
            ) {
                setSelectedValue(nonScheduledOption)
            }
        }
    }, [withNonScheduledOption, allTripScheduleServices, value])

    // Update selectedValue when value prop changes
    useEffect(() => {
        if (value && tripScheduleServices.length > 0) {
            // Handle the case where value is an array (for isMulti=true)
            if (Array.isArray(value)) {
                // Check if non-scheduled is in the array
                const hasNonScheduled = value.some(
                    (v) =>
                        v === 'non-scheduled' ||
                        (v && v.value === 'non-scheduled'),
                )

                if (hasNonScheduled && withNonScheduledOption) {
                    // If non-scheduled is selected, only use that option
                    setSelectedValue([nonScheduledOption])
                } else {
                    // Otherwise, map all selected values
                    const selectedOptions = value
                        .map((v) => {
                            if (
                                v === 'non-scheduled' ||
                                (v && v.value === 'non-scheduled')
                            ) {
                                return nonScheduledOption
                            }
                            return tripScheduleServices.find(
                                (t: any) => t.value === (v.value || v),
                            )
                        })
                        .filter(Boolean)
                    setSelectedValue(selectedOptions)
                }
            } else {
                // Handle the case where value is 'non-scheduled'
                if (
                    value === 'non-scheduled' ||
                    (value && value.value === 'non-scheduled')
                ) {
                    setSelectedValue(nonScheduledOption)
                } else {
                    const tss = tripScheduleServices.find(
                        (t: any) => t.value === (value.value || value),
                    )
                    if (tss) {
                        setSelectedValue(tss)
                    }
                }
            }
        }
    }, [value, tripScheduleServices, withNonScheduledOption])

    const isLoading = readTripScheduleServicesLoading || isLoadingMore

    return (
        <>
            {isLoading && tripScheduleServices.length === 0 ? (
                <Skeleton />
            ) : (
                <Select
                    id="tss-dropdown"
                    closeMenuOnSelect={true}
                    options={tripScheduleServices}
                    menuPlacement="auto"
                    value={selectedValue}
                    onChange={(selected) => {
                        if (!withNonScheduledOption) {
                            // If non-scheduled option is not enabled, just use default behavior
                            setSelectedValue(selected)
                            onChange(selected)
                            return
                        }

                        // MULTI-SELECT MODE
                        if (isMulti) {
                            // Handle case where nothing is selected
                            if (!selected || selected.length === 0) {
                                setSelectedValue([])
                                onChange([])
                                return
                            }

                            // Check if non-scheduled is in the new selection
                            const hasNonScheduled = selected.some(
                                (option: any) =>
                                    option.value === 'non-scheduled',
                            )

                            // Check if non-scheduled was previously selected
                            const wasNonScheduledSelected =
                                Array.isArray(selectedValue) &&
                                selectedValue.some(
                                    (option: any) =>
                                        option.value === 'non-scheduled',
                                )

                            // Case 1: User selected non-scheduled
                            if (hasNonScheduled && !wasNonScheduledSelected) {
                                // User just selected non-scheduled, clear everything else
                                setSelectedValue([nonScheduledOption])
                                onChange([nonScheduledOption])
                                return
                            }

                            // Case 2: User had non-scheduled selected and selected something else
                            if (
                                wasNonScheduledSelected &&
                                selected.length > 1
                            ) {
                                // Remove non-scheduled from selection
                                const filteredSelection = selected.filter(
                                    (option: any) =>
                                        option.value !== 'non-scheduled',
                                )
                                setSelectedValue(filteredSelection)
                                onChange(filteredSelection)
                                return
                            }
                        }
                        // SINGLE-SELECT MODE
                        else {
                            // If nothing is selected
                            if (!selected) {
                                setSelectedValue(null)
                                onChange(null)
                                return
                            }

                            // If non-scheduled is selected
                            if (selected.value === 'non-scheduled') {
                                setSelectedValue(nonScheduledOption)
                                onChange(selected)
                                return
                            }

                            // If something else is selected (non-scheduled will be automatically deselected)
                            setSelectedValue(selected)
                            onChange(selected)
                            return
                        }

                        // Default fallback behavior
                        setSelectedValue(selected)
                        onChange(selected)
                    }}
                    placeholder={placeholder}
                    isClearable={isClearable}
                    // classNames={{
                    //     control: () => classes.selectControl + ' w-full',
                    //     singleValue: () => classes.selectSingleValue,
                    //     menu: () => classes.selectMenu,
                    //     option: () => classes.selectOption,
                    // }}
                    isMulti={isMulti}
                    styles={{
                        container: (provided) => ({
                            ...provided,
                            width: '100%',
                        }),
                    }}
                />
            )}
        </>
    )
}

export default TripScheduleServiceDropdown
