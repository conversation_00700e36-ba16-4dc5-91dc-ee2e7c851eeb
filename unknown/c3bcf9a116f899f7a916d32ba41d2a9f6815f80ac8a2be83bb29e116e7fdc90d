'use client'
import CustomPagination from '@/components/ui/custom-pagination'
import { formatDate } from '@/app/helpers/dateHelper'
import { ReadTripReportSchedules } from '@/app/lib/graphQL/query'
import { Label } from '@/components/ui/label'
import { useLazyQuery } from '@apollo/client'
import dayjs from 'dayjs'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { Link } from 'react-aria-components'
import TableWrapper from '@/components/ui/table-wrapper'

const TripReportScheduleList = () => {
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [tripReportSchedules, setTripReportSchedules] = useState([])
    const limit = 100
    const [pageInfo, setPageInfo] = useState({
        totalCount: 0,
        hasNextPage: false,
        hasPreviousPage: false,
    })
    const [page, setPage] = useState(0)
    let [filter, setFilter] = useState({} as SearchFilter)
    const [readTripReportSchedules] = useLazyQuery(ReadTripReportSchedules, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readTripReportSchedules
            if (data) {
                setTripReportSchedules(data.nodes)
                setPageInfo(data.pageInfo)
            }
        },
        onError: (error: any) => {
            console.error('ReadTripReportSchedules error', error)
        },
    })
    const loadTripSchedules = async (
        startPage: number = 0,
        searchFilter: any = { ...filter },
    ) => {
        const updatedFilter: SearchFilter = {
            ...searchFilter,
        }
        await readTripReportSchedules({
            variables: {
                limit: limit,
                offset: startPage * limit,
                filter: updatedFilter,
            },
        })
    }
    const handleNavigationClick = (newPage: any) => {
        if (newPage < 0 || newPage === page) return
        setPage(newPage)
        loadTripSchedules(newPage)
    }
    useEffect(() => {
        if (isLoading) {
            loadTripSchedules()
            setIsLoading(false)
        }
    }, [isLoading])
    return (
        <div className="w-full py-0">
            <div className="flex w-full justify-start flex-col items-start">
                <TableWrapper headings={[]}>
                    <tr className="font-medium border-b border-slblue-200 hidden md:table-row">
                        <td className="hidden md:table-cell">
                            <Label>Title</Label>
                        </td>
                        <td className="hidden md:table-cell">
                            <Label>Depart</Label>
                        </td>
                        <td className="hidden md:table-cell">
                            <Label>Date Range</Label>
                        </td>
                        <td className="hidden md:table-cell">
                            <Label>Vessels</Label>
                        </td>
                    </tr>
                    {tripReportSchedules.map((trs: any) => (
                        <tr
                            key={trs.id}
                            className={`border-b border-sldarkblue-50 even:bg-sllightblue-50/50 hover:bg-sllightblue-50 `}>
                            <td className="p-2 min-w-1/2">
                                <Link
                                    href={`/trip-report-schedules/edit/?id=${trs.id}`}
                                    className="group-hover:text-sllightblue-1000">
                                    {trs.title}
                                </Link>
                            </td>
                            <td className="p-2 min-w-1/2">{`${trs.fromLocation.title || ''}  @ ${dayjs(trs.start + ' ' + trs.departTime).format('HH:mma')}`}</td>
                            <td className="p-2 min-w-1/2">{`From ${formatDate(trs.start)} until ${formatDate(trs.end)}`}</td>
                            <td className="p-2 min-w-1/2">{`${trs.vehicles.nodes.map((v: any) => v.title).join(', ')}`}</td>
                        </tr>
                    ))}
                </TableWrapper>
            </div>
            <div className="flex justify-center">
                <CustomPagination
                    page={page}
                    limit={limit}
                    onClick={(newPage: number) =>
                        handleNavigationClick(newPage)
                    }
                    {...pageInfo}
                    visiblePageCount={5}
                />
            </div>
        </div>
    )
}

export default TripReportScheduleList
