'use client'

import type React from 'react'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON>le,
    DialogFooter,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'

interface AddCrewMemberDialogProps {
    openDialog: boolean
    setOpenDialog: (open: boolean) => void
    handleCreate: (formData: FormData) => void
    actionText: string
    error?: { message: string }
}

export function AddCrewMemberDialog({
    openDialog,
    setOpenDialog,
    handleCreate,
    actionText,
    error,
}: AddCrewMemberDialogProps) {
    const onSubmit = (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()
        const formData = new FormData(e.currentTarget)
        handleCreate(formData)
    }

    return (
        <Dialog open={openDialog} onOpenChange={setOpenDialog}>
            <DialogContent>
                <DialogHeader>
                    <DialogTitle className="font-medium">
                        Add Crew Member
                    </DialogTitle>
                </DialogHeader>
                <form onSubmit={onSubmit}>
                    <div className="grid grid-cols-1 gap-4 border-t pt-6">
                        <div className="flex gap-4">
                            <Input
                                id="crew-firstName"
                                name="firstName"
                                type="text"
                                placeholder="First Name"
                            />
                            <Input
                                id="crew-surname"
                                name="surname"
                                type="text"
                                placeholder="Surname"
                            />
                        </div>
                        <div className="flex gap-4">
                            <Input
                                id="crew-username"
                                name="username"
                                type="text"
                                placeholder="Username"
                            />
                            <Input
                                id="crew-password"
                                name="password"
                                type="password"
                                placeholder="Password"
                            />
                        </div>
                        <div className="flex gap-4">
                            <Input
                                id="crew-email"
                                name="email"
                                type="email"
                                placeholder="Email"
                            />
                            <Input
                                id="crew-phoneNumber"
                                name="phoneNumber"
                                type="text"
                                placeholder="Phone Number"
                            />
                        </div>
                        {error && (
                            <div className="text-rose-600">{error.message}</div>
                        )}
                    </div>
                    <DialogFooter className="mt-6">
                        <Button type="submit">{actionText}</Button>
                    </DialogFooter>
                </form>
            </DialogContent>
        </Dialog>
    )
}
