import gql from 'graphql-tag'

export const TRAINING_TYPE_BY_IDS = gql`
    query GetOneTrainingType($id: [ID!]!) {
        readTrainingTypes(filter: { id: { in: $id } }) {
            nodes {
                id
                archived
                highWarnWithin
                mediumWarnWithin
                occursEvery
                procedure
                title
                trainingSessions {
                    nodes {
                        id
                        date
                        vessel {
                            id
                            title
                        }
                    }
                }
                vessels {
                    nodes {
                        id
                        title
                    }
                }
                customisedComponentField {
                    nodes {
                        id
                        status
                        sortOrder
                        fieldName
                        description
                    }
                }
            }
        }
    }
`
