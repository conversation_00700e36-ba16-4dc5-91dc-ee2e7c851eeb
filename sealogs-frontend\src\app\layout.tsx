import { ApolloWrapper } from './lib/ApolloWrapper'
import './globals.css'
import { ThemeProviders } from './providers'
import AuthProvider from '@/components/auth-provider'
import { Metadata, Viewport } from 'next'
import DataFetcher from './offline/components/DataFetcher'
import DataSync from './offline/components/DataSync'
import Hotjar from '@hotjar/browser'
import { NuqsAdapter } from 'nuqs/adapters/next/app'

const siteId = 5285190
const hotjarVersion = 6

// Hotjar.init(siteId, hotjarVersion);

export const metadata: Metadata = {
    applicationName: 'SeaLogs',
    title: {
        default: 'SeaLogs',
        template: 'SeaLogs',
    },
    description: 'SeaLogs Application',
    manifest: '/manifest.json',
    appleWebApp: {
        capable: true,
        statusBarStyle: 'default',
        title: 'SeaLogs',
    },
    formatDetection: {
        telephone: false,
    },
}

export const viewport: Viewport = {
    themeColor: '#FFFFFF',
}

export default function RootLayout({
    children,
}: {
    children: React.ReactNode
}) {
    return (
        <html lang="en" suppressHydrationWarning>
            <body suppressHydrationWarning={true}>
                <NuqsAdapter>
                    <ApolloWrapper>
                        <ThemeProviders>
                            <AuthProvider>
                                <div className="app-root">{children}</div>
                            </AuthProvider>
                        </ThemeProviders>
                    </ApolloWrapper>
                </NuqsAdapter>
            </body>
        </html>
    )
}
