'use client'
import { useEffect, useRef, useState } from 'react'
import Link from 'next/link'
import { CREATE_LOGBOOK_ENTRY } from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_LOGBOOKENTRY } from '@/app/lib/graphQL/query'
import { isCrew } from '@/app/helpers/userHelper'
import { formatDate } from '@/app/helpers/dateHelper'
import SeaLogsButton from '@/components/ui/sea-logs-button'

export default function VesselLogStatus({
    vessel = false,
    icon = false,
}: {
    vessel: any
    icon: boolean
}) {
    const [vesselLog, setVesselLog] = useState<any>(null)
    const [vesselState, setVesselState] = useState<string>('Locked')
    const router = useRouter()
    const [isNewLogEntryDisabled, setIsNewLogEntryDisabled] =
        useState<boolean>(false)
    const [imCrew, setImCrew] = useState<boolean>(false)

    /*const [fetchCrewMembers] = useLazyQuery(GET_CREW_MEMBERS, {
        fetchPolicy: 'no-cache',
        onCompleted: (crewResponse: any) => {
            if (!crewResponse?.data?.readCrewMembers_LogBookEntrySections?.nodes) return

            const crew = crewResponse.data.readCrewMembers_LogBookEntrySections.nodes

            // Use entries stored from the logBookEntries query:
            if (!lastFetchedEntries.current) return;
            const data = lastFetchedEntries.current.map((entry: any) => {
                const crewData = crew.filter(
                    (crewMember: any) => crewMember.logBookEntryID === entry.id
                );
                return {
                    ...entry,
                    crew: crewData,
                };
            });

            handleSetVesselState(data)
        },
        onError: (error: any) => {
            console.error('Error fetching crew members:', error)
        },
    })*/

    const lastFetchedEntries = useRef<any[]>([])

    const [fetchLogBookEntries] = useLazyQuery(GET_LOGBOOKENTRY, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const entries = response.GetLogBookEntries?.nodes ?? []
            lastFetchedEntries.current = entries // store entries for next query

            // Process entries to check for open logs
            entries.forEach((entry: any) => {
                if (entry.state !== 'Locked') {
                    setVesselLog(entry)
                    setVesselState('Open')
                }
            })
        },
        onError: (error: any) => {
            console.error('Error fetching log book entries:', error)
        },
    })

    useEffect(() => {
        if (vessel) {
            loadLogBookEntries()
        }
    }, [vessel])

    useEffect(() => {
        setImCrew(isCrew() || false)
    }, [])
    const loadLogBookEntries = async () => {
        await fetchLogBookEntries({
            variables: {
                vesselId: +vessel?.id,
            },
        })

        // Process entries to check for open logs
        if (
            lastFetchedEntries.current &&
            lastFetchedEntries.current.length > 0
        ) {
            lastFetchedEntries.current.forEach((entry: any) => {
                if (entry.state !== 'Locked') {
                    setVesselLog(entry)
                    setVesselState('Open')
                }
            })
        }
    }

    const handleCreateNewLogEntry = async () => {
        if (vessel?.logBookID > 0) {
            setIsNewLogEntryDisabled(true)
            await createLogEntry({
                variables: {
                    input: {
                        vehicleID: vessel.id,
                        logBookID: vessel.logBookID,
                    },
                },
            })
        }
    }

    const [createLogEntry] = useMutation(CREATE_LOGBOOK_ENTRY, {
        onCompleted: (response: any) => {
            router.push(
                `/log-entries/?vesselID=${vessel.id}&logentryID=${response.createLogBookEntry.id}`,
            )
        },
        onError: (error: any) => {
            console.error('createLogEntry error', error)
        },
    })

    return (
        <>
            {icon ? (
                vesselState === 'Open' ? (
                    <Link
                        href={`/log-entries?vesselID=${vessel.id}&logentryID=${vesselLog?.id}`}>
                        <button
                            type="button"
                            className="grow-0 group w-full inline-flex justify-center items-center rounded-md bg-slorange-300 p-4 text-slorange-1000 shadow-sm ring-1 ring-inset ring-slorange-1000 hover:bg-slorange-400">
                            <svg
                                className="-ml-1.5 mr-1 h-5 w-5 hidden md:inline-block"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 19 19"
                                fill="currentColor">
                                <path
                                    className="stroke-slorange-1000 group-hover:stroke-white"
                                    id="Path"
                                    d="M9,18a1.994,1.994,0,0,1-1.414-.586l-.828-.828A2,2,0,0,0,5.343,16H1a1,1,0,0,1-1-1V1A1,1,0,0,1,1,0H5A4.992,4.992,0,0,1,9,2Z"
                                    transform="translate(0.5 0.5)"
                                    fill="none"
                                    strokeWidth="1"
                                />
                                <path
                                    className="stroke-slorange-1000 group-hover:stroke-white"
                                    id="secondary"
                                    d="M0,18V2A4.992,4.992,0,0,1,4,0H8A1,1,0,0,1,9,1V15a1,1,0,0,1-1,1H3.657a2,2,0,0,0-1.414.586l-.828.828A1.994,1.994,0,0,1,0,18Z"
                                    transform="translate(9.5 0.5)"
                                    fill="none"
                                    strokeWidth="1"
                                />
                            </svg>
                            Open log entry
                        </button>
                    </Link>
                ) : (
                    <>
                        {!imCrew && (
                            <SeaLogsButton
                                icon="new_logbook"
                                className="group w-full inline-flex justify-center items-center rounded-md p-4 shadow-sm ring-1 ring-inset hover:bg-gray-100"
                                text="New logbook entry"
                                action={handleCreateNewLogEntry}
                                isDisabled={isNewLogEntryDisabled}
                            />
                        )}
                    </>
                )
            ) : vesselState === 'Open' ? (
                <div className={` inline-block alert`}>
                    <Link
                        href={`/log-entries?vesselID=${vessel.id}&logentryID=${vesselLog?.id}`}>
                        Open log{' '}
                        {vesselLog?.startDate
                            ? formatDate(vesselLog.startDate)
                            : ''}
                    </Link>
                </div>
            ) : (
                <></>
            )}
        </>
    )
}
