import gql from 'graphql-tag'

export const GET_CALENDAR_DIALOG_LOGBOOK_ENTERY_BY_ID = gql`
    query GetLogBookEntries($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            masterID
            state
            className
            startDate
            endDate
            fuelLevel
            logBookID
            createdByID
            master {
                id
                firstName
                surname
            }
        }
    }
`
