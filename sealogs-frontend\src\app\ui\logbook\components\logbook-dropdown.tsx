'use client'

import { <PERSON><PERSON>ef<PERSON>, XCircle } from 'lucide-react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import { Separator } from '@/components/ui/separator'
import { useQueryState } from 'nuqs'
import { SealogsCogIcon } from '@/app/lib/icons'
import { cn } from '@/app/lib/utils'
import { Button } from '@/components/ui'
import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'

interface MenuItem {
    label: string
    value: string
    icon?: React.ReactNode
    url?: string
}

interface LogbookActionMenuProps {
    items: MenuItem[]
    onBack: () => void
    onDistructAction: () => void
    ShowDistructive?: boolean
    setOpen?: any
    logBookConfig?: any
    backLabel?: string
    disTructLabel?: string
    onSelect?: (value: string) => void
    triggerIcon?: React.ReactNode
}

export function LogbookActionMenu({
    items,
    onBack,
    onDistructAction,
    ShowDistructive = false,
    setOpen,
    logBookConfig,
    backLabel = 'Back to vessel',
    disTructLabel = 'Delete logbook entry',
    onSelect,
    triggerIcon,
}: LogbookActionMenuProps) {
    // Use nuqs to manage the tab state through URL query parameters
    const [tab, setTab] = useQueryState('tab', { defaultValue: 'crew' })
    const [radioLogField, setRadioLogField] = useState<any>()
    const router = useRouter()

    const handleSelect = async (item: MenuItem) => {
        if (item.url) {
            router.push(item.url)
        } else if (onSelect) {
            onSelect(item.value)
        } else {
            if (item.value !== tab) {
                await setTab(item.value)
            }
        }
    }

    useEffect(() => {
        if (logBookConfig) {
            setRadioLogField(
                logBookConfig?.customisedLogBookComponents?.nodes
                    ?.find((node: any) => node.title === 'Trip Log')
                    ?.customisedComponentFields?.nodes?.find(
                        (node: any) => node.fieldName === 'RadioLog',
                    ),
            )
        }
    }, [logBookConfig])

    return (
        <DropdownMenu>
            <DropdownMenuTrigger
                className="h-10 flex items-center"
                asChild={!!triggerIcon}>
                {triggerIcon ? (
                    <Button
                        variant="ghost"
                        onClick={(e) => e.stopPropagation()}
                        size="sm"
                        className="h-8 w-8 p-0">
                        {triggerIcon}
                    </Button>
                ) : (
                    <SealogsCogIcon size={36} />
                )}
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end" className="w-[256px] p-0">
                <div className="text-input flex flex-col items-center justify-center py-[9px]">
                    <DropdownMenuItem
                        hoverEffect={false}
                        onClick={onBack}
                        className={cn(
                            'flex items-center gap-[8px] px-[26px] w-full h-11 rounded-md cursor-pointer focus:bg-accent',
                            'hover:bg-accent hover:px-[6px] hover:w-[233px] hover:border hover:border-border',
                        )}>
                        <ArrowLeft size={16} />
                        <span>{backLabel}</span>
                    </DropdownMenuItem>

                    {items.map((item, index) => (
                        <DropdownMenuItem
                            key={index}
                            onClick={() => handleSelect(item)}>
                            {/* Circle indicator */}
                            {!item.icon ? (
                                <div className="h-full w-fit flex z-10 relative flex-col items-center justify-center">
                                    <div
                                        className={cn(
                                            'border-l -translate-y-[17.5px] absolute inset-y-0 border-wedgewood-200 border-dashed',
                                            index === 0 && 'invisible',
                                        )}
                                    />
                                    <div
                                        className={cn(
                                            'size-[11px] z-10 rounded-full',
                                            'group-hover:border-primary group-hover:bg-curious-blue-200',
                                            tab === item.value
                                                ? 'border border-primary bg-curious-blue-200'
                                                : 'border border-cool-wedgewood-200 bg-outer-space-50',
                                        )}
                                    />
                                </div>
                            ) : (
                                item.icon
                            )}
                            <div
                                className={cn(
                                    'relative z-20',
                                    tab === item.value && !item.url
                                        ? 'font-medium text-accent-foreground'
                                        : '',
                                )}>
                                {item.label}
                            </div>
                        </DropdownMenuItem>
                    ))}
                </div>
                {logBookConfig && radioLogField && (
                    <>
                        {radioLogField.status !== 'Off' && (
                            <>
                                <Separator className="border-border" />
                                <DropdownMenuItem
                                    hoverEffect={false}
                                    className={cn(
                                        'px-[47px] h-[61px] text-text hover:text-foreground flex gap-2.5 group py-[21px] relative focus:bg-outer-space-50 rounded-none',
                                    )}
                                    onClick={() => {
                                        setOpen(true)
                                    }}>
                                    <span className="relative z-20 text-text uppercase hover:text-primary text-sm">
                                        Radio logs
                                    </span>
                                    <div
                                        className={cn(
                                            'absolute w-20 left-12 inset-y-0',
                                            'group-hover:bg-outer-space-50 group-hover:w-full group-hover:left-0',
                                            'will-change-transform will-change-width will-change-padding transform-gpu',
                                            'group-hover:transition-[width,left] group-hover:ease-out group-hover:duration-300',
                                            'outline-none focus:outline-none active:outline-none',
                                        )}
                                    />
                                </DropdownMenuItem>
                            </>
                        )}
                    </>
                )}
                {ShowDistructive && (
                    <>
                        <Separator className="border-border" />
                        <DropdownMenuItem
                            hoverEffect={false}
                            onClick={onDistructAction}
                            className={cn(
                                'group relative h-[61px] px-[26px] py-[21px] cursor-pointer focus:bg-destructive/5 rounded-none text-destructive focus:text-destructive',
                            )}>
                            <div className="relative gap-2.5 flex items-center z-20">
                                <XCircle size={24} />
                                <span>{disTructLabel}</span>
                            </div>
                            <div
                                className={cn(
                                    'absolute w-full h-11 bottom-0 inset-x-0',
                                    'group-hover:bg-destructive/focus:bg-destructive/5 group-hover:h-full',
                                    'will-change-transform will-change-width will-change-padding transform-gpu',
                                    'group-hover:transition-[height,color] group-hover:ease-out group-hover:duration-300',
                                    'outline-none focus:outline-none active:outline-none',
                                )}
                            />
                        </DropdownMenuItem>
                    </>
                )}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
