const TableWrapper = (props: any) => {
    return (
        <table className={` table-auto w-full `} cellPadding="10">
            <thead>
                <tr className={!props.showHeader ? 'hidden' : ''}>
                    {props.headings.map((item: any, index: number) => {
                        return (
                            <th
                                key={index}
                                scope="col"
                                className={`pb-3 pt-6 px-2 ${index === 0 ? 'rounded-tl-lg' : ' '}   ${props.headings.length === index + 1 ? 'rounded-tr-lg' : ' '}
                                ${item.includes(':') ? (item.split(':')[1] === 'last' ? 'rounded-tr-lg' : '') : ''}
                                ${item.includes(':') ? (item.split(':')[1] === 'smhidden' ? 'hidden sm:block' : '') : ''}
                                ${item.includes(':') ? (item.split(':')[1] === 'left' ? 'text-left' : '') : ''}
                                ${item.includes(':') ? (item.split(':')[1] === 'firstHead' ? 'text-left text-nowrap font-thin  md: lg:text-2xl pl-6 rounded-tl-lg' : '') : ''}  `}>
                                {item.includes(':') ? item.split(':')[0] : item}
                            </th>
                        )
                    })}
                </tr>
            </thead>
            <tbody className={`  text-foreground ${props?.bodyClass}`}>
                {props.children}
            </tbody>
        </table>
    )
}

export default TableWrapper
