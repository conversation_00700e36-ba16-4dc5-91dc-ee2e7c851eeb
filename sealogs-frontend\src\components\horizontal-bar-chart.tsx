'use client'

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, YAxis } from 'recharts'

import {
    Chart<PERSON>ontainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart'
import { P } from './ui'

export function HorizontalBarChartComponent({
    chartConfig,
    chartData,
    cardTitle,
    cardInfo,
}: {
    chartConfig: any
    chartData: any
    cardTitle?: string
    cardInfo?: any
}) {
    return (
        <div className="flex flex-col min-h-0 overflow-auto shadow-none max-h-80">
            <div>
                <P>{cardTitle}</P>
                {cardInfo}
            </div>
            <ChartContainer
                config={chartConfig}
                className="flex flex-col overflow-auto size-full">
                <BarChart
                    accessibilityLayer
                    data={chartData}
                    layout="vertical"
                    margin={{
                        left: 33,
                        right: 10,
                    }}
                    barCategoryGap="20%">
                    <YAxis
                        dataKey="title"
                        type="category"
                        tick={{ fontSize: 14, fill: '#71717A' }}
                        tickLine={false}
                        tickMargin={5}
                        axisLine={{
                            stroke: '#CBD3D6',
                            strokeDasharray: '4 2',
                        }}
                        tickFormatter={(value) =>
                            chartConfig[value as keyof typeof chartConfig]
                                ?.label
                        }
                    />
                    <XAxis
                        dataKey="amount"
                        type="number"
                        hide
                        padding={{ left: 10 }}
                    />
                    <ChartTooltip
                        cursor={false}
                        content={
                            <ChartTooltipContent
                                hideLabel
                                hideIndicator
                                className="chartToolTip min-w-0"
                                nameKey="title"
                            />
                        }
                    />
                    <Bar dataKey="amount" layout="vertical" radius={5} />
                </BarChart>
            </ChartContainer>
        </div>
    )
}
