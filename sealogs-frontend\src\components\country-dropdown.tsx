import { useEffect, useState } from 'react'
import { Combobox } from '@/components/ui/comboBox'
import { CountriesList } from '@/app/lib/data'

interface Country {
    value: string
    label: string
}

const CountryDropdown = ({ value = 'NZ', onChange }: any) => {
    const [selectedCountry, setSelectedCountry] = useState<Country | undefined>(
        undefined,
    )

    useEffect(() => {
        let country = CountriesList.find((c) => c.value === 'NZ')
        if (value) {
            country = CountriesList.find((c) => c.value === value)
        }
        setSelectedCountry(country ? country : CountriesList[0])
    }, [value])
    return (
        <Combobox
            options={CountriesList}
            placeholder="Country"
            value={selectedCountry}
            onChange={onChange}
        />
    )
}

export default CountryDropdown
