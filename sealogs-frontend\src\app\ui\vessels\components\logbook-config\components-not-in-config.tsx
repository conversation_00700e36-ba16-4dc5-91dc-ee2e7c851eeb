'use client'

import { isCategorised } from '../../actions'
import { Button } from '@/components/ui/button'
import { LogBookConfiguration } from '@/app/lib/logbook-configuration/types'
import { FilePlusIcon } from 'lucide-react'
import { cn } from '@/app/lib/utils'

interface IComponentProps {
    tab: string
    logBookConfig: any
    slallFields: LogBookConfiguration[]
    fields: any
    imCrew: boolean
    createCustomisedComponent: Function
}

function ComponentsNotInConfig({
    tab,
    logBookConfig,
    slallFields,
    imCrew,
    createCustomisedComponent,
    fields,
}: IComponentProps) {
    return (
        <div className="flex flex-col gap-4 mt-4">
            {fields.map((component: any) => (
                <div
                    key={component.label}
                    className={cn(
                        'grid grid-cols-3 pb-2 border border-border rounded-lg',
                        component.label,
                        !(
                            tab?.replace('Logbook', 'LogBook') ===
                                component.label.replace('Logbook', 'LogBook') ||
                            (component.label === 'Crew Welfare' &&
                                tab === 'Crew Members') ||
                            (tab === 'Engine Reports' &&
                                (component.componentClass ===
                                    'Engineer_LogBookComponent' ||
                                    component.componentClass ===
                                        'Fuel_LogBookComponent'))
                        ) && 'hidden',
                    )}>
                    <div className="col-span-3 md:col-span-1 flex flex-col justify-between bg-background">
                        <div className="p-4 mt-2 font-medium text-xl">
                            <p>{component.label}</p>
                            <p className="mt-2 text-destructive text-lg">
                                Not Exist
                            </p>
                        </div>
                        {!isCategorised(
                            component,
                            slallFields,
                            logBookConfig,
                        ) && (
                            <div className="mt-2 p-4">
                                {!imCrew && (
                                    <Button
                                        variant="primaryOutline"
                                        iconLeft={FilePlusIcon}
                                        color="sky"
                                        onClick={() => {
                                            document.body.style.cursor = 'wait'
                                            createCustomisedComponent({
                                                variables: {
                                                    input: {
                                                        title: component.label,
                                                        sortOrder:
                                                            component.sortOrder ||
                                                            0,
                                                        category:
                                                            component.category,
                                                        customisedLogBookConfigID:
                                                            logBookConfig.id,
                                                        componentClass:
                                                            component.componentClass,
                                                        active: true,
                                                    },
                                                },
                                            })
                                        }}>
                                        {`Add ${component.label}`}
                                    </Button>
                                )}
                            </div>
                        )}
                    </div>
                    <div className="col-span-3 md:col-span-2">
                        <p className="font-medium px-4 py-3 border-b border-border text-lg">
                            Available Fields:
                        </p>
                        <div className="max-h-80 overflow-y-auto">
                            {component.items.map(
                                (field: any, index: number) => (
                                    <div
                                        key={field.value + '_' + index}
                                        className={
                                            'border-b border-border py-3 px-4'
                                        }>
                                        {field.label}
                                    </div>
                                ),
                            )}
                        </div>
                    </div>
                </div>
            ))}
        </div>
    )
}

export { ComponentsNotInConfig }
