'use client'

import { cn } from '@/app/lib/utils'
import { Button, RadioGroup, RadioGroupItem } from '@/components/ui'
import { MessageSquareIcon, PencilIcon } from 'lucide-react'

interface IProps {
    title: string
    field: any
    isDisabled?: boolean
    className?: string
    value?: string
    onUpdateField: (field: any, status: string) => void
    onDescriptionClick?: (title: string, description: any) => void
    onCustomizeClick?: (field: any) => void
}

export function GroupFieldHeaderCheck({
    title,
    field,
    className,
    value,
    onUpdateField,
    onDescriptionClick,
    onCustomizeClick,
    isDisabled,
}: IProps) {
    return (
        <div className={cn('flex justify-between items-start', className)}>
            <div className="flex flex-col gap-3">
                <div className="flex gap-2 items-center font-medium text-xl">
                    {title}{' '}
                    {field?.description && (
                        <Button
                            variant={'text'}
                            size={'icon'}
                            onClick={() =>
                                onDescriptionClick &&
                                onDescriptionClick(title, field?.description)
                            }>
                            <MessageSquareIcon />
                        </Button>
                    )}
                </div>
                <RadioGroup
                    variant="horizontal"
                    className={cn({
                        'opacity-60': isDisabled,
                    })}
                    gap={'large'}
                    value={value}
                    onValueChange={(value) => {
                        onUpdateField(
                            field,
                            value === 'yes' ? 'Required' : 'Off',
                        )
                    }}
                    disabled={isDisabled}>
                    {/* No Option - Destructive */}
                    <div className="flex gap-2">
                        <RadioGroupItem
                            value="no"
                            id={`${title}-group-no_radio`}
                            variant="destructive"
                            size="lg"
                        />
                        <div className="font-medium text-destructive">No</div>
                    </div>

                    {/* Yes Option - Success */}
                    <div className="flex gap-2">
                        <RadioGroupItem
                            value="yes"
                            id={`${title}-group-yes_radio`}
                            variant="success"
                            size="lg"
                        />
                        <div className="font-medium text-bright-turquoise-600">
                            Yes
                        </div>
                    </div>
                </RadioGroup>
            </div>
            <Button
                size={'icon'}
                variant={'text'}
                onClick={() => onCustomizeClick && onCustomizeClick(field)}>
                <PencilIcon />
            </Button>
        </div>
    )
}
