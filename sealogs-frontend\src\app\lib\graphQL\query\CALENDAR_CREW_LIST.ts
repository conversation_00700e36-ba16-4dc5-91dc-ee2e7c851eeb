import gql from 'graphql-tag'

export const CALENDAR_CREW_LIST = gql`
    query ReadSeaLogsMembers(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SeaLogsMemberFilterFields = {}
    ) {
        readSeaLogsMembers(
            limit: $limit
            offset: $offset
            filter: $filter
            sort: { firstName: ASC, surname: <PERSON><PERSON> }
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                trainingSessionsDue {
                    nodes {
                        id
                        dueDate
                        memberID
                        member {
                            id
                            firstName
                            surname
                        }
                        vesselID
                        vessel {
                            id
                            title
                        }
                        trainingTypeID
                        trainingType {
                            id
                            title
                        }
                    }
                }
            }
        }
    }
`
