'use client'

// React and hooks
import { useEffect, useState } from 'react'

// Apollo GraphQL
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_RADIO_LOGS } from '@/app/lib/graphQL/query'
import { CREATE_RADIO_LOG, UPDATE_RADIO_LOG } from '@/app/lib/graphQL/mutation'

// Shadcn UI components
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Input } from '@/components/ui/input'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import {
    Sheet,
    SheetBody,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from '@/components/ui/sheet'
import { P } from '@/components/ui/typography'

// Icons
import { Plus } from 'lucide-react'

// Other components
import RadioTimeField from './components/radioTimeField'
import dayjs from 'dayjs'
export default function RadioLogsScheduleService({
    open,
    setOpen,
    currentTrip,
}: {
    open: any
    setOpen: any
    currentTrip: any
}) {
    const [radioLogs, setRadioLogs] = useState<any>([])
    const [defaultRadioLogs, setDefaultRadioLogs] = useState<any>(false)
    const [displayLogAlert, setDisplayLogAlert] = useState(false)
    const [radioTitle, setRadioTitle] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentLog, setCurrentLog] = useState<any>(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)

    const [getRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setRadioLogs(data)
                if (defaultRadioLogs == false) {
                    getDefaultRadioLogs({
                        variables: {
                            filter: {
                                tripScheduleServiceID: {
                                    eq: +currentTrip.tripReportSchedule
                                        .tripScheduleServiceID,
                                },
                            },
                        },
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    const [getDefaultRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setDefaultRadioLogs(true)
                if (data.length > 0) {
                    const logsToCreate = data
                        .filter(
                            (defaultLog: any) =>
                                !radioLogs.some(
                                    (log: any) =>
                                        log.defaultParent == defaultLog.id,
                                ),
                        )
                        .map((defaultLog: any) => ({
                            title: defaultLog.title,
                            tripReport_LogBookEntrySectionID: currentTrip.id,
                            defaultParent: +defaultLog.id,
                        }))

                    logsToCreate.forEach((log: any) => {
                        createRadioLog({
                            variables: {
                                input: {
                                    tripReport_LogBookEntrySectionID:
                                        +currentTrip.id,
                                    title: log.title,
                                    defaultParent: +log.defaultParent,
                                    order: log.order,
                                },
                            },
                        })
                    })
                }
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    useEffect(() => {
        if (currentTrip.id) {
            getRadioLogs({
                variables: {
                    filter: {
                        tripReport_LogBookEntrySectionID: {
                            eq: currentTrip.id,
                        },
                    },
                },
            })
        }
    }, [])

    useEffect(() => {
        if (defaultRadioLogs && defaultRadioLogs.length > 0) {
        }
    }, [defaultRadioLogs])

    const [createRadioLog] = useMutation(CREATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            tripReport_LogBookEntrySectionID: {
                                eq: currentTrip.id,
                            },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('createRadioLog error', error)
        },
    })

    const [updateRadioLog] = useMutation(UPDATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            tripReport_LogBookEntrySectionID: {
                                eq: currentTrip.id,
                            },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('updateRadioLog error', error)
        },
    })

    const handleAddRadioLog = () => {
        setDisplayLogAlert(false)
        if (currentLog) {
            updateRadioLog({
                variables: {
                    input: {
                        id: currentLog.id,
                        title: radioTitle,
                    },
                },
            })
        } else {
            createRadioLog({
                variables: {
                    input: {
                        tripReport_LogBookEntrySectionID: +currentTrip.id,
                        title: radioTitle,
                    },
                },
            })
        }
    }

    const handleLogCheck = (log: any, time: any) => {
        updateRadioLog({
            variables: {
                input: {
                    id: log.id,
                    time: dayjs(time).format('YYYY-MM-DD HH:mm:ss'),
                },
            },
        })
    }

    return (
        <>
            <Sheet open={open} onOpenChange={setOpen}>
                <SheetContent side="right" className="w-[60%] p-0 border-l">
                    <div className="h-full flex flex-col">
                        <SheetHeader>
                            <SheetTitle>Radio Logs</SheetTitle>
                        </SheetHeader>
                        <SheetBody>
                            {radioLogs && radioLogs.length > 0 ? (
                                <div className="w-full">
                                    {radioLogs.map((log: any) => (
                                        <div
                                            key={log.id}
                                            className="flex flex-row gap-2 mb-2 justify-between items-center">
                                            <span className="text-sm lg:text-base">
                                                <Button
                                                    variant="ghost"
                                                    onClick={() => {
                                                        setDisplayLogAlert(true)
                                                        setCurrentLog(log)
                                                    }}>
                                                    {log.title}
                                                </Button>
                                            </span>
                                            <div className="flex flex-row gap-2">
                                                <RadioTimeField
                                                    log={log}
                                                    handleTimeChange={
                                                        handleLogCheck
                                                    }
                                                />
                                                <Button
                                                    variant="text"
                                                    className="text-red-500"
                                                    onClick={() => {
                                                        updateRadioLog({
                                                            variables: {
                                                                input: {
                                                                    id: log.id,
                                                                    tripReport_LogBookEntrySectionID: 0,
                                                                },
                                                            },
                                                        })
                                                    }}>
                                                    Delete
                                                </Button>
                                            </div>
                                        </div>
                                    ))}
                                </div>
                            ) : (
                                <P className="text-muted-foreground flex justify-center items-start h-full">
                                    No Radio Logs
                                </P>
                            )}
                            <Button
                                variant="text"
                                iconLeft={Plus}
                                className="ml-2"
                                onClick={() => {
                                    setDisplayLogAlert(true)
                                    setCurrentLog(false)
                                }}>
                                Add Radio Log
                            </Button>
                        </SheetBody>
                    </div>
                </SheetContent>
            </Sheet>

            <AlertDialogNew
                openDialog={displayLogAlert}
                setOpenDialog={setDisplayLogAlert}
                handleCreate={handleAddRadioLog}
                actionText={currentLog ? 'Update' : 'Create'}
                title={`${currentLog ? 'Edit' : 'Create'} Radio Log`}>
                <Label htmlFor="radioLogTitle">
                    Title
                    <Input
                        id="radioLogTitle"
                        placeholder="Enter Location/Title"
                        defaultValue={currentLog?.title}
                        required
                        onChange={(e) => {
                            setRadioTitle(e.target.value)
                        }}
                    />
                </Label>
            </AlertDialogNew>

            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={() => {
                    updateRadioLog({
                        variables: {
                            input: {
                                id: currentLog.id,
                                comment: currentComment,
                            },
                        },
                    })
                    setOpenCommentAlert(false)
                }}
                title="Comment"
                actionText="Update">
                <Label htmlFor="radioLogComment">
                    Comment
                    <Textarea
                        id="radioLogComment"
                        placeholder="Enter Comment"
                        defaultValue={currentComment}
                        required
                        onChange={(e) => {
                            setCurrentComment(e.target.value)
                        }}
                    />
                </Label>
            </AlertDialogNew>
        </>
    )
}
