'use client'
import DepartmentList from '@/app/ui/department/list'
import { useRouter } from 'next/navigation'
import { use, useEffect, useState } from 'react'
import { preventCrewAccess } from '../helpers/userHelper'
import Loading from '../loading'
import SeaLogsButton from '@/components/ui/sea-logs-button'
import { H2 } from '@/components/ui'

const DepartmentPage = () => {
    const router = useRouter()
    const [useDepartment, setUseDepartment] = useState<any>(null)
    useEffect(() => {
        preventCrewAccess()
        if (localStorage.getItem('useDepartment') === 'true') {
            setUseDepartment(true)
        } else {
            setUseDepartment(false)
        }
    }, [])
    return (
        <>
            {useDepartment ? (
                <div className="w-full p-0 ">
                    <div className="flex flex-col md:flex-row md:justify-between pb-4 items-center">
                        <H2 className="text-3xl">Departments</H2>
                        <div className="flex flex-col md:flex-row items-center">
                            <SeaLogsButton
                                // link={`/department/create`}
                                action={() => router.push('/department/create')}
                                color="sllightblue"
                                type="primary"
                                text="Add Department"
                            />
                        </div>
                    </div>
                    <DepartmentList />
                </div>
            ) : (
                <>
                    {useDepartment === null ? (
                        <Loading />
                    ) : (
                        <Loading message="Departments are not enabled, please enable the departments from settings to use departments." />
                    )}
                </>
            )}
        </>
    )
}

export default DepartmentPage
