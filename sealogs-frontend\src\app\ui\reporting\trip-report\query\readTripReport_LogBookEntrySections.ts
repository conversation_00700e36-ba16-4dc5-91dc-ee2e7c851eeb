import gql from 'graphql-tag'

export const ReadTripReport_LogBookEntrySections = gql`
    query ReadTripReport_LogBookEntrySections(
        $filter: TripReport_LogBookEntrySectionFilterFields = {}
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readTripReport_LogBookEntrySections(
            filter: $filter
            limit: $limit
            offset: $offset
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                tripScheduleDepartTime
                departTime
                tripScheduleArriveTime
                paxJoinedAdult
                arriveTime
                pob
                vob
                comment
                fromLocation {
                    id
                    title
                }
                toLocation {
                    id
                    title
                }
                logBookEntry {
                    startDate
                    vehicle {
                        title
                    }
                }
                tripReportScheduleID
                tripReportSchedule {
                    transitTripID
                }
                tripReport_Stops {
                    nodes {
                        paxJoined
                        paxDeparted
                        arriveTime
                        departTime
                        stopLocation {
                            title
                        }
                    }
                }
            }
        }
    }
`
