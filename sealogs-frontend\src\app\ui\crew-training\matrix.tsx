'use client'

import VesselDropdown from '@/components/filter/components/vessel-dropdown'
import { GetCrewListWithTrainingStatus } from '@/app/lib/actions'
import { CREW_BRIEF_LIST, CREW_TRAINING_TYPES } from '@/app/lib/graphQL/query'
import {
    Avatar,
    AvatarFallback,
    Card,
    CardContent,
    CardHeader,
    getCrewInitials,
    ScrollArea,
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'
import { useLazyQuery } from '@apollo/client'
import { debounce, filter } from 'lodash'
import { Dot, X } from 'lucide-react'
import { useEffect, useState } from 'react'

interface ITrainingType {
    id: number
    title: string
}

interface ISelectOption {
    value: string
    label: string
}

export default function CrewTrainingMatrix() {
    const [trainingTypes, setTrainingTypes] = useState<ITrainingType[]>([])
    const [crewList, setCrewList] = useState<any>([])
    const [selectedVessels, setSelectedVessels] = useState<ISelectOption[]>([])

    const debouncedOnVesselsChange = debounce(setSelectedVessels, 500)

    const [queryTrainingTypeList] = useLazyQuery(CREW_TRAINING_TYPES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('queryTrainingTypeList error', error)
        },
    })

    const [queryCrewMembers] = useLazyQuery(CREW_BRIEF_LIST, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {},
        onError: (error: any) => {
            console.error('queryCrewMembers error', error)
        },
    })

    const loadData = async () => {
        let filterTrainingTypes = {}
        let filterCrew = {}

        if (selectedVessels.length > 0) {
            filterTrainingTypes = {
                vessels: {
                    id: {
                        in: selectedVessels.map((item) => item.value),
                    },
                },
            }

            filterCrew = {
                vehicles: {
                    id: {
                        in: selectedVessels.map((item) => item.value),
                    },
                },
            }
        }

        const trainingTypesResult = await queryTrainingTypeList({
            variables: {
                filter: filterTrainingTypes,
            },
        })

        const trainingTypesData = (
            trainingTypesResult.data?.readTrainingTypes.nodes ?? []
        ).map((val: any) => ({
            id: val.id,
            title: val.title,
        }))
        setTrainingTypes(trainingTypesData)

        const membersResult = await queryCrewMembers({
            variables: {
                filter: filterCrew,
            },
        })
        const transformedCrewList = GetCrewListWithTrainingStatus(
            membersResult.data?.readSeaLogsMembers.nodes ?? [],
            [],
        )

        setCrewList(transformedCrewList)
    }

    useEffect(() => {
        loadData()
    }, [selectedVessels])

    return (
        <Card>
            <div className="mb-6 flex flex-row gap-4 items-center">
                <p>Filter By: </p>
                <VesselDropdown
                    value={selectedVessels}
                    onChange={(selected: ISelectOption[]) =>
                        debouncedOnVesselsChange(selected)
                    }
                />
            </div>
            <Table className="w-ful">
                <TableHeader>
                    <TableRow>
                        <TableHead className="w-20">&nbsp;</TableHead>
                        {trainingTypes.map((type) => {
                            return (
                                <TableHead
                                    key={'training-type-header-' + type.id}
                                    className="relative h-[200px] w-20">
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <div
                                                className="origin-bottom-left -rotate-[75deg] whitespace-nowrap absolute bottom-1 left-[18px] text-primary"
                                                title={type.title}>
                                                {type.title}
                                            </div>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            {type.title}
                                        </TooltipContent>
                                    </Tooltip>
                                </TableHead>
                            )
                        })}
                    </TableRow>
                </TableHeader>
            </Table>
            <ScrollArea className="h-[800px]">
                <Table>
                    <TableBody>
                        {crewList.map((crew: any) => (
                            <TableRow key={crew.id}>
                                <TableCell className="p-1 w-20">
                                    <Tooltip>
                                        <TooltipTrigger>
                                            <Avatar
                                                size="sm"
                                                variant={
                                                    crew.trainingStatus
                                                        ?.label !== 'Good'
                                                        ? 'destructive'
                                                        : 'success'
                                                }>
                                                <AvatarFallback className="text-sm">
                                                    {getCrewInitials(
                                                        crew.firstName,
                                                        crew.surname,
                                                    )}
                                                </AvatarFallback>
                                            </Avatar>
                                        </TooltipTrigger>
                                        <TooltipContent>
                                            {crew.firstName}{' '}
                                            {crew.surname ?? ''}
                                        </TooltipContent>
                                    </Tooltip>
                                </TableCell>
                                {trainingTypes.map((trainingType) => (
                                    <TableCell
                                        className="w-20"
                                        key={'value-type-' + trainingType.id}>
                                        <Tooltip>
                                            <TooltipTrigger>
                                                <StatusBadge
                                                    trainingTypeID={
                                                        trainingType.id
                                                    }
                                                    crewMemberData={crew}
                                                />
                                            </TooltipTrigger>
                                            <TooltipContent>
                                                <div className="flex gap-1 text-sm">
                                                    <span>Crew:</span>
                                                    <span>
                                                        {crew.firstName}{' '}
                                                        {crew.surname ?? ''}
                                                    </span>
                                                </div>
                                                <div className="flex gap-1 text-sm">
                                                    <span>Training:</span>
                                                    <span>
                                                        {trainingType.title}
                                                    </span>
                                                </div>
                                            </TooltipContent>
                                        </Tooltip>
                                    </TableCell>
                                ))}
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </ScrollArea>
        </Card>
    )
}

const StatusBadge = ({
    trainingTypeID,
    crewMemberData,
}: {
    trainingTypeID: number
    crewMemberData: any
}) => {
    const hasTrainingDues = crewMemberData.trainingStatus.dues.some(
        (training: any) => training.trainingTypeID == trainingTypeID,
    )

    if (hasTrainingDues) {
        return (
            <div
                className={`text-accent-foreground border bg-red-vivid-100 border-red-vivid-500 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                <X className="text-destructive" />
            </div>
        )
    }

    const hasCompletedTraining = crewMemberData.trainingSessions.nodes.some(
        (trainingSession: any) => {
            return trainingSession.trainingTypes.nodes.some(
                (trainingType: any) => trainingType.id == trainingTypeID,
            )
        },
    )

    if (hasCompletedTraining) {
        return (
            <div
                className={`text-accent-foreground border bg-bright-turquoise-100 border-bright-turquoise-600 items-center justify-center p-2 rounded-full flex w-8 h-8`}>
                <svg
                    className={`h-5 w-5`}
                    viewBox="0 0 20 20"
                    fill="#27AB83"
                    aria-hidden="true">
                    <path
                        fillRule="evenodd"
                        d="M16.704 4.153a.75.75 0 01.143 1.052l-8 10.5a.75.75 0 01-1.127.075l-4.5-4.5a.75.75 0 011.06-1.06l3.894 3.893 7.48-9.817a.75.75 0 011.05-.143z"
                        clipRule="evenodd"
                    />
                </svg>
            </div>
        )
    }

    return (
        <div
            className={`text-accent-foreground border bg-outer-space-50 border-border items-center justify-center rounded-full flex w-8 h-8`}>
            <div className="rounded-full size-3 bg-background0"></div>
        </div>
    )
}
