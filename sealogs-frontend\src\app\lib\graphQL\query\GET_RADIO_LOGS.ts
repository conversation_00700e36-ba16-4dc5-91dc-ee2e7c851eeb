import gql from 'graphql-tag'

export const GET_RADIO_LOGS = gql`
    query GetRadioLogs($filter: RadioLogFilterFields = {}) {
        readRadioLogs(filter: $filter, sort: { order: ASC }) {
            nodes {
                id
                title
                status
                comment
                time
                vesselID
                logBookEntryID
                tripScheduleServiceID
                defaultParent
                order
            }
        }
    }
`
