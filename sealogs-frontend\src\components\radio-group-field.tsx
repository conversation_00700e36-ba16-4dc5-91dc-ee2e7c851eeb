'use client'

import { useEffect, useState } from 'react'

const RadioGroupField = (props: any) => {
    const [yes, setYes] = useState(props.checked)
    const [No, setNo] = useState(!props.checked)

    useEffect(() => {
        setYes(props.checked)
        setNo(!props.checked)
    }, [props.checked])

    function callyes() {
        props.handleYesChange()
        setYes(true)
        setNo(false)
    }
    function callNo() {
        props.handleNoChange()
        setNo(true)
        setYes(false)
    }
    const classes = {
        fieldWrapper:
            'flex flex-row gap-2 my-4 text-left items-center justify-between',
        inputWrapper: 'flex flex-row gap-2 justify-start items-center',
        radio: 'flex flex-row gap-1 items-center text-sm lg:text-base',
        radioInput: 'w-6 h-6 bg-slblue-100 border-slblue-200',
        radioLabel: 'text-2xs',
        textarea:
            'block p-2.5 w-full mt-4 text-sm text-slblue-000 bg-slblue-50 rounded-lg border border-slblue-400 focus:ring-slblue-500 focus:border-slblue-500',
        label: `font-light text-2xs uppercase font-inter`,
    }
    return (
        <div
            className={`dailyCheckField ${classes.fieldWrapper} ${props.className}`}>
            <div className={`${classes.inputWrapper}`}>
                <div
                    className={`${classes.radio} ${props.locked ? 'pointer-events-none opacity-60' : ''}`}>
                    <div className="md:w-8 md:h-8 w-6 h-6">
                        {No ? (
                            <div className="bg-slblue-50 border-sky-400 ring-1 ring-slblue-200 p-0.5 rounded-full h-full">
                                <div className="bg-sky-700 ring-1 ring-slblue-200 rounded-full h-full"></div>
                            </div>
                        ) : (
                            <div
                                className="bg-slblue-50 border-sky-400 ring-1 ring-slblue-200 p-0.5 rounded-full h-full"
                                onClick={() => callNo()}>
                                <div className="bg-white ring-1 ring-slblue-200 rounded-full h-full"></div>
                            </div>
                        )}
                    </div>
                    <input
                        id={`${props.inputId}-no_radio`}
                        type="radio"
                        name={`${props.inputId}-radio`}
                        onChange={() => callNo()}
                        className={classes.radioInput + ' ' + 'hidden'}
                        defaultChecked={props.defaultNoChecked}
                    />
                    <label
                        htmlFor={`${props.inputId}-no_radio`}
                        className={classes.radioLabel + ' ' + 'cursor-pointer'}>
                        {props.noLabel}
                    </label>
                </div>
                <div
                    className={`${classes.radio} ${props.locked ? 'pointer-events-none opacity-60' : ''}`}>
                    <div className="md:w-8 md:h-8 w-6 h-6">
                        {yes ? (
                            <div className="bg-slblue-50 border-sky-400 ring-1 ring-slblue-200 p-0.5 rounded-full h-full">
                                <div className="bg-sky-700 ring-1 ring-slblue-200 rounded-full h-full"></div>
                            </div>
                        ) : (
                            <div
                                className="bg-slblue-50 border-sky-400 ring-1 ring-slblue-200 p-0.5 rounded-full h-full"
                                onClick={() => callyes()}>
                                <div className="bg-white ring-1 ring-slblue-200 rounded-full h-full"></div>
                            </div>
                        )}
                    </div>
                    <input
                        id={`${props.inputId}-yes_radio`}
                        type="radio"
                        name={`${props.inputId}-radio`}
                        onChange={() => callyes()}
                        className={classes.radioInput + ' ' + 'hidden'}
                        defaultChecked={props.defaultYesChecked}
                    />
                    <label
                        htmlFor={`${props.inputId}-yes_radio`}
                        className={classes.radioLabel + ' ' + 'cursor-pointer'}>
                        {props.yesLabel}
                    </label>
                </div>
            </div>
        </div>
    )
}

export default RadioGroupField
