'use client'

import { useTheme } from 'next-themes'
import { useEffect, useState } from 'react'

export function ThemeSwitcher() {
    const [mounted, setMounted] = useState(false)
    const { theme, setTheme } = useTheme()

    useEffect(() => {
        setMounted(true)
    }, [])

    if (!mounted) return null

    return (
        <div className=" ">
            <button
                className={`${theme === 'light' ? 'hidden' : ''} flex`}
                onClick={() => setTheme('light')}>
                Light mode
            </button>
        </div>
    )
}
