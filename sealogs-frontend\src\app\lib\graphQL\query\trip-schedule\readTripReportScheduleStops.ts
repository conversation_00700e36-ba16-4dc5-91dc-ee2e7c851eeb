import gql from 'graphql-tag'

export const ReadTripReportScheduleStops = gql`
    query ReadTripReportScheduleStops(
        $filter: TripReportScheduleStopFilterFields
        $limit: Int = 100
        $offset: Int = 0
    ) {
        readTripReportScheduleStops(
            filter: $filter
            limit: $limit
            offset: $offset
        ) {
            pageInfo {
                totalCount
                hasNextPage
                hasPreviousPage
            }
            nodes {
                id
                title
                arriveTime
                departTime
                pickUp
                dropOff
                stopLocationID
                stopLocation {
                    id
                    title
                }
            }
        }
    }
`
