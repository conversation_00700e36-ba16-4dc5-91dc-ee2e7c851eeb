'use client'

import React from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { FooterWrapper } from '@/components/footer-wrapper'
import { ArrowLeft, Plus, Save, Check } from 'lucide-react'
import { cn } from '@/app/lib/utils'

export interface ActionFooterProps {
    /**
     * Whether to show the footer based on permissions
     */
    showFooter?: boolean

    /**
     * Whether to show the Create Task button
     */
    showCreateTask?: boolean
    /**
     * Whether to show the Save button
     */
    showSave?: boolean
    /**
     * Custom text for the Cancel button
     */
    cancelText?: string
    /**
     * Custom text for the Create Task button
     */
    createTaskText?: string
    /**
     * Custom text for the Save button
     */
    saveText?: string
    /**
     * Custom icon for the Cancel button
     */
    cancelIcon?: 'x' | 'arrowLeft'
    /**
     * Custom icon for the Create Task button
     */
    createTaskIcon?: 'plus' | 'check'
    /**
     * Custom icon for the Save button
     */
    saveIcon?: 'save' | 'check'
    /**
     * Loading state for the Create Task button
     */
    createTaskLoading?: boolean
    /**
     * Loading state for the Save button
     */
    saveLoading?: boolean
    /**
     * Disabled state for the Create Task button
     */
    createTaskDisabled?: boolean
    /**
     * Disabled state for the Save button
     */
    saveDisabled?: boolean
    /**
     * Callback for the Cancel button
     */
    onCancel?: () => void
    /**
     * Callback for the Create Task button
     */
    onCreateTask?: () => void
    /**
     * Callback for the Save button
     */
    onSave?: () => void
    /**
     * Additional CSS classes for the footer
     */
    className?: string
    /**
     * Additional CSS classes for the parent container
     */
    parentClassName?: string
    /**
     * Whether to show a border
     */
    noBorder?: boolean
    /**
     * Bottom position
     */
    bottom?: string
    /**
     * Additional props to pass to the FooterWrapper
     */
    footerWrapperProps?: React.ComponentProps<typeof FooterWrapper>
    /**
     * Children to render in the footer (will be rendered after the buttons)
     */
    children?: React.ReactNode
}

/**
 * ActionFooter component for consistent footer actions across pre-departure checks
 */
export function ActionFooter({
    showFooter = true,
    showCreateTask = true,
    showSave = false,
    cancelText = 'Cancel',
    createTaskText = 'Create Task',
    saveText = 'Save',
    cancelIcon = 'arrowLeft',
    createTaskIcon = 'plus',
    saveIcon = 'save',
    createTaskLoading = false,
    saveLoading = false,
    createTaskDisabled = false,
    saveDisabled = false,
    onCancel,
    onCreateTask,
    onSave,
    className,
    parentClassName,
    noBorder,
    bottom,
    footerWrapperProps,
    children,
}: ActionFooterProps) {
    // If footer shouldn't be shown, return null
    if (!showFooter) {
        return null
    }

    // Cancel handler calls the provided onCancel function
    const handleCancel = () => {
        if (onCancel) {
            onCancel()
        }
    }

    // Get the appropriate icon component for each button
    const getCancelIcon = () => {
        switch (cancelIcon) {
            case 'arrowLeft':
                return ArrowLeft
            case 'x':
            default:
                return ArrowLeft
        }
    }

    const getCreateTaskIcon = () => {
        switch (createTaskIcon) {
            case 'check':
                return Check
            case 'plus':
            default:
                return Plus
        }
    }

    const getSaveIcon = () => {
        switch (saveIcon) {
            case 'check':
                return Check
            case 'save':
            default:
                return Save
        }
    }

    return (
        <FooterWrapper
            noBorder={noBorder}
            bottom={bottom}
            parentClassName={cn('', parentClassName)}
            className={className}
            {...footerWrapperProps}>
            {onCancel && (
                <Button
                    variant="back"
                    iconLeft={<ArrowLeft />}
                    onClick={handleCancel}>
                    {cancelText}
                </Button>
            )}

            {showCreateTask && (
                <Button
                    variant="primaryOutline"
                    iconLeft={getCreateTaskIcon()}
                    onClick={onCreateTask}
                    isLoading={createTaskLoading}
                    disabled={createTaskDisabled || createTaskLoading}>
                    {createTaskText}
                </Button>
            )}

            {showSave && (
                <Button
                    iconLeft={getSaveIcon()}
                    onClick={onSave}
                    isLoading={saveLoading}
                    disabled={saveDisabled || saveLoading}>
                    {saveText}
                </Button>
            )}

            {children}
        </FooterWrapper>
    )
}
