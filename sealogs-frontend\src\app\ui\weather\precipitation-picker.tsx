'use client'

import { precipitations } from '@/app/helpers/weatherHelper'

const DropletSVG = ({ fill }: { fill: any }) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={50}
        height={50}
        viewBox="0 0 43.025 43.023">
        <path
            d="M18.167,42.508a17.407,17.407,0,0,1-4.258-.522,17.725,17.725,0,0,1-.267-34.333L40.1.564A1.909,1.909,0,0,1,42.435,2.9l-7.09,26.454A17.822,17.822,0,0,1,18.167,42.508Z"
            transform="translate(0.011 0.015)"
            fill={fill}
            stroke="#035388"
            strokeMiterlimit="10"
            strokeWidth="1"
        />
    </svg>
)

interface IProps {
    selected: any
    onChange: (value: any) => void
}

const PrecipitationPicker = ({ selected, onChange }: IProps) => {
    return (
        <div className="flex w-full justify-between md:justify-start md:gap-4">
            {precipitations.map((precipitation, index) => {
                const selectedIndex = precipitations.findIndex(
                    (p) => p.value === selected.value,
                )
                const fillColor =
                    index <= selectedIndex ? precipitation.fillColor : '#FFFFFF'

                return (
                    <button
                        key={precipitation.value}
                        type="button"
                        onClick={() => onChange(precipitation)}>
                        <DropletSVG fill={fillColor} />
                    </button>
                )
            })}
        </div>
    )
}

export default PrecipitationPicker
