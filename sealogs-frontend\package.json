{"name": "sealogs", "version": "3.4.0", "main": "main.js", "author": "SouthInc <<EMAIL>>", "description": "Maritime Fleet Management Software", "scripts": {"dev": "next dev", "build": "rm -rf .next && NODE_OPTIONS='--max-old-space-size=4096' next build", "start": "next start", "lint": "next lint", "dev:electron-concurrent": "concurrently -n \"NEXT,ELECTRON\" -c \"yellow,blue\" --kill-others \"next dev\" \"electron .\"", "dev:electron": "next build && electron .", "build:electron": "next build && electron-builder", "find-deadcode": "ts-prune"}, "dependencies": {"@apollo/client": "^3.9.0-alpha.3", "@apollo/experimental-nextjs-app-support": "0.7.0", "@as-integrations/next": "^3.0.0", "@capacitor/android": "^6.0.0", "@capacitor/camera": "^6.0.0", "@capacitor/core": "^6.0.0", "@capacitor/ios": "^6.0.0", "@ducanh2912/next-pwa": "^10.2.8", "@emotion/styled": "^11.11.0", "@fullcalendar/core": "^6.1.13", "@fullcalendar/daygrid": "^6.1.13", "@fullcalendar/list": "^6.1.13", "@fullcalendar/react": "^6.1.13", "@hookform/resolvers": "^3.10.0", "@hotjar/browser": "^1.0.9", "@mantine/hooks": "^8.0.0", "@mui/x-date-pickers": "^6.18.7", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-scroll-area": "^1.2.3", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.2", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@radix-ui/react-toast": "^1.2.6", "@radix-ui/react-tooltip": "^1.1.8", "@reactuses/core": "^5.0.21", "@tanstack/react-table": "^8.21.2", "@userback/react": "^0.3.4", "android": "^0.0.8", "apollo3-cache-persist": "^0.15.0", "aws-sdk": "^2.1692.0", "axios": "^1.6.5", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.4", "date-fns": "^3.6.0", "dayjs": "^1.11.11", "dexie": "^3.2.7", "electron-context-menu": "^3.6.1", "electron-serve": "^1.3.0", "graphql": "^16.8.1", "graphql-tag": "^2.12.6", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "jspdf-autotable": "^3.8.4", "jwt-decode": "^4.0.0", "leaflet": "^1.9.4", "leaflet-defaulticon-compatibility": "^0.1.2", "lodash": "^4.17.21", "lucide-react": "^0.474.0", "next": "^14.0.4", "next-themes": "^0.2.1", "nuqs": "^2.4.3", "react": "^18", "react-aria-components": "^1.0.1", "react-day-picker": "8.10.1", "react-dom": "^18", "react-gauge-component": "^1.2.21", "react-hook-form": "^7.54.2", "react-leaflet": "^4.2.1", "react-quill": "^2.0.0", "react-select": "^5.8.0", "react-signature-canvas": "^1.0.6", "recharts": "^2.15.1", "swiper": "^11.0.6", "tailwind-merge": "^3.0.1", "tailwindcss-animate": "^1.0.7", "yet-another-react-lightbox": "^3.19.0", "zod": "^3.24.1"}, "devDependencies": {"@capacitor/assets": "^3.0.5", "@capacitor/cli": "^6.0.0", "@types/leaflet": "^1.9.12", "@types/lodash": "^4.14.202", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "@types/react-signature-canvas": "^1.0.5", "@types/uuid": "^9.0.8", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "electron": "^29.1.0", "electron-builder": "^24.13.3", "eslint": "^8", "eslint-config-next": "14.0.4", "postcss": "^8", "prettier": "3.2.5", "tailwindcss": "^3.3.0", "ts-prune": "^0.10.3", "typescript": "^5"}}