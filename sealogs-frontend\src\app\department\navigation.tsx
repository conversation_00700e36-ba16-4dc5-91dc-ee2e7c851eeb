import { But<PERSON>, <PERSON><PERSON>, DialogTrigger, Popover } from 'react-aria-components'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { SealogsMaintenanceIcon } from '../lib/icons/SealogsMaintenanceIcon'
import { SealogsReportsIcon } from '../lib/icons/SealogsReportsIcon'
import { usePathname } from 'next/navigation'
import { getPermissions, hasPermission } from '../helpers/userHelper'
import { SealogsVesselsIcon } from '../lib/icons'

export default function Navigation(props: any) {
    const pathname = usePathname()
    const [toggleSettings, setToggleSettings] = useState(
        pathname.includes('/settings/crew-duty/list') ||
            pathname.includes('/settings/inventory/category')
            ? true
            : false,
    )
    const [toggleCrew, setToggleCrew] = useState(
        (pathname.includes('/crew') ||
            pathname.includes('/crew-training') ||
            pathname.includes('/training-type')) &&
            !pathname.includes('/settings')
            ? true
            : false,
    )
    const [toggleHnS, setToggleHnS] = useState(
        (pathname.includes('/health-and-safety') ||
            pathname.includes('/risk-evaluations') ||
            pathname.includes('/risk-strategies') ||
            pathname.includes('/incident-records')) &&
            !pathname.includes('/settings')
            ? true
            : false,
    )
    const [toggleVessel, setToggleVessel] = useState(
        pathname.includes('/vessel') || pathname.includes('/info')
            ? true
            : false,
    )
    const [toggleInventory, setToggleInventory] = useState(
        (pathname.includes('/inventory') ||
            pathname.includes('/inventory/suppliers')) &&
            !pathname.includes('/settings')
            ? true
            : false,
    )
    const classes = {
        button: 'peer flex  p-1.5 xl:pl-2 focus-visible:outline-none items-center justify-center md:justify-start',
        icons: 'inline-block',
        navigation: 'hidden xl:inline-block   ',
        active: '  rounded-md my-4',
        link: '  rounded-md  my-4',
        innerMenu: 'border-l  border-solid lg:ml-4 w-full ',
        innerItem: ' py-4 pl-2 border-l relative -left-[1px]',
    }
    const [superAdmin, setSuperAdmin] = useState(false)
    const [admin, setAdmin] = useState(false)
    useEffect(() => {
        const superAdmin = localStorage.getItem('superAdmin') === 'true'
        const admin = localStorage.getItem('admin') === 'true'
        setSuperAdmin(superAdmin)
        setAdmin(admin)
    }, [])

    const [permissions, setPermissions] = useState<any>(false)

    useEffect(() => {
        setPermissions(getPermissions)
    }, [])

    return (
        <>
            <ul className="w-full">
                <li
                    className={`${pathname.includes('/dashboard') ? ' ' : classes.link}`}>
                    <Link href={`/dashboard`}>
                        <SealogsVesselsIcon
                            className={`h-12 w-12 ring-1 p-0.5 rounded-full`}
                        />
                        <span className={classes.navigation}>Home port</span>
                    </Link>
                </li>
                <li
                    className={`${pathname.includes('/vessel') ? ' ' : classes.link}`}>
                    <Link href={`/vessel`}>
                        <div className={classes.button}>
                            <svg
                                className="h-10 w-10"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 792.94 796.89">
                                <path
                                    d="M18.72,443.46l49.36-10.95,40.78,210.23c-24.73,11.46-39.09,18.57-39.84,18.94-1.21.6-1.82,2.13-1.41,3.53l35.41,121.85c.36,1.2,1.35,2.01,2.48,2.01h520.87c.98,0,1.88-.61,2.32-1.58l158.12-345.55c.43-.92.37-2.03-.14-2.9-.51-.87-1.43-1.42-2.33-1.3-55.3,3.51-114.19,13.19-173.51,26.65L537.56,19.7c-.17-1.02-.83-1.87-1.71-2.21l-28.14-10.93c-.8-.32-1.71-.15-2.39.4l-16.14,13.15c-7.01-.31-48.91-.65-82.59,34.32-34.39,35.71-112.59,29-113.37,28.93-.84-.11-1.71.32-2.25,1.07-.54.75-.71,1.75-.46,2.66l32.83,117.76c.24.88.84,1.57,1.62,1.87,10.81,4.27,21.7,6.05,32.34,6.05,39.24,0,75.12-24.15,91.03-36.63,26.84-21.05,55.2-12.93,61.54-10.71l49.32,311.52c-48.37,12.6-96.42,27.27-142.47,42.73l-52.61-241.16c-.16-.76-.6-1.41-1.2-1.8s-1.3-.5-2-.31L6.29,381.72c-1.35.4-2.18,1.92-1.87,3.43l11.25,56.09c.32,1.53,1.63,2.56,3.05,2.21h0ZM445.33,171.42c-19.38,15.2-69.39,48.42-117.39,30.32l-31.31-112.34c16.58.94,81.54,2.47,113.53-30.75,30.24-31.4,68.54-32.91,77.62-32.78l21.08,133.18c-10.23-3.1-37.21-8.27-63.54,12.37ZM492.83,24.33l14.47-11.8,25.41,9.87,73,443.13c-13.81,3.18-27.62,6.57-41.42,10.12L492.83,24.33ZM780.1,443.8l-155.32,339.47H107.4l-34.11-117.37c32.18-15.79,417.39-202.03,706.81-222.1h0ZM411.74,521.36c-63.29,21.39-122.61,44.19-173.56,65.07l-44.25-197.99,177.24-53.03s40.56,185.95,40.56,185.95ZM359.69,282.77l10.25,47.01-179.84,53.81c-1.37.41-2.19,1.96-1.84,3.49l45.01,201.37c-49.5,20.38-90.74,38.77-119.52,52.03l-41.12-211.98c-.3-1.54-1.6-2.54-3.05-2.23l-49.38,10.96-10.16-50.62,349.65-103.83h0Z"
                                    fill="#ffffff"
                                    stroke="#ffffff"
                                    strokeMiterlimit="10"
                                    strokeWidth="8.7px"
                                />
                                <path
                                    d="M780.1,443.8l-155.32,339.47H107.4l-34.11-117.37c32.18-15.79,417.39-202.03,706.81-222.1h0Z"
                                    fill="#f5f7fa"
                                    stroke="#ffffff"
                                    strokeMiterlimit="10"
                                    strokeWidth="8.7px"
                                />
                            </svg>
                            <span className={classes.navigation}>
                                All vessels
                            </span>
                        </div>
                    </Link>
                </li>
                <li
                    className={`${(pathname.includes('/crew') || pathname.includes('/crew-training')) && !pathname.includes('/settings') ? ' ' : classes.link} hidden xl:block`}>
                    <Button
                        className={classes.button}
                        onPress={() => setToggleCrew(!toggleCrew)}>
                        <svg
                            className="m-auto lg:-ml-1 lg:mr-2 h-9 w-9"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 19 18.25">
                            <path
                                d="M18.6935,7.7297h-2.3172c-.9285-2.994-3.7221-5.0658-6.8763-5.0658S3.5522,4.7357,2.6237,7.7297H.3065c-.0901,0-.1757.0396-.2341.1087s-.0832.16-.068.249l1.2258,7.2433c.0249.1473.1526.2554.3022.2554h15.9354c.1493,0,.277-.1079.3022-.2554l1.2258-7.2433c.0151-.0891-.0098-.18-.068-.249s-.144-.1087-.2341-.1087ZM.6175,8.3096h17.7656l-.356,1.8273H.9729l-.3554-1.8273ZM9.5,3.2768c2.8218,0,5.329,1.8115,6.233,4.4529H3.267c.904-2.6414,3.4112-4.4529,6.233-4.4529ZM17.2087,14.973H1.7914l-.7146-4.2233h16.8466l-.7146,4.2233Z"
                                fill="#022450"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0px"
                            />
                            <polygon
                                points=".6175 8.3096 18.3831 8.3096 18.0271 10.1368 .9729 10.1368 .6175 8.3096"
                                fill="#f2f4f7"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0px"
                            />
                        </svg>
                        <span className={classes.navigation}>Crew</span>
                    </Button>
                    <div
                        className={`peer w-full flex justify-between p-2 ${toggleCrew ? 'block' : 'hidden'}`}>
                        <ul className={classes.innerMenu}>
                            <li
                                className={`${classes.innerItem} ${pathname.includes('/crew') && !pathname.includes('/crew-training') ? '0' : 'border-transparent'}`}>
                                <Link href="/crew">
                                    <span className={classes.navigation}>
                                        All crew
                                    </span>
                                </Link>
                            </li>
                            {permissions &&
                                hasPermission('VIEW_TRAINING', permissions) && (
                                    <li
                                        className={`${classes.innerItem} ${pathname.includes('/crew-training') || pathname.includes('/training-type') ? '0' : 'border-transparent'}`}>
                                        <Link href="/crew-training">
                                            <span
                                                className={classes.navigation}>
                                                Training / Drills
                                            </span>
                                        </Link>
                                    </li>
                                )}
                        </ul>
                    </div>
                </li>
                <li className="sm:block xl:hidden">
                    <DialogTrigger>
                        <Button className={classes.button}>
                            <svg
                                className="m-auto lg:-ml-1 lg:mr-2 h-9 w-9"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 19 18.25">
                                <path
                                    d="M18.6935,7.7297h-2.3172c-.9285-2.994-3.7221-5.0658-6.8763-5.0658S3.5522,4.7357,2.6237,7.7297H.3065c-.0901,0-.1757.0396-.2341.1087s-.0832.16-.068.249l1.2258,7.2433c.0249.1473.1526.2554.3022.2554h15.9354c.1493,0,.277-.1079.3022-.2554l1.2258-7.2433c.0151-.0891-.0098-.18-.068-.249s-.144-.1087-.2341-.1087ZM.6175,8.3096h17.7656l-.356,1.8273H.9729l-.3554-1.8273ZM9.5,3.2768c2.8218,0,5.329,1.8115,6.233,4.4529H3.267c.904-2.6414,3.4112-4.4529,6.233-4.4529ZM17.2087,14.973H1.7914l-.7146-4.2233h16.8466l-.7146,4.2233Z"
                                    fill="#022450"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0px"
                                />
                                <polygon
                                    points=".6175 8.3096 18.3831 8.3096 18.0271 10.1368 .9729 10.1368 .6175 8.3096"
                                    fill="#f2f4f7"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0px"
                                />
                            </svg>
                        </Button>
                        <Popover placement="right" className="   rounded">
                            <Dialog>
                                <div
                                    className={`peer w-full flex justify-between p-2`}>
                                    <ul className={classes.innerMenu}>
                                        <li
                                            className={`${classes.innerItem} ${pathname.includes('/crew') && !pathname.includes('/crew-training') ? '0' : 'border-transparent'}`}>
                                            <Link href="/crew">
                                                <span>All crew</span>
                                            </Link>
                                        </li>
                                        {permissions &&
                                            hasPermission(
                                                'VIEW_TRAINING',
                                                permissions,
                                            ) && (
                                                <li
                                                    className={`${classes.innerItem} ${pathname.includes('/crew-training') || pathname.includes('/training-type') ? '0' : 'border-transparent'}`}>
                                                    <Link href="/crew-training">
                                                        <span>
                                                            Training / Drills
                                                        </span>
                                                    </Link>
                                                </li>
                                            )}
                                    </ul>
                                </div>
                            </Dialog>
                        </Popover>
                    </DialogTrigger>
                </li>
                <li
                    className={`${(pathname.includes('/risk-evaluations') || pathname.includes('/risk-strategies')) && !pathname.includes('/settings') ? ' ' : classes.link} hidden xl:block`}>
                    <Button
                        className={classes.button}
                        onPress={() => setToggleHnS(!toggleHnS)}>
                        <svg
                            className="m-auto lg:-ml-1 lg:mr-2 h-9 w-9"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 19.209 18.25">
                            <rect
                                x=".2658"
                                y="5.9435"
                                width="18.6775"
                                height="12.0407"
                                rx=".6387"
                                ry=".6387"
                                fill="#f2f4f7"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.25px"
                            />
                            <path
                                d="M19.084,17.3455V3.2669c0-.4297-.3497-.7795-.7795-.7795h-9.5224L7.0224.1531c-.0132-.0177-.0341-.028-.0561-.028H.9045C.4747.125.125.4748.125.9045v16.441c0,.4297.3497.7795.7795.7795h17.4001c.4297,0,.7795-.3497.7795-.7795ZM3.2614,5.8028H.9045c-.2644,0-.4977.133-.6387.3349V.9045C.2658.5523.5523.2658.9045.2658h6.0267l1.7597,2.3343c.0132.0177.0341.028.0561.028h9.5576c.3522,0,.6387.2865.6387.6387v2.8707c-.141-.2019-.3743-.3349-.6387-.3349h-2.3569M15.8069,5.8028H3.4022M.2658,17.3455V6.5821c0-.3521.2865-.6386.6387-.6386h17.4001c.3522,0,.6387.2865.6387.6386v10.7634c0,.3522-.2865.6387-.6387.6387H.9045c-.3522,0-.6387-.2865-.6387-.6387Z"
                                fill="#022450"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.25px"
                            />
                            <path
                                d="M13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z"
                                fill="#ffffff"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.3885px"
                            />
                            <path
                                d="M13.4779,10.8868h-2.6351v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.2231c-.0698,0-.1268.0567-.1268.1268v2.6351h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.2229c0,.0701.0569.1268.1268.1268h2.6351v2.6351c0,.0701.0569.1268.1268.1268h2.2231c.0698,0,.1268-.0567.1268-.1268v-2.6351h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.2229c0-.0701-.0569-.1268-.1268-.1268ZM13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z"
                                fill="#022450"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.3885px"
                            />
                        </svg>
                        <span className={classes.navigation}>
                            Health & safety
                        </span>
                    </Button>
                    <div
                        className={`peer w-full flex justify-between p-2 ${toggleHnS ? 'block' : 'hidden'}`}>
                        <ul className={classes.innerMenu}>
                            <li
                                className={`${classes.innerItem} ${pathname.includes('/risk-evaluations') ? '0' : 'border-transparent'}`}>
                                <Link href="/risk-evaluations">
                                    <span className={classes.navigation}>
                                        Risk Evaluations
                                    </span>
                                </Link>
                            </li>
                            <li
                                className={`${classes.innerItem} ${pathname.includes('/risk-strategies') ? '0' : 'border-transparent'}`}>
                                <Link href="/risk-strategies">
                                    <span className={classes.navigation}>
                                        Risk Strategies
                                    </span>
                                </Link>
                            </li>
                            <li
                                className={`${classes.innerItem} ${pathname.includes('/incident-records') ? 'border-slblue-500' : 'border-transparent'}`}>
                                <Link href="/incident-records">
                                    <span className={classes.navigation}>
                                        Incident Records
                                    </span>
                                </Link>
                            </li>
                        </ul>
                    </div>
                </li>
                <li className="sm:block xl:hidden">
                    <DialogTrigger>
                        <Button className={classes.button}>
                            <svg
                                className="m-auto lg:-ml-1 lg:mr-2 h-9 w-9"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 19.209 18.25">
                                <rect
                                    x=".2658"
                                    y="5.9435"
                                    width="18.6775"
                                    height="12.0407"
                                    rx=".6387"
                                    ry=".6387"
                                    fill="#f2f4f7"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.25px"
                                />
                                <path
                                    d="M19.084,17.3455V3.2669c0-.4297-.3497-.7795-.7795-.7795h-9.5224L7.0224.1531c-.0132-.0177-.0341-.028-.0561-.028H.9045C.4747.125.125.4748.125.9045v16.441c0,.4297.3497.7795.7795.7795h17.4001c.4297,0,.7795-.3497.7795-.7795ZM3.2614,5.8028H.9045c-.2644,0-.4977.133-.6387.3349V.9045C.2658.5523.5523.2658.9045.2658h6.0267l1.7597,2.3343c.0132.0177.0341.028.0561.028h9.5576c.3522,0,.6387.2865.6387.6387v2.8707c-.141-.2019-.3743-.3349-.6387-.3349h-2.3569M15.8069,5.8028H3.4022M.2658,17.3455V6.5821c0-.3521.2865-.6386.6387-.6386h17.4001c.3522,0,.6387.2865.6387.6386v10.7634c0,.3522-.2865.6387-.6387.6387H.9045c-.3522,0-.6387-.2865-.6387-.6387Z"
                                    fill="#022450"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.25px"
                                />
                                <path
                                    d="M13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z"
                                    fill="#ffffff"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.3885px"
                                />
                                <path
                                    d="M13.4779,10.8868h-2.6351v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.2231c-.0698,0-.1268.0567-.1268.1268v2.6351h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.2229c0,.0701.0569.1268.1268.1268h2.6351v2.6351c0,.0701.0569.1268.1268.1268h2.2231c.0698,0,.1268-.0567.1268-.1268v-2.6351h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.2229c0-.0701-.0569-.1268-.1268-.1268ZM13.3511,13.1097h-2.6351c-.0698,0-.1268.0567-.1268.1268v2.6351h-1.9696v-2.6351c0-.0701-.0569-.1268-.1268-.1268h-2.6351v-1.9694h2.6351c.0698,0,.1268-.0567.1268-.1268v-2.6351h1.9696v2.6351c0,.0701.0569.1268.1268.1268h2.6351v1.9694Z"
                                    fill="#022450"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.3885px"
                                />
                            </svg>
                        </Button>
                        <Popover placement="right" className="   rounded">
                            <Dialog>
                                <div
                                    className={`peer w-full flex justify-between p-2`}>
                                    <ul className={classes.innerMenu}>
                                        <li
                                            className={`${classes.innerItem} ${pathname.includes('/risk-evaluations') ? 'border-curious-blue-400' : 'border-transparent'}`}>
                                            <Link href="/risk-evaluations">
                                                <span>Risk Evaluations</span>
                                            </Link>
                                        </li>
                                        <li
                                            className={`${classes.innerItem} ${pathname.includes('/risk-strategies') ? 'border-curious-blue-400' : 'border-transparent'}`}>
                                            <Link href="/risk-strategies">
                                                <span>Risk Strategies</span>
                                            </Link>
                                        </li>
                                        <li
                                            className={`${classes.innerItem} ${pathname.includes('/incident-records') ? 'border-curious-blue-400' : 'border-transparent'}`}>
                                            <Link href="/incident-records">
                                                <span>Incident Records</span>
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </Dialog>
                        </Popover>
                    </DialogTrigger>
                </li>
                <li
                    className={`${(pathname.includes('/inventory') || pathname.includes('/inventory/suppliers')) && !pathname.includes('/settings') ? ' ' : classes.link} hidden xl:block`}>
                    <Button
                        className={classes.button}
                        onPress={() => setToggleInventory(!toggleInventory)}>
                        <svg
                            className="m-auto lg:-ml-1 lg:mr-2 h-9 w-9"
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 19.4014 20.2416">
                            <rect
                                x=".2681"
                                y="15.3634"
                                width="7.1756"
                                height="1.7875"
                                fill="#f2f4f7"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.25px"
                            />
                            <rect
                                x="11.9577"
                                y="15.3634"
                                width="7.1756"
                                height="1.7875"
                                fill="#f2f4f7"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.25px"
                            />
                            <path
                                d="M15.4984,9.1277l1.4943-7.5975c.0066-.0338-.0109-.0678-.0426-.0817L13.9446.125c-.0026-.0012-.0054-.0005-.0081-.0014-.0036-.0011-.0065-.0011-.0102-.0017-.016-.0026-.0319-.0009-.0462.007-.0009.0005-.0019-.0001-.0028.0004-.0001,0-.0021.0012-.0024.0014,0,0-.0001,0-.0002,0,0,0,0,0,0,0-.0412.0243-1.711.9955-4.1739.9955C7.24,1.1263,5.5679.155,5.5268.1308c0,0,0,0,0,0,0,0-.0001,0-.0002,0-.0003-.0002-.0023-.0013-.0024-.0014-.0009-.0006-.002,0-.0029-.0004-.0141-.0077-.0299-.0094-.0459-.0069-.0038.0006-.0069.0005-.0106.0017-.0026.0008-.0054.0002-.0079.0013l-3.0056,1.3236c-.0316.014-.0492.0479-.0426.0817l1.4943,7.5975L.1481,12.1682c-.0173.0141-.0273.0351-.0273.0573v7.8217c0,.0406.0331.0737.0737.0737h8.1825c.0196,0,.0383-.0078.0521-.0216l1.2716-1.2716,1.2716,1.2716c.0138.0138.0325.0216.0521.0216h8.1825c.0406,0,.0737-.0331.0737-.0737v-7.8217c0-.0222-.0101-.0432-.0273-.0573l-3.7548-3.0404ZM9.9007,15.1208l-.1263-7.6558c.4834-.1725,4.164-1.6693,4.2137-7.1599l2.8488,1.2545-1.4914,7.5832c-.0055.0268.0046.0544.0259.0715l3.6294,2.9065.1,3M9.7007,1.2736c2.1585,0,3.7208-.7367,4.1378-.9558-.0575,5.5134-3.8279,6.9121-4.1379,7.017-.3103-.1047-4.0803-1.4989-4.1378-7.017.417.2191,1.9793.9558,4.1378.9558ZM.3507,15.1208v-3l3.6794-2.9065c.0213-.0171.0314-.0447.0259-.0715L2.5645,1.5596,5.4134.3051c.0498,5.488,3.7275,6.9862,4.2137,7.16l.0737,7.6557h-2M.2681,15.3634h7.1756v1.7875H.2681v-1.7875ZM9.627,18.6929l-1.2806,1.2806H.2681v-2.6753h7.183c.0363.2613.2589.4638.53.4638h1.6459v.931ZM7.9811,17.6146c-.2152,0-.3901-.1749-.3901-.3901v-1.9348c0-.2152.1749-.3904.3901-.3904h1.7189s.0004.0003.0007.0003.0004-.0003.0007-.0003h1.7189c.2152,0,.3901.1752.3901.3904v1.9348c0,.2152-.1749.3901-.3901.3901h-3.4392ZM19.1333,19.9735h-8.0784l-1.2806-1.2806v-.931h1.6459c.2711,0,.4937-.2025.53-.4638h7.183v2.6753ZM19.1333,17.1509h-7.1756v-1.7875h7.1756v1.7875Z"
                                fill="#022450"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.25px"
                            />
                            <path
                                d="M7.9811,17.6146c-.2152,0-.3901-.1749-.3901-.3901v-1.9348c0-.2152.1749-.3904.3901-.3904h1.7189s.0004.0003.0007.0003.0004-.0003.0007-.0003h1.7189c.2152,0,.3901.1752.3901.3904v1.9348c0,.2152-.1749.3901-.3901.3901h-3.4392Z"
                                fill="#f2f4f7"
                                stroke="#022450"
                                strokeMiterlimit="10"
                                strokeWidth="0.25px"
                            />
                        </svg>
                        <span className={classes.navigation}>Inventory</span>
                    </Button>
                    <div
                        className={`peer w-full flex justify-between p-2 ${toggleInventory ? 'block' : 'hidden'}`}>
                        <ul className={classes.innerMenu}>
                            <li
                                className={`${classes.innerItem} ${pathname.includes('/inventory') && !pathname.includes('/inventory/suppliers') ? 'border-sky-600' : 'border-transparent'}`}>
                                <Link href="/inventory">
                                    <span className={classes.navigation}>
                                        All inventory
                                    </span>
                                </Link>
                            </li>
                            <li
                                className={`${classes.innerItem} ${pathname.includes('/inventory/suppliers') ? 'border-sky-600' : 'border-transparent'}`}>
                                <Link href="/inventory/suppliers">
                                    <span className={classes.navigation}>
                                        Suppliers
                                    </span>
                                </Link>
                            </li>
                        </ul>
                    </div>
                </li>
                <li className="sm:block xl:hidden">
                    <DialogTrigger>
                        <Button className={classes.button}>
                            <svg
                                className="m-auto lg:-ml-1 lg:mr-2 h-9 w-9"
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 19.4014 20.2416">
                                <rect
                                    x=".2681"
                                    y="15.3634"
                                    width="7.1756"
                                    height="1.7875"
                                    fill="#f2f4f7"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.25px"
                                />
                                <rect
                                    x="11.9577"
                                    y="15.3634"
                                    width="7.1756"
                                    height="1.7875"
                                    fill="#f2f4f7"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.25px"
                                />
                                <path
                                    d="M15.4984,9.1277l1.4943-7.5975c.0066-.0338-.0109-.0678-.0426-.0817L13.9446.125c-.0026-.0012-.0054-.0005-.0081-.0014-.0036-.0011-.0065-.0011-.0102-.0017-.016-.0026-.0319-.0009-.0462.007-.0009.0005-.0019-.0001-.0028.0004-.0001,0-.0021.0012-.0024.0014,0,0-.0001,0-.0002,0,0,0,0,0,0,0-.0412.0243-1.711.9955-4.1739.9955C7.24,1.1263,5.5679.155,5.5268.1308c0,0,0,0,0,0,0,0-.0001,0-.0002,0-.0003-.0002-.0023-.0013-.0024-.0014-.0009-.0006-.002,0-.0029-.0004-.0141-.0077-.0299-.0094-.0459-.0069-.0038.0006-.0069.0005-.0106.0017-.0026.0008-.0054.0002-.0079.0013l-3.0056,1.3236c-.0316.014-.0492.0479-.0426.0817l1.4943,7.5975L.1481,12.1682c-.0173.0141-.0273.0351-.0273.0573v7.8217c0,.0406.0331.0737.0737.0737h8.1825c.0196,0,.0383-.0078.0521-.0216l1.2716-1.2716,1.2716,1.2716c.0138.0138.0325.0216.0521.0216h8.1825c.0406,0,.0737-.0331.0737-.0737v-7.8217c0-.0222-.0101-.0432-.0273-.0573l-3.7548-3.0404ZM9.9007,15.1208l-.1263-7.6558c.4834-.1725,4.164-1.6693,4.2137-7.1599l2.8488,1.2545-1.4914,7.5832c-.0055.0268.0046.0544.0259.0715l3.6294,2.9065.1,3M9.7007,1.2736c2.1585,0,3.7208-.7367,4.1378-.9558-.0575,5.5134-3.8279,6.9121-4.1379,7.017-.3103-.1047-4.0803-1.4989-4.1378-7.017.417.2191,1.9793.9558,4.1378.9558ZM.3507,15.1208v-3l3.6794-2.9065c.0213-.0171.0314-.0447.0259-.0715L2.5645,1.5596,5.4134.3051c.0498,5.488,3.7275,6.9862,4.2137,7.16l.0737,7.6557h-2M.2681,15.3634h7.1756v1.7875H.2681v-1.7875ZM9.627,18.6929l-1.2806,1.2806H.2681v-2.6753h7.183c.0363.2613.2589.4638.53.4638h1.6459v.931ZM7.9811,17.6146c-.2152,0-.3901-.1749-.3901-.3901v-1.9348c0-.2152.1749-.3904.3901-.3904h1.7189s.0004.0003.0007.0003.0004-.0003.0007-.0003h1.7189c.2152,0,.3901.1752.3901.3904v1.9348c0,.2152-.1749.3901-.3901.3901h-3.4392ZM19.1333,19.9735h-8.0784l-1.2806-1.2806v-.931h1.6459c.2711,0,.4937-.2025.53-.4638h7.183v2.6753ZM19.1333,17.1509h-7.1756v-1.7875h7.1756v1.7875Z"
                                    fill="#022450"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.25px"
                                />
                                <path
                                    d="M7.9811,17.6146c-.2152,0-.3901-.1749-.3901-.3901v-1.9348c0-.2152.1749-.3904.3901-.3904h1.7189s.0004.0003.0007.0003.0004-.0003.0007-.0003h1.7189c.2152,0,.3901.1752.3901.3904v1.9348c0,.2152-.1749.3901-.3901.3901h-3.4392Z"
                                    fill="#f2f4f7"
                                    stroke="#022450"
                                    strokeMiterlimit="10"
                                    strokeWidth="0.25px"
                                />
                            </svg>
                        </Button>
                        <Popover placement="right" className="   rounded">
                            <Dialog>
                                <div
                                    className={`peer w-full flex justify-between p-2`}>
                                    <ul className={classes.innerMenu}>
                                        <li
                                            className={`${classes.innerItem} ${pathname.includes('/inventory') && !pathname.includes('/inventory/suppliers') ? 'border-sky-600' : 'border-transparent'}`}>
                                            <Link href="/inventory">
                                                <span>All inventory</span>
                                            </Link>
                                        </li>
                                        <li
                                            className={`${classes.innerItem} ${pathname.includes('/inventory/suppliers') ? 'border-sky-600' : 'border-transparent'}`}>
                                            <Link href="/inventory/suppliers">
                                                <span>Suppliers</span>
                                            </Link>
                                        </li>
                                    </ul>
                                </div>
                            </Dialog>
                        </Popover>
                    </DialogTrigger>
                </li>
                <li
                    className={`${pathname.includes('/maintenance') ? ' ' : classes.link}`}>
                    <Link href={`/maintenance`}>
                        <div className={classes.button}>
                            <SealogsMaintenanceIcon
                                className={`icons m-auto lg:-ml-1 lg:mr-2 h-9 w-9`}
                            />
                            <span className={classes.navigation}>
                                Maintenance
                            </span>
                        </div>
                    </Link>
                </li>
                <li
                    className={`${pathname.includes('/document-locker') ? ' ' : classes.link}`}>
                    <Link href={`/document-locker`}>
                        <div className={classes.button}>
                            <div
                                className={`icons m-auto lg:-ml-1 lg:mr-2 h-9 w-9`}>
                                <svg
                                    id="a"
                                    data-name="Layer 2"
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 19 18">
                                    <rect
                                        x="2.5"
                                        y=".98"
                                        width="5.44"
                                        height="16.04"
                                        style={{
                                            fill: '#f2f4f7',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <polygon
                                        points="9.04 1.61 11.95 17.39 17.29 16.4 14.39 .61 9.04 1.61"
                                        style={{
                                            fill: '#f2f4f7',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <path
                                        d="M8.22,17.59H2.21c-.16,0-.28-.13-.28-.28V.7c0-.16.13-.28.28-.28h6.01c.16,0,.28.13.28.28v16.62c0,.14-.13.27-.28.27ZM2.5,17.02h5.44s0-16.04,0-16.04H2.5s0,16.04,0,16.04Z"
                                        style={{
                                            fill: '#022450',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <path
                                        d="M7.1,6.49h-3.77c-.09,0-.14-.06-.14-.14V1.82c0-.09.06-.14.14-.14h3.77c.09,0,.14.06.14.14v4.53c0,.07-.07.14-.14.14ZM3.47,6.21h3.49s0-4.25,0-4.25h-3.49s0,4.25,0,4.25Z"
                                        style={{
                                            fill: '#022450',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <path
                                        d="M5.22,16.03c-1.01,0-1.84-.82-1.84-1.84s.82-1.84,1.84-1.84,1.84.82,1.84,1.84-.84,1.84-1.84,1.84ZM5.22,12.64c-.85,0-1.56.69-1.56,1.56s.69,1.56,1.56,1.56,1.56-.69,1.56-1.56-.71-1.56-1.56-1.56Z"
                                        style={{
                                            fill: '#022450',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <path
                                        d="M11.72,18c-.13,0-.26-.1-.28-.23L8.43,1.42c-.03-.16.07-.3.23-.33L14.56,0c.07-.01.16,0,.21.04s.1.11.11.18l3.02,16.34c.01.07,0,.16-.04.21s-.11.1-.18.11l-5.89,1.09s-.03.01-.06.01ZM9.04,1.61l2.9,15.78,5.34-.99L14.39.61l-5.34.99Z"
                                        style={{
                                            fill: '#022450',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <path
                                        d="M10.84,6.88c-.07,0-.13-.04-.14-.11l-.82-4.46s0-.07.03-.1c.03-.03.06-.06.09-.06l3.71-.68c.07-.01.16.04.17.11l.81,4.45s0,.07-.03.1-.06.06-.09.06l-3.71.69h-.01ZM10.18,2.39l.77,4.18,3.43-.64-.77-4.18-3.43.64Z"
                                        style={{
                                            fill: '#022450',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                    <path
                                        d="M14.12,15.95c-.86,0-1.64-.62-1.8-1.5-.09-.48.01-.98.3-1.37.28-.41.69-.68,1.18-.77.99-.18,1.96.48,2.14,1.47h0c.18.99-.48,1.96-1.47,2.14-.13.01-.24.03-.34.03ZM14.12,12.54c-.1,0-.18.01-.28.03-.41.07-.77.31-.99.65-.24.34-.33.75-.26,1.16.16.84.96,1.4,1.81,1.25.84-.16,1.4-.96,1.25-1.81-.14-.75-.79-1.28-1.53-1.28Z"
                                        style={{
                                            fill: '#022450',
                                            strokeWidth: '0px',
                                        }}
                                    />
                                </svg>
                            </div>
                            <span className={classes.navigation}>
                                Document locker
                            </span>
                        </div>
                    </Link>
                </li>
                <li
                    className={`${pathname.includes('/reporting') ? ' ' : classes.link}`}>
                    <Link href={`/reporting`}>
                        <div className={classes.button}>
                            <SealogsReportsIcon
                                className={`icons m-auto lg:-ml-1 lg:mr-2 h-9 w-9`}
                            />
                            <span className={classes.navigation}>
                                Reporting
                            </span>
                        </div>
                    </Link>
                </li>
                <li>
                    <Link href={`/visualIndicator`}>
                        <div className={classes.button}>
                            <SealogsReportsIcon
                                className={`icons m-auto lg:-ml-1 lg:mr-2 h-9 w-9`}
                            />
                            <span className={classes.navigation}>
                                VisualIndicator
                            </span>
                        </div>
                    </Link>
                </li>
            </ul>
        </>
    )
}
