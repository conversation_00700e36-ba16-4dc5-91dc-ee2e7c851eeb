import gql from 'graphql-tag'

export const GET_ENGINE_HOURS_PIE_CHART_MAINTENANCE_CHECK = gql`
    query ReadComponentMaintenanceChecks(
        $limit: Int = 500
        $offset: Int = 0
        $filter: ComponentMaintenanceCheckFilterFields = {}
    ) {
        readComponentMaintenanceChecks(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                startDate
                completed
                expires
                status
                archived
                maintenanceSchedule {
                    id
                    title
                    description
                    type
                    occursEveryType
                    occursEvery
                    maintenanceChecks {
                        nodes {
                            id
                        }
                    }
                    engineUsage {
                        nodes {
                            id
                            lastScheduleHours
                            isScheduled
                            engine {
                                id
                                title
                                currentHours
                            }
                        }
                    }
                }
            }
        }
    }
`