'use client'

import React, { useState, useEffect } from 'react'
import { useMutation } from '@apollo/client'
import { UpdateTripReport_LogBookEntrySection } from '@/app/lib/graphQL/mutation'
import { Combobox } from '@/components/ui/comboBox'
import { Label } from '@/components/ui/label'
import { useToast } from '@/hooks/use-toast'

export default function Master({
    currentTrip,
    tripReport,
    crewMembers,
}: {
    currentTrip: any
    tripReport: any
    templateStyle?: boolean | string
    offline?: boolean
    updateTripReport?: any
    crewMembers: any
}) {
    const [selectedMaster, setSelectedMaster] = useState<any>()
    const { toast } = useToast()

    useEffect(() => {
        if (tripReport && crewMembers) {
            setSelectedMaster(null)
            const trip = tripReport.find(
                (trip: any) => trip.id === currentTrip.id,
            )
            if (trip?.masterID > 0) {
                // Find the master in the crew members list to get the full profile
                const foundMaster = crewMembers.find(
                    (member: any) => member.crewMember.id === trip.masterID,
                )

                if (foundMaster) {
                    setSelectedMaster({
                        label:
                            foundMaster.crewMember.firstName +
                            ' ' +
                            foundMaster.crewMember.surname,
                        value: String(foundMaster.crewMember.id),
                        profile: {
                            firstName: foundMaster.crewMember.firstName || '',
                            surname: foundMaster.crewMember.surname || '',
                            avatar: foundMaster.crewMember.profileImage,
                        },
                    })
                }
            }
        }
    }, [tripReport, crewMembers, currentTrip])

    const handleMasterChange = async (selectedMaster: any) => {
        setSelectedMaster(selectedMaster)
        updateTripReport_LogBookEntrySectionMasterID({
            variables: {
                input: {
                    id: currentTrip.id,
                    masterID: selectedMaster ? +selectedMaster.value : null,
                },
            },
        })
    }

    const [updateTripReport_LogBookEntrySectionMasterID] = useMutation(
        UpdateTripReport_LogBookEntrySection,
        {
            onError: (error) => {
                console.error('onError', error)
                toast({
                    title: 'Error',
                    description: 'Failed to update master',
                    variant: 'destructive',
                })
            },
        },
    )

    const getSelectedMaster = () => {
        if (selectedMaster) {
            return selectedMaster
        }
        if (crewMembers) {
            const foundMaster = crewMembers.find(
                (member: any) => member.crewMember.id === currentTrip.masterID,
            )
            if (foundMaster) {
                return {
                    label:
                        foundMaster.crewMember.firstName +
                        ' ' +
                        foundMaster.crewMember.surname,
                    value: String(foundMaster.crewMember.id),
                    profile: {
                        firstName: foundMaster.crewMember.firstName || '',
                        surname: foundMaster.crewMember.surname || '',
                        avatar: foundMaster.crewMember.profileImage,
                    },
                }
            }
        }
        return null
    }

    return (
        <Label htmlFor="trip-master" label="Trip Master">
            {crewMembers && (
                <Combobox
                    options={
                        crewMembers?.map((member: any) => ({
                            label:
                                member.crewMember.firstName +
                                ' ' +
                                member.crewMember.surname,
                            value: String(member.crewMember.id),
                            profile: {
                                firstName: member.crewMember.firstName || '',
                                surname: member.crewMember.surname || '',
                                avatar: member.crewMember.profileImage,
                            },
                        })) ?? []
                    }
                    value={getSelectedMaster()}
                    onChange={handleMasterChange}
                    placeholder="Select Master"
                    buttonClassName="md:w-64 w-full"
                />
            )}
        </Label>
    )
}
