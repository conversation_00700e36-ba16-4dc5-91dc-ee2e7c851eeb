'use client'
import React, { useEffect, useState } from 'react'
import { Vessel } from '../../../../../../types/vessel'
import 'swiper/css'
import 'swiper/css/navigation'
import 'swiper/css/pagination'
import 'swiper/css/scrollbar'
import 'swiper/css/free-mode'
import dynamic from 'next/dynamic'
import { Skeleton } from '@/components/ui'
import { useLazyQuery } from '@apollo/client'
import { ReadDashboardData, ReadVessels } from './queries'
const FullMap = dynamic(() => import('@/components/full-map'), {
    ssr: false,
})
export default function DashboardMap() {
    const [isLoading, setIsLoading] = useState(true)
    const [vesselList, setVesselList] = useState<Vessel[]>([])
    const [filteredVesselList, setFilteredVesselList] = useState<Vessel[]>([])
    //const [inventories, setInventories] = useState<any>(false)

    const [currentDepartment, setCurrentDepartment] = useState<any>(false)

    //getInventoryList(setInventories)

    useEffect(() => {
        if (currentDepartment && vesselList) {
            if (
                currentDepartment === true ||
                localStorage.getItem('useDepartment') !== 'true'
            ) {
                setFilteredVesselList(vesselList)
            } else {
                setFilteredVesselList(
                    vesselList.filter((vessel: any) =>
                        currentDepartment.some(
                            (department: any) => department.id === vessel.id,
                        ),
                    ),
                )
                setFilteredVesselList(vesselList)
            }
        }
    }, [currentDepartment, vesselList])

    const handleSetVessels = (vessels: any) => {
        const activeVessels = vessels.filter(
            (vessel: any) => vessel.showOnDashboard,
        )
        setVesselList(activeVessels)
    }

    const [getVessselsWithLatestStatus] = useLazyQuery(ReadVessels, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('queryLogBookEntrySections error', error)
        },
    })
    const [queryVessels] = useLazyQuery(ReadDashboardData, {
        fetchPolicy: 'no-cache',
        onCompleted: async (queryVesselResponse: any) => {
            if (
                queryVesselResponse.readDashboardData &&
                typeof window !== 'undefined'
            ) {
                const vessels = queryVesselResponse.readDashboardData[0].vessels

                const vesselIDs = vessels.map((item: any) => item.id)

                let response: any = []
                if (vesselIDs.length > 0) {
                    response = await getVessselsWithLatestStatus({
                        variables: {
                            vesselFilter: {
                                id: { in: vesselIDs },
                            },
                            filter: { archived: { eq: false } },
                        },
                    })
                }

                const vesselsWithLatestStatus =
                    response.data?.readVessels?.nodes ?? []

                // Loop through vessels and save the tasksDue and trainingsDue properties to localStorage with this format for the keys: 'tasksDue-id' and 'trainingsDue-id'
                for (let i = 0; i < vessels.length; i++) {
                    const vessel = vessels[i]
                    localStorage.setItem(
                        `tasksDue-${vessel.id}`,
                        vessel.tasksDue,
                    )
                    localStorage.setItem(
                        `trainingsDue-${vessel.id}`,
                        vessel.trainingsDue,
                    )
                }

                const vesselsWithStatus = vessels.map(function (vessel: any) {
                    const vesselWithStatus = vesselsWithLatestStatus.find(
                        (item: any) => item.id == vessel.id,
                    )
                    const statusHistory =
                        vesselWithStatus?.statusHistory.nodes ?? []
                    const status =
                        statusHistory.length > 0 ? statusHistory[0] : null

                    return {
                        ...vessel,
                        status: status,
                    }
                })

                handleSetVessels(vesselsWithStatus)
            }
        },
        onError: (error: any) => {
            console.error('queryVessels error', error)
        },
    })
    const loadVessels = async () => {
        await queryVessels({
            variables: {
                limit: 200,
                offset: 0,
                archived: 0, // 0 - active, 1 - archived, 2 - both
            },
        })
    }
    useEffect(() => {
        if (isLoading) {
            loadVessels()
            setIsLoading(false)
        }
    }, [isLoading])

    return (
        <>
            {vesselList && vesselList.length ? (
                <FullMap className="h-[100cvh]" vessels={vesselList} />
            ) : (
                <Skeleton className="size-full" />
            )}
        </>
    )
}
