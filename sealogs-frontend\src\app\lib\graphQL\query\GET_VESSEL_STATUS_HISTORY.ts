import gql from "graphql-tag";

export const GET_VESSEL_STATUS_HISTORY = gql`
  query GET_VESSEL_STATUS_HISTORY($filter: VesselStatusFilterFields = {}, $offset: Int = 0, $limit: Int = 100){
    readVesselStatuss(
      sort: { date: ASC },
      filter: $filter,
      limit: $limit
      offset: $offset
    ) {
      pageInfo{
        hasNextPage
        totalCount
      }
      nodes {
          id
          date
          status
          comment
          created
          reason
          otherReason
          expectedReturn
          vesselID
      }
    }
  }
` 