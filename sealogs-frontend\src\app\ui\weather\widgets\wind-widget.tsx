import { windDirections } from '@/app/helpers/weatherHelper'
import { useEffect, useState } from 'react'
import { cn } from '@/app/lib/utils'
import WindSpeedSlider from '../wind-speed-slider'
import WindDirectionDropdown from '../wind-direction-dropdown'
import { type Option } from '@/components/ui/comboBox'
import { Label } from '@/components/ui/label'
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui'

interface WindWidgetProps {
    direction?: string
    speed?: number
    editMode?: boolean
    iconOnly?: boolean
    onChange?: (values: {
        speed: number
        direction: { label: string; degrees: number; value: string }
    }) => void
}

const WindWidget = ({
    direction = 'north',
    speed = 0,
    editMode = false,
    iconOnly = false,
    onChange,
}: WindWidgetProps) => {
    const [selectedWindDirection, setSelectedWindDirection] = useState(
        windDirections.find((item) => item.value === direction) ||
            windDirections.find((item) => item.value === 'north')!,
    )
    const [selectedSpeed, setSelectedSpeed] = useState<number>(speed)

    useEffect(() => {
        const windDir = windDirections.find((item) => item.value === direction)
        if (windDir) {
            setSelectedWindDirection(windDir)
        }
    }, [direction])

    useEffect(() => {
        setSelectedSpeed(speed)
    }, [speed])

    const clampedSpeed = Math.max(0, Math.min(60, selectedSpeed))
    const fillPercentage = (clampedSpeed / 60) * 100
    const rotationDegrees =
        selectedWindDirection.degrees >= 0 ? selectedWindDirection.degrees : 0

    const ariaLabel = `Wind direction: ${selectedWindDirection.label}, speed: ${selectedSpeed} knots`

    const handleWindSpeedChange = (value: number) => {
        setSelectedSpeed(value)
        if (onChange) {
            onChange({
                speed: value,
                direction: {
                    label: selectedWindDirection.label,
                    degrees: selectedWindDirection.degrees,
                    value: selectedWindDirection.value,
                },
            })
        }
    }

    const handleWindDirectionChange = (
        option: Option | Option[] | null | string,
    ) => {
        let windDir: any = null
        let selectedValue = ''

        if (option && option instanceof Option && !Array.isArray(option)) {
            selectedValue = option.value
        }

        if (option && typeof option === 'string') {
            selectedValue = option
        }

        if (windDir) {
            setSelectedWindDirection(windDir)
            if (onChange) {
                onChange({
                    speed: selectedSpeed,
                    direction: {
                        label: windDir.label,
                        degrees: windDir.degrees,
                        value: windDir.value,
                    },
                })
            }
        }
    }

    if (!editMode) {
        return (
            <div className="flex flex-col items-center p-4">
                {iconOnly && (
                    <Popover triggerType="hover">
                        <PopoverTrigger asChild>
                            <div
                                className={cn(
                                    'relative transition-all duration-300 ease-in-out',
                                    iconOnly
                                        ? 'w-16 h-16'
                                        : 'w-[179.605px] h-[179.605px]',
                                )}
                                style={{
                                    transform: `rotate(${rotationDegrees}deg)`,
                                }}>
                                {/* Background SVG */}
                                <svg
                                    xmlns="http://www.w3.org/2000/svg"
                                    viewBox="0 0 179.605 179.605"
                                    aria-label={ariaLabel}
                                    className="w-full h-full transition-all duration-300 ease-in-out">
                                    <path
                                        d="M3,61.211,128,2,68.789,127,55.632,74.368Z"
                                        transform="translate(-2.121 90.51) rotate(-45)"
                                        fill="white"
                                        stroke="#035388"
                                        strokeLinecap="round"
                                        strokeLinejoin="round"
                                        strokeWidth="2"
                                    />
                                </svg>

                                {/* Overlay SVG */}
                                <div className="absolute top-0 left-0 w-full h-full transition-all duration-300 ease-in-out">
                                    <svg
                                        xmlns="http://www.w3.org/2000/svg"
                                        viewBox="0 0 179.605 179.605"
                                        className="w-full h-full">
                                        <mask id="fillMask">
                                            <rect
                                                x="0"
                                                y="0"
                                                width="179.605"
                                                height={`${fillPercentage}%`}
                                                fill="#fff"
                                            />
                                        </mask>
                                        <path
                                            d="M3,61.211,128,2,68.789,127,55.632,74.368Z"
                                            transform="translate(-2.121 90.51) rotate(-45)"
                                            fill="#035388"
                                            stroke="#035388"
                                            strokeLinecap="round"
                                            strokeLinejoin="round"
                                            strokeWidth="2"
                                            mask="url(#fillMask)"
                                        />
                                    </svg>
                                </div>
                            </div>
                        </PopoverTrigger>
                        <PopoverContent>
                            <div>
                                <span className="font-bold">
                                    {selectedWindDirection.label}:
                                </span>{' '}
                                {selectedSpeed.toFixed(0)} knots
                            </div>
                        </PopoverContent>
                    </Popover>
                )}
                {!iconOnly && (
                    <div
                        className={cn(
                            'relative transition-all duration-300 ease-in-out',
                            iconOnly ? 'w-16 h-16' : 'size-[125px]',
                        )}
                        style={{
                            transform: `rotate(${rotationDegrees}deg)`,
                        }}>
                        {/* Background SVG */}
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            viewBox="0 0 179.605 179.605"
                            aria-label={ariaLabel}
                            className="w-full h-full transition-all duration-300 ease-in-out">
                            <path
                                d="M3,61.211,128,2,68.789,127,55.632,74.368Z"
                                transform="translate(-2.121 90.51) rotate(-45)"
                                fill="white"
                                stroke="#035388"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                            />
                        </svg>

                        {/* Overlay SVG */}
                        <div className="absolute top-0 left-0 w-full h-full transition-all duration-300 ease-in-out">
                            <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 179.605 179.605"
                                className="w-full h-full">
                                <mask id="fillMask">
                                    <rect
                                        x="0"
                                        y="0"
                                        width="179.605"
                                        height={`${fillPercentage}%`}
                                        fill="#fff"
                                    />
                                </mask>
                                <path
                                    d="M3,61.211,128,2,68.789,127,55.632,74.368Z"
                                    transform="translate(-2.121 90.51) rotate(-45)"
                                    fill="#035388"
                                    stroke="#035388"
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth="2"
                                    mask="url(#fillMask)"
                                />
                            </svg>
                        </div>
                    </div>
                )}

                {!iconOnly && (
                    <div
                        className={cn(
                            'transition-all duration-300 ease-in-out',
                        )}>
                        <span className="font-bold text-4xl">
                            {selectedWindDirection.abbreviation}:{' '}
                            {selectedSpeed.toFixed(0)}
                        </span>{' '}
                        <span className="font-bold text-base">knots</span>
                    </div>
                )}
            </div>
        )
    }

    return (
        <div className="w-full flex flex-col gap-4 my-4">
            <div className="w-full">
                <Label label="Wind Direction">
                    <WindDirectionDropdown
                        value={selectedWindDirection.value}
                        onChange={handleWindDirectionChange}
                    />
                </Label>
            </div>
            <div className="w-full">
                <WindSpeedSlider
                    label="Wind Strength"
                    value={selectedSpeed}
                    onChange={handleWindSpeedChange}
                    unitOfMeasure="knots"
                />
            </div>
        </div>
    )
}

export default WindWidget
