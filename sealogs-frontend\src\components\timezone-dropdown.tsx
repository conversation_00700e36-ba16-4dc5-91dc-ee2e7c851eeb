import { TimezonesByCountry } from '../app/lib/data'
import { useEffect, useState } from 'react'
import { Combobox, Option } from '@/components/ui/comboBox'

interface TimezoneDropdownProps {
    value?: string
    countryCode?: string
    onChange: (option: Option | Option[] | null) => void
    disabled?: boolean
}

const TimezoneDropdown = ({
    value = 'Pacific/Auckland',
    countryCode = 'ALL',
    onChange,
    disabled = false,
}: TimezoneDropdownProps) => {
    const [timezones, setTimezones] = useState<Option[]>([])
    const [selectedTimezone, setSelectedTimezone] = useState<Option | null>(
        null,
    )

    useEffect(() => {
        const tz = (TimezonesByCountry[countryCode] || []).map((t) => ({
            value: t,
            label: t,
        }))

        setTimezones(tz)
        let timezone = tz.find((c) => c.value === 'Pacific/Auckland')
        if (value) {
            timezone = tz.find((c) => c.value === value)
        }
        setSelectedTimezone(timezone ? timezone : tz[0])
    }, [value, countryCode])

    return (
        <Combobox
            options={timezones}
            placeholder="Time Zone"
            value={selectedTimezone}
            onChange={onChange}
            isDisabled={disabled}
        />
    )
}

export default TimezoneDropdown
