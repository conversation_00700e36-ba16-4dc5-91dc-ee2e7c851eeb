import React from 'react'
import { cn } from '@/app/lib/utils'
import { Button, RadioGroup, RadioGroupItem } from '@/components/ui'
import { RequiredTooltip } from './required-tooltip'
import { MessageSquareIcon, PencilIcon } from 'lucide-react'

interface IProps {
    title: string
    isRequired?: boolean
    className?: string
    isDisabled?: boolean
    value?: string
    onUpdateField: (field: any, status: string) => void
    field: any
    onDescriptionClick?: (title: string, description: any) => void
    onCustomizeClick?: (field: any) => void
}

export function ConfigCheckField({
    title,
    isRequired = false,
    className,
    isDisabled = false,
    value,
    onUpdateField,
    field,
    onDescriptionClick,
    onCustomizeClick,
}: IProps) {
    return (
        <div
            className={cn(
                'flex flex-nowrap border-b border-border gap-4',
                className,
            )}>
            <div className="flex">
                <RadioGroup
                    variant="horizontal"
                    className={cn({
                        'opacity-60': isDisabled,
                    })}
                    gap={'none'}
                    value={value}
                    onValueChange={(value) => {
                        onUpdateField(
                            field,
                            value === 'yes' ? 'Required' : 'Off',
                        )
                    }}
                    disabled={isDisabled}>
                    {/* No Option - Destructive */}
                    <div
                        className={cn(
                            'flex w-[48px] bg-cinnabar-100 justify-center py-3 standard:p-0 standard:items-center',
                        )}>
                        <RadioGroupItem
                            value="no"
                            id={`${field.id}-no_radio`}
                            variant="destructive"
                            size="lg"
                        />
                    </div>

                    {/* Yes Option - Success */}
                    <div
                        className={cn(
                            'flex w-[48px] bg-bright-turquoise-100 justify-center pt-3 standard:p-0 standard:items-center',
                        )}>
                        <RadioGroupItem
                            value="yes"
                            id={`${field.id}-yes_radio`}
                            variant="success"
                            size="lg"
                        />
                    </div>
                </RadioGroup>
            </div>

            <div className="flex flex-grow justify-between items-center gap-3 py-1 pr-2">
                <div className="flex gap-2 items-center">
                    {title}
                    {isRequired && (
                        <RequiredTooltip message="This is a must have field!" />
                    )}
                    {field?.description && (
                        <Button
                            variant={'text'}
                            size={'icon'}
                            onClick={() =>
                                onDescriptionClick &&
                                onDescriptionClick(title, field?.description)
                            }>
                            <MessageSquareIcon />
                        </Button>
                    )}
                </div>
                <Button
                    size={'icon'}
                    variant={'text'}
                    onClick={() => onCustomizeClick && onCustomizeClick(field)}>
                    <PencilIcon />
                </Button>
            </div>
        </div>
    )
}
