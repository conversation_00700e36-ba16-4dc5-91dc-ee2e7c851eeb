'use client'

import { useEffect, useMemo, useState } from 'react'
import vesselTypes from '@/app/lib/vesselTypes'
import {
    UPDATE_CUSTOMISED_COMPONENT_FIELD,
    UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
    CREATE_CUSTOMISED_COMPONENT_FIELD,
    CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
    UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
    CreateCustomisedLogBookConfig,
} from '@/app/lib/graphQL/mutation'
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_LOGBOOK_CONFIG } from '@/app/lib/graphQL/query'
import { getVesselByID } from '@/app/lib/actions'
import { useRouter } from 'next/navigation'
import { useToast } from '@/hooks/use-toast'
import { isCrew } from '@/app/helpers/userHelper'
import {
    filterByVesselType,
    sortTabs,
    getYes<PERSON>heckedStatus,
    getLevelThree<PERSON>ategory,
    isCategorised,
    getComponentsNotInConfig,
    getFieldName,
    isFieldInConfig,
    getLevelThreeCategoryGroup,
    fieldIsGroup,
    sortCustomisedComponentFields,
    isFileField,
    getCategory,
    getTabTitle,
    isInLevel,
    filterFieldClasses,
    subCategoryVisibilityCheck,
    getNoCheckedStatus,
    getFieldsNotInConfig,
    getGroupFields,
} from './actions'
import CustomisedComponentFieldModel from '@/app/offline/models/customisedComponentField'
import CustomisedLogBookConfigModel from '@/app/offline/models/customisedLogBookConfig'
import { uniqueLogbookComponents } from '@/app/helpers/logBookHelper'
import {
    RequiredFields,
    SLALL_LogBookFields,
} from '@/app/lib/logbook-configuration'
import { LogBookConfiguration } from '@/app/lib/logbook-configuration/types'
import {
    Button,
    Card,
    CardContent,
    CardHeader,
    CardTitle,
    ListHeader,
    Tabs,
    TabsContent,
    TabsList,
    TabsTrigger,
} from '@/components/ui'
import { FooterWrapper } from '@/components/footer-wrapper'
import {
    ArrowLeft,
    BanIcon,
    CheckCircle,
    FilePlusIcon,
    RotateCcw,
    SaveIcon,
} from 'lucide-react'
import {
    CheckFieldContent,
    CheckFieldTopContent,
} from '@/components/daily-check-field'
import {
    ComponentsNotInConfig,
    ConfigCheckField,
    DocumentField,
    EditConfigDialog,
    GroupFieldHeaderCheck,
    useEditConfigDialog,
    useDescriptionDialog,
    type IConfigForm,
} from './components/logbook-config'
import { DescriptionDialog } from './components/logbook-config/description-dialog'
import { cn } from '@/app/lib/utils'

export interface ITab {
    title: string
    category: string
    componentClass: string
}

export default function LogbookConfig({
    logBookID,
    vesselID,
}: {
    logBookID: number
    vesselID: number
}) {
    const { toast } = useToast()
    const router = useRouter()
    const [isLoading, setIsLoading] = useState(true)
    const [logBookConfig, setLogBookConfig] = useState<any>(false)
    const [updatedFields, setUpdatedFields] = useState<any>([])
    const [updatedLocalFields, setUpdatedLocalFields] = useState<any>([])
    const [vessel, setVessel] = useState<any>(false)
    const [tab, setTab] = useState<string>('')
    const [tabs, setTabs] = useState<ITab[]>([])
    const [categoryTab, setCategoryTab] = useState<any>()
    const [categoryTabs, setCategoryTabs] = useState<any>()
    const [dailyCheckCategory, setDailyCheckCategory] = useState<any>(false)
    const [dailyCheckCategories, setDailyCheckCategories] = useState<any>(false)
    const [levelThreeCategories, setLevelThreeCategories] = useState<any>(false)
    const [levelThreeCategory, setLevelThreeCategory] = useState<any>(false)
    const [resetCounter, setResetCounter] = useState(-1)
    const [documents, setDocuments] = useState<Array<Record<string, any>>>([])
    const [saving, setSaving] = useState(false)
    const [notify, setNotify] = useState(true)
    const [imCrew, setImCrew] = useState(false)
    const [filteredFields, setFilteredFields] = useState<
        LogBookConfiguration[] | null
    >(null)

    const editConfigDialog = useEditConfigDialog()
    const descriptionDialog = useDescriptionDialog()

    const handleSetVessel = (vessel: any) => {
        if (vessel?.vesselType) {
            const logbookFields = SLALL_LogBookFields.filter((field: any) => {
                if (field?.items?.length > 0) {
                    return field.vesselType.includes(
                        vesselTypes.indexOf(vessel?.vesselType),
                    )
                }
                return false
            })
            var filteredFields: any = []
            logbookFields.map((logbookField: any) => {
                var currentField = logbookField
                var currentFieldItems: any = []
                logbookField.items.map((fields: any) => {
                    if (
                        fields.vesselType.includes(
                            vesselTypes.indexOf(vessel?.vesselType),
                        )
                    ) {
                        if (
                            vessel?.vesselSpecifics?.carriesDangerousGoods ==
                            false
                        ) {
                            if (fields.classes !== 'dangerous-goods-sailing') {
                                currentFieldItems.push(fields)
                            }
                        } else {
                            currentFieldItems.push(fields)
                        }
                    }
                    // if (fields.hasDynamicChildren) {
                    //     const dynamicChildren =
                    //         vessel.defaultRadioLogs.nodes.map(
                    //             (radioLog: any) => {
                    //                 return {
                    //                     value: radioLog.title,
                    //                     label: radioLog.title,
                    //                     vesselType: [
                    //                         0, 1, 2, 3, 4, 5, 6, 7, 8, 9,
                    //                     ],
                    //                     status: 'Required',
                    //                     groupTo: 'RadioLog',
                    //                 }
                    //             },
                    //         )
                    //     currentFieldItems.push(...dynamicChildren)
                    // }
                })
                currentField.items = currentFieldItems
                filteredFields.push(currentField)
            })
            // check on this
            setFilteredFields(filteredFields)
        }
        setVessel(vessel)
        loadLogBookConfig()
    }

    getVesselByID(vesselID, handleSetVessel)

    const slallFields = useMemo(() => {
        return filteredFields ? filteredFields : SLALL_LogBookFields
    }, [filteredFields])

    const [queryLogBookConfig] = useLazyQuery(GET_LOGBOOK_CONFIG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            /**
             * Removes duplicate components from the customisedLogBookComponents array based on the componentClass property.
             * If a duplicate is found, it keeps the component with the highest id value.
             * @param {Object} responseData - The response data containing customisedLogBookComponents.
             * @param {Array} responseData.customisedLogBookComponents.nodes - The array of components to be deduplicated.
             * @returns {Array} The unique components with the highest id value for each componentClass.
             */
            const responseData = response.readOneCustomisedLogBookConfig
            /* const customisedLogBookComponents =
                responseData?.customisedLogBookComponents?.nodes ?? []
            let uniqueComponents = customisedLogBookComponents.reduce(
                (acc: any, current: any) => {
                    const existing = acc.find(
                        (item: any) =>
                            item.componentClass === current.componentClass,
                    )
                    if (existing) {
                        if (Number(current.id) > Number(existing.id)) {
                            return acc.map((item: any) =>
                                item.componentClass === current.componentClass
                                    ? current
                                    : item,
                            )
                        }
                        return acc
                    }
                    return [...acc, current]
                },
                [],
            )
            uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
                const matchingLogBookField = SLALL_LogBookFields.find(
                    (logBookField: any) =>
                        logBookField.componentClass ===
                        uniqueComponent.componentClass,
                )
                if (matchingLogBookField) {
                    return {
                        ...uniqueComponent,
                        title: matchingLogBookField.label,
                    }
                }
                return uniqueComponent
            }) */
            let uniqueComponents = uniqueLogbookComponents(responseData)
            uniqueComponents = uniqueComponents.map((uniqueComponent: any) => {
                if (uniqueComponent.customisedComponentFields.nodes) {
                    uniqueComponent.customisedComponentFields.nodes.sort(
                        sortCustomisedComponentFields,
                    )
                }
                return uniqueComponent
            })
            responseData.customisedLogBookComponents.nodes = uniqueComponents

            const data = filterByVesselType(
                responseData,
                slallFields,
                vesselTypes,
                vessel,
            )

            if (data) {
                setUpdatedLocalFields([])
                setUpdatedFields([])

                setLogBookConfig(data)
                {
                    data.policies.nodes.length > 0 &&
                        setDocuments(data.policies.nodes)
                }
                if (!tab) {
                    const tabs = data.customisedLogBookComponents?.nodes
                        .map((component: any) => ({
                            title: component.title,
                            category: component.category,
                            componentClass: component.componentClass,
                        }))
                        .sort()
                    const logbookFields = slallFields
                    const config = data.customisedLogBookComponents.nodes
                    const defaultConfig = logbookFields.map(
                        (component: any) => component,
                    )
                    var componentsNotInConfig: any = []
                    defaultConfig.forEach((defaultLogBookComponents: any) => {
                        var found = false
                        config.forEach((customisedLogBookComponents: any) => {
                            if (
                                customisedLogBookComponents.componentClass ===
                                defaultLogBookComponents.componentClass
                            ) {
                                found = true
                            }
                        })
                        if (!found) {
                            componentsNotInConfig.push(defaultLogBookComponents)
                        }
                    })
                    const additionalTabs = componentsNotInConfig.map(
                        (component: any) => ({
                            title: component.label,
                            category: component.category,
                            componentClass: component.componentClass,
                        }),
                    )
                    const sortedTabs = sortTabs(
                        [...tabs, ...additionalTabs],
                        slallFields,
                    )

                    setTabs(sortedTabs)
                    setTab(sortedTabs[0].title)

                    // const categoryTabs: string[] = Array.from(
                    //     new Set<string>(
                    //         data.customisedLogBookComponents?.nodes.map(
                    //             (component: any) => component.category,
                    //         ),
                    //     ),
                    // )

                    // setCategoryTabs(categoryTabs)
                    // setCategoryTab(categoryTabs[0])

                    // console.info('Category Tabs', categoryTabs)

                    // var currentTab = false
                    // sortedTabs.forEach((element: any) => {
                    //     if (element.category === categoryTabs[0]) {
                    //         if (!currentTab) {
                    //             console.info('Set Tab 2', element.title)
                    //             setTab(element.title)
                    //         }
                    //         currentTab = element.title
                    //     }
                    // })
                }
            } else {
                document.body.style.cursor = 'wait'
                createCustomisedLogBookConfig({
                    variables: {
                        input: {
                            customisedLogBookID: logBookID,
                        },
                    },
                })
            }
        },
        onError: (error: any) => {
            console.error('queryLogBookConfig error', error)
        },
    })

    const [createCustomisedLogBookConfig] = useMutation(
        CreateCustomisedLogBookConfig,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedLogBookConfig
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    useEffect(() => {
        if (isLoading && vessel) {
            setImCrew(isCrew() || false)
            setIsLoading(false)
            handleSetDailyCheckCategories()
            handleLevelThreeCategories()
        }
    }, [isLoading, vessel])

    const loadLogBookConfig = async () => {
        await queryLogBookConfig({
            variables: {
                id: +logBookID,
            },
        })
    }

    const updateFieldStatus = (field: any, status: string) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const appendedData = [
                ...updatedFields.filter(
                    (updatedField: any) => updatedField.fieldID !== field.id,
                ),
                { fieldID: field.id, status: status },
            ]
            setUpdatedFields(appendedData)
            document.body.style.cursor = 'wait'
            updateCustomisedComponentField({
                variables: {
                    input: {
                        id: field.id,
                        status: status,
                    },
                },
            })
        } else {
            setUpdatedLocalFields([
                ...updatedLocalFields.filter(
                    (updatedField: any) =>
                        updatedField.localID !== field.localID,
                ),
                { ...field, status: status },
            ])
            document.body.style.cursor = 'wait'
            createCustomisedComponentField({
                variables: {
                    input: {
                        customisedFieldTitle: field?.title
                            ? field.title
                            : field.label,
                        customisedLogBookComponentID:
                            field.customisedLogBookComponentID,
                        fieldName: field.value,
                        status: status,
                        sortOrder: field.sortOrder || 0,
                    },
                },
            })
        }
    }

    const [createCustomisedComponentField] = useMutation(
        CREATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponentField
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const deleteLocalCustomisedComponentField = async (id: any) => {
        if (+id > 0) {
            const customisedComponentFieldModel =
                new CustomisedComponentFieldModel()
            await customisedComponentFieldModel.delete(id)
        }
    }

    const [updateCustomisedComponentField] = useMutation(
        UPDATE_CUSTOMISED_COMPONENT_FIELD,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedComponentField
                deleteLocalCustomisedComponentField(data.id)
                if (resetCounter > 0) {
                    setResetCounter(resetCounter - 1)
                }
                if (resetCounter == 0) {
                    setResetCounter(resetCounter - 1)
                }
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const handleUpdateConfigEdit = async (field: any, form: IConfigForm) => {
        document.body.style.cursor = 'wait'
        await updateCustomisedComponentField({
            variables: {
                input: {
                    id: field.id,
                    customisedFieldTitle: form.fieldName,
                    sortOrder: form.sortOrder,
                    description:
                        form.description === '<p><br></p>'
                            ? ''
                            : form.description,
                },
            },
        })

        editConfigDialog.closeDialog()
    }

    const handleSetDailyCheckCategories = () => {
        const logbookFields = slallFields

        const dailyCheckCategories = Array.from(
            new Set(
                logbookFields.filter((field: any) => field.subCategory).length >
                0
                    ? logbookFields
                          .filter((field: any) => field.subCategory)[0]
                          .items.filter((item: any) => item.level !== 3) // Exclude level 3 items
                          .map((item: any) => {
                              return item.fieldSet ? item.fieldSet : 'Other' // Map main categories
                          })
                    : 'Other',
            ),
        )

        setDailyCheckCategories(
            dailyCheckCategories.filter(
                (category: any) =>
                    category !== 'Checks' &&
                    category !== 'Other' &&
                    category !== 'Documentation' &&
                    category !== 'Fuel Checks',
            ),
        )
        if (!dailyCheckCategory) {
            setDailyCheckCategory(dailyCheckCategories[0])
        }
    }

    const handleLevelThreeCategories = () => {
        const logbookFields = slallFields
        const levelThreeCategories: any = Array.from(
            new Set(
                logbookFields
                    .filter((field: any) => field.subCategory)[0]
                    .items.filter((field: any) => field.level === 3)
                    .map((field: any) => {
                        return {
                            fieldSet: field.fieldSet,
                            label: field.label,
                            status: field.status,
                        }
                    }),
            ),
        )
        setLevelThreeCategories(levelThreeCategories)
        if (!levelThreeCategory) {
            setLevelThreeCategory(levelThreeCategories[0].label)
        }
    }

    const mapConfigToDefault = (currentComponent: any) => {
        const logbookFields = slallFields
        setResetCounter(resetCounter + 1)
        const config = logBookConfig.customisedLogBookComponents.nodes
            .filter((component: any) => component.id == currentComponent.id)
            .map((component: any) => component)
        const defaultConfig = logbookFields.map((component: any) => component)
        config.forEach((customisedLogBookComponents: any) => {
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    customisedLogBookComponents.componentClass ===
                    defaultLogBookComponents.componentClass
                ) {
                    customisedLogBookComponents.customisedComponentFields.nodes
                        .filter(
                            (customFields: any, index: number, self: any[]) =>
                                self.findIndex(
                                    (c: any) =>
                                        c.fieldName === customFields.fieldName,
                                ) === index,
                        )
                        .forEach((customFields: any) => {
                            defaultLogBookComponents.items.forEach(
                                (defaultField: any) => {
                                    if (
                                        customFields.fieldName ===
                                        defaultField.value
                                    ) {
                                        const updatedField = updatedFields.find(
                                            (updatedField: any) =>
                                                updatedField.fieldID ===
                                                customFields.id,
                                        )
                                        if (
                                            defaultField.status !=
                                            customFields.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                        if (
                                            updatedField?.fieldID &&
                                            updatedField?.status !=
                                                defaultField.status
                                        ) {
                                            document.body.style.cursor = 'wait'
                                            updateCustomisedComponentField({
                                                variables: {
                                                    input: {
                                                        id: customFields.id,
                                                        status: defaultField.status,
                                                    },
                                                },
                                            })
                                            setResetCounter(resetCounter + 1)
                                        }
                                    }
                                },
                            )
                        })
                }
            })
        })
    }

    // const changeTab = (tab: string) => () => {
    //     setTab(tab?.replace('Logbook', 'LogBook'))
    // }

    // const changeCategoryTab = (tab: string) => () => {
    //     setCategoryTab(tab)
    //     var currentTab = false
    //     tabs.forEach((element: any) => {
    //         if (
    //             element.category.replace('Logbook', 'LogBook') ===
    //             tab?.replace('Logbook', 'LogBook')
    //         ) {
    //             if (!currentTab) {
    //                 setTab(element.title.replace('Logbook', 'LogBook'))
    //             }
    //             currentTab = element.title.replace('Logbook', 'LogBook')
    //         }
    //     })
    // }

    const [updateCustomisedComponent] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const activateCustomisedComponent = (id: number) => () => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: true,
                },
            },
        })
    }

    const deactivateCustomisedComponent = (id: number) => () => {
        document.body.style.cursor = 'wait'
        updateCustomisedComponent({
            variables: {
                input: {
                    id: id,
                    active: false,
                },
            },
        })
    }

    const activateCustomisedSubComponent =
        (component: any, category: string) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields + '||' + category,
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .concat(category)
                                .join('||'),
                        },
                    },
                })
            }
        }

    const deactivateCustomisedSubComponent =
        (component: any, category: string) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields
                                .split('||')
                                .filter((field: any) => field !== category)
                                .join('||'),
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .filter((cat: any) => cat != category)
                                .join('||'),
                        },
                    },
                })
            }
        }

    const activateCustomisedLevelThreeComponent =
        (component: any, levelThree: any) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields:
                                component.subFields + '||' + levelThree.label,
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .concat(levelThree.label)
                                .join('||'),
                        },
                    },
                })
            }
            //if (levelThree?.status === 'disabled') {
            levelThreeCategories.map((category: any) => {
                if (category.label === levelThree.label) {
                    category.status = 'Enabled'
                }
                return category
            })
            //}
        }

    const deactivateCustomisedLevelThreeComponent =
        (component: any, levelThree: any) => () => {
            if (component.subFields) {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: component.subFields
                                .split('||')
                                .filter(
                                    (field: any) => field !== levelThree.label,
                                )
                                .join('||'),
                        },
                    },
                })
            } else {
                document.body.style.cursor = 'wait'
                updateCustomisedComponent({
                    variables: {
                        input: {
                            id: component.id,
                            subFields: dailyCheckCategories
                                .filter((cat: any) => cat != levelThree.label)
                                .join('||'),
                        },
                    },
                })
            }
            //if (levelThree?.status === 'enabled') {
            levelThreeCategories.map((category: any) => {
                if (category.label === levelThree.label) {
                    category.status = 'Disabled'
                }
                return category
            })
            //}
        }

    const changeDailyChecksCategoryTab = (tab: string) => () => {
        setDailyCheckCategory(tab)
    }

    const changeLevelThreeCategoryTab = (tab: string) => () => {
        setLevelThreeCategory(tab)
    }

    const [createCustomisedComponent] = useMutation(
        CREATE_CUSTOMISED_LOGBOOK_COMPONENT,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.createCustomisedComponent
                loadLogBookConfig()
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const isRequiredField = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const isRequired = RequiredFields.includes(field.fieldName)
                ? true
                : false
            if (
                isRequired &&
                !getYesCheckedStatus(field, updatedFields, updatedLocalFields)
            ) {
                updateFieldStatus(field, 'Required')
            }
            return isRequired
        } else {
            const isRequired = RequiredFields.includes(field.value)
                ? true
                : false
            if (
                isRequired &&
                !getYesCheckedStatus(field, updatedFields, updatedLocalFields)
            ) {
                if (
                    updatedLocalFields.filter(
                        (updatedField: any) =>
                            updatedField.localID !== field.localID,
                    ).length === 0
                ) {
                    document.body.style.cursor = 'wait'
                    createCustomisedComponentField({
                        variables: {
                            input: {
                                customisedFieldTitle: field?.title
                                    ? field.title
                                    : field.label,
                                customisedLogBookComponentID:
                                    field.customisedLogBookComponentID,
                                fieldName: field.value,
                                status: 'Required',
                                sortOrder: field.sortOrder || 0,
                            },
                        },
                    })
                }
                setUpdatedLocalFields([
                    ...updatedLocalFields.filter(
                        (updatedField: any) =>
                            updatedField.localID !== field.localID,
                    ),
                    { ...field, status: 'Required' },
                ])
            }
            return isRequired
        }
    }

    const handleSave = (notify = true) => {
        if (notify) {
            setSaving(true)
            toast({
                description: 'Logbook configuration saving...',
            })
        } else {
            setNotify(false)
        }
        const policies =
            documents.length > 0
                ? documents?.map((doc: any) => +doc.id).join(',')
                : ''
        document.body.style.cursor = 'wait'
        updateCustomisedLogBookConfig({
            variables: {
                input: {
                    id: logBookConfig.id,
                    policies: policies,
                },
            },
        })
        setIsLoading(true)
    }

    useEffect(() => {
        if (!isLoading && documents) {
            setNotify(false)
            handleSave(false)
        }
    }, [documents])

    const deleteLocalCustomisedLogBookConfig = async (id: any) => {
        if (+id > 0) {
            const customisedLogBookConfigModel =
                new CustomisedLogBookConfigModel()
            await customisedLogBookConfigModel.delete(id)
        }
    }

    const [updateCustomisedLogBookConfig] = useMutation(
        UPDATE_CUSTOMISED_LOGBOOK_CONFIG,
        {
            onCompleted: (response: any) => {
                document.body.style.cursor = 'auto'
                const data = response.updateCustomisedLogBookConfig
                deleteLocalCustomisedLogBookConfig(data.id)
                if (data.id > 0) {
                    if (notify) {
                        toast({
                            description: 'Logbook configuration saved',
                        })
                        setSaving(false)
                    }
                    setNotify(true)
                }
            },
            onError: (error: any) => {
                document.body.style.cursor = 'auto'
                console.error('updateVessel error', error)
            },
        },
    )

    const filterFieldsWithGroup = (fields: any, componentClass: string) => {
        const logbookFields = slallFields
        const defaultConfig = logbookFields.map((component: any) => component)
        var groupFields: any = []
        var groups: any = []
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                tab?.replace('Logbook', 'LogBook') ===
                    defaultLogBookComponents.label.replace(
                        'Logbook',
                        'LogBook',
                    ) &&
                defaultLogBookComponents.componentClass === componentClass
            ) {
                defaultLogBookComponents.items.forEach((defaultField: any) => {
                    if (defaultField.groupTo && defaultField.level !== 3) {
                        if (!groups.includes(defaultField.groupTo)) {
                            groups.push(defaultField.groupTo)
                        }
                    }
                })
            }
        })
        defaultConfig.forEach((defaultLogBookComponents: any) => {
            if (
                tab?.replace('Logbook', 'LogBook') ===
                defaultLogBookComponents.label.replace('Logbook', 'LogBook')
            ) {
                defaultLogBookComponents.items.forEach(
                    (defaultField: any, index: number) => {
                        groups.forEach((group: any) => {
                            if (
                                defaultField.value === group &&
                                !isFieldInConfig(
                                    defaultField.value,
                                    logBookConfig,
                                    tab,
                                )
                            ) {
                                if (defaultField.level !== 3) {
                                    groupFields.push({
                                        ...defaultField,
                                        description: null,
                                        fieldName: defaultField.value,
                                        id: index + '0',
                                        sortOrder: index,
                                        status: defaultField.status,
                                    })
                                }
                            }
                        })
                    },
                )
            }
        })
        if (groups.length > 0) {
            const flatGroupFields = groupFields.flatMap(
                (group: any) => group.value,
            )
            return [
                ...groupFields.map((group: any) => {
                    let groupData = group
                    fields.map((field: any) => {
                        if (group.value === field.fieldName) {
                            groupData = { ...group, ...field }
                        }
                    })
                    return groupData
                }),
                ...fields.filter(
                    (field: any) => !flatGroupFields.includes(field.fieldName),
                ),
            ]
                .filter((field: any) =>
                    filterFieldsWithGroupCombinedFilter(field),
                )
                .sort(sortCustomisedComponentFields)
        }
        return fields
            .filter((field: any) => filterFieldsWithGroupCombinedFilter(field))
            .sort(sortCustomisedComponentFields)
    }

    const filterFieldsWithGroupCombinedFilter = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false &&
                returnField !== false &&
                groupFields !== false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false &&
                returnField !== false &&
                groupFields !== false
            )
        }
    }

    const customisedComponentFieldsCombinedFilter = (field: any) => {
        if (
            field?.__typename &&
            field.__typename === 'CustomisedComponentField'
        ) {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.fieldName,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.fieldName === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.fieldName === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        } else {
            const logbookFields = slallFields
            const defaultConfig = logbookFields.map(
                (component: any) => component,
            )
            var group = false
            var returnField = false
            var groupFields = false
            defaultConfig.forEach((defaultLogBookComponents: any) => {
                if (
                    tab?.replace('Logbook', 'LogBook') ===
                        defaultLogBookComponents.label.replace(
                            'Logbook',
                            'LogBook',
                        ) ||
                    (defaultLogBookComponents.label === 'Crew Welfare' &&
                        tab === 'Crew Members')
                ) {
                    if (
                        defaultLogBookComponents.items.find(
                            (defaultField: any) =>
                                defaultField.groupTo === field.value,
                        ) !== undefined
                    ) {
                        groupFields = true
                    }
                    defaultLogBookComponents.items.forEach(
                        (defaultField: any) => {
                            if (field.value === defaultField.value) {
                                group = defaultField?.groupTo
                                    ? defaultField.groupTo
                                    : false
                            }
                            if (
                                field.value === defaultField.value &&
                                defaultField.vesselType.includes(
                                    vesselTypes.indexOf(vessel?.vesselType),
                                ) &&
                                defaultField.level !== 3
                            ) {
                                returnField = field
                            }
                        },
                    )
                }
            })
            return (
                group === false && returnField !== false && groupFields == false
            )
        }
    }

    const checkLevelThree = (field: any): boolean => {
        if (
            getLevelThreeCategory(
                field,
                slallFields,
                tab?.replace('Logbook', 'LogBook'),
            )
        ) {
            if (
                getLevelThreeCategory(
                    field,
                    slallFields,
                    tab?.replace('Logbook', 'LogBook'),
                ) === levelThreeCategory
            ) {
                return true
            }
            return false
        }
        return true
    }

    const checkLevelThreeGroup = (field: any) => {
        const levelThreeCategoryGroup = getLevelThreeCategoryGroup(
            field,
            slallFields,
            tab?.replace('Logbook', 'LogBook'),
        )
        if (levelThreeCategoryGroup) {
            if (levelThreeCategoryGroup === levelThreeCategory) {
                return true
            }
            return false
        }
        return true
    }

    useEffect(() => {
        if (!tab && Array.isArray(tabs) && tabs.length > 0) {
            setTab(tabs[0].title)
        }
    }, [tab, tabs])

    const isPreDepartureCheckSectionEnabled = (
        dailyCheckCategory: any,
        dailyCheckCategories: any,
        component: any,
        levelThreeCategory: any = null,
        levelThreeCategories: any = [],
    ) => {
        let isEnabled = false
        if (dailyCheckCategory !== 'Engine Checks') {
            if (component.subFields) {
                if (dailyCheckCategories.includes(dailyCheckCategory)) {
                    const subFieldsList = component.subFields.split('||')
                    isEnabled = subFieldsList.includes(dailyCheckCategory)
                } else {
                    // do nothing
                }
            } else if (!isEnabled) {
                isEnabled = component.active
            }
        } else {
            // Engine checks
            isEnabled = true
            const result = levelThreeCategories.some(
                (category: any) => category.label === levelThreeCategory,
            )
            if (result) {
                if (component.subFields) {
                    const subFieldsList = component.subFields.split('||')
                    isEnabled = subFieldsList.includes(levelThreeCategory)
                } else if (!isEnabled) {
                    isEnabled = component.active
                }
            } else {
                // do nothing
            }
        }
        return isEnabled
    }

    const isRadioLogsChecked = () => {
        if (logBookConfig) {
            const tripReportComponent =
                logBookConfig.customisedLogBookComponents.nodes.find(
                    (component: any) =>
                        component.componentClass ===
                        'TripReport_LogBookComponent',
                )

            if (tripReportComponent?.customisedComponentFields?.nodes) {
                const radioLogs =
                    tripReportComponent.customisedComponentFields.nodes.find(
                        (field: any) =>
                            field.fieldName === 'RadioLog' &&
                            field.status === 'Required',
                    )
                if (radioLogs) {
                    return true
                }
            }
        }
        return false
    }

    const filteredTabs = useMemo(() => {
        return tabs.filter(
            (element) =>
                element.title !== 'Crew Welfare' &&
                element.title !== 'Crew Training' &&
                element.componentClass !== 'Engine_LogBookComponent' &&
                element.componentClass !== 'Engineer_LogBookComponent' &&
                element.componentClass !== 'Fuel_LogBookComponent' &&
                element.componentClass !== 'Supernumerary_LogBookComponent',
        )
    }, [tabs])

    const componentsNotInConfig = useMemo(() => {
        const fields = logBookConfig
            ? getComponentsNotInConfig(slallFields, logBookConfig) ?? []
            : []
        return fields
    }, [logBookConfig, slallFields])

    const isTabOpen = (component: any): boolean => {
        return (
            tab?.replace('Logbook', 'LogBook') ===
                component.title.replace('Logbook', 'LogBook') ||
            (tab === 'Crew Members' && component.title === 'Crew Welfare') ||
            (tab === 'Engine Reports' &&
                (component.componentClass === 'Engineer_LogBookComponent' ||
                    component.componentClass === 'Fuel_LogBookComponent'))
        )
    }

    const getFilteredAndSortedCustomisedComponentFields = (component: any) => {
        const fields = component.customisedComponentFields.nodes
            .filter(
                (customFields: any, index: number, self: any[]) =>
                    self.findIndex(
                        (c: any) => c.fieldName === customFields.fieldName,
                    ) === index,
            )
            .filter((field: any) =>
                customisedComponentFieldsCombinedFilter(field),
            )
            .sort(sortCustomisedComponentFields)

        return fields
    }

    const openEditConfigDialog = (field: any) => {
        editConfigDialog.openDialog({
            field,
            title: field?.customisedFieldTitle
                ? field?.customisedFieldTitle
                : getFieldName(field, slallFields),
            isFieldGroup: fieldIsGroup(
                field,
                slallFields,
                tab?.replace('Logbook', 'LogBook'),
            ),
        })
    }

    console.info('Tab', tab, tabs)
    console.info(
        'Daily Check Category',
        dailyCheckCategory,
        dailyCheckCategories,
    )
    console.info(
        'Level Three Category',
        levelThreeCategory,
        levelThreeCategories,
    )

    return (
        <div className="space-y-6">
            <ListHeader title={`Logbook Configuration: ${vessel?.title}`} />
            <Card>
                <CardHeader>
                    <CardTitle className="text-2xl font-semibold">
                        Vessel Type: {vessel?.vesselType?.replaceAll('_', ' ')}
                    </CardTitle>
                </CardHeader>
                <CardContent>
                    {!isLoading && filteredTabs.length > 0 && (
                        <>
                            <Tabs
                                value={tab}
                                onValueChange={(newTab) => {
                                    setTab(newTab)
                                }}>
                                <TabsList>
                                    {filteredTabs.map(
                                        (element, index: number) => {
                                            return (
                                                <TabsTrigger
                                                    key={element.title}
                                                    value={element.title}>
                                                    {getTabTitle(
                                                        element,
                                                        SLALL_LogBookFields,
                                                    )}
                                                </TabsTrigger>
                                            )
                                        },
                                    )}
                                </TabsList>
                                {filteredTabs.map((element) => (
                                    <TabsContent value={element.title}>
                                        {getTabTitle(
                                            element,
                                            SLALL_LogBookFields,
                                        )}
                                    </TabsContent>
                                ))}
                            </Tabs>
                            <div className="mt-4 flex flex-col gap-4">
                                {logBookConfig.customisedLogBookComponents?.nodes
                                    // Crew Members must come first before Crew Welfare
                                    .sort((a: any, b: any) => {
                                        if (
                                            a.componentClass ===
                                            'CrewMembers_LogBookComponent'
                                        )
                                            return -1
                                        if (
                                            b.componentClass ===
                                            'CrewMembers_LogBookComponent'
                                        )
                                            return 1
                                        return 0
                                    })
                                    .map((component: any) => (
                                        <div
                                            key={component.id}
                                            className={`border rounded-lg grid grid-cols-3 border-border ${isTabOpen(component) ? '' : 'hidden'} ${component.label}`}>
                                            <div className="flex bg-background border-b border-border flex-row md:flex-col items-center md:items-start justify-between gap-4 col-span-3 md:col-span-1 p-4">
                                                <div className="text-xl font-medium flex items-center gap-2">
                                                    {getTabTitle(
                                                        component,
                                                        SLALL_LogBookFields,
                                                    )}{' '}
                                                    {component.active ? (
                                                        ''
                                                    ) : (
                                                        <BanIcon className="text-destructive" />
                                                    )}
                                                </div>
                                                {!isCategorised(
                                                    component,
                                                    slallFields,
                                                    logBookConfig,
                                                ) &&
                                                    !imCrew && (
                                                        <div className="flex gap-2">
                                                            {component.active ? (
                                                                <>
                                                                    <Button
                                                                        onClick={deactivateCustomisedComponent(
                                                                            component.id,
                                                                        )}
                                                                        iconLeft={
                                                                            BanIcon
                                                                        }
                                                                        variant={
                                                                            'destructive'
                                                                        }>
                                                                        Disable
                                                                    </Button>
                                                                    <Button
                                                                        iconLeft={
                                                                            RotateCcw
                                                                        }
                                                                        variant={
                                                                            'outline'
                                                                        }
                                                                        onClick={() =>
                                                                            mapConfigToDefault(
                                                                                component,
                                                                            )
                                                                        }>
                                                                        Reset
                                                                        Default
                                                                    </Button>
                                                                </>
                                                            ) : (
                                                                <Button
                                                                    variant="primary"
                                                                    iconLeft={
                                                                        CheckCircle
                                                                    }
                                                                    onClick={activateCustomisedComponent(
                                                                        component.id,
                                                                    )}>
                                                                    Enable
                                                                </Button>
                                                            )}
                                                        </div>
                                                    )}
                                            </div>
                                            {isCategorised(
                                                component,
                                                slallFields,
                                                logBookConfig,
                                            ) && (
                                                <>
                                                    <div className="md:col-span-2 hidden md:block bg-background border-b border-border">
                                                        {/* Categorised */}
                                                    </div>
                                                    {/* remove later */}
                                                    <div className="col-span-3 p-4 overflow-x-auto">
                                                        {dailyCheckCategories && (
                                                            <Tabs
                                                                value={
                                                                    dailyCheckCategory
                                                                }
                                                                onValueChange={(
                                                                    newTab,
                                                                ) => {
                                                                    setDailyCheckCategory(
                                                                        newTab,
                                                                    )
                                                                }}>
                                                                <TabsList>
                                                                    {dailyCheckCategories.map(
                                                                        (
                                                                            category: any,
                                                                        ) => (
                                                                            <TabsTrigger
                                                                                key={
                                                                                    category
                                                                                }
                                                                                value={
                                                                                    category
                                                                                }>
                                                                                {category !==
                                                                                'Engine Checks'
                                                                                    ? category
                                                                                    : 'Engine, steering, electrical & alt power'}
                                                                            </TabsTrigger>
                                                                        ),
                                                                    )}
                                                                </TabsList>
                                                            </Tabs>
                                                        )}
                                                    </div>
                                                    {levelThreeCategories && (
                                                        <div
                                                            className={cn(
                                                                'col-span-3 p-4',
                                                                !isInLevel(
                                                                    dailyCheckCategory,
                                                                    3,
                                                                    slallFields,
                                                                ) && 'hidden',
                                                            )}>
                                                            <Tabs
                                                                value={
                                                                    levelThreeCategory
                                                                }
                                                                onValueChange={(
                                                                    newValue,
                                                                ) =>
                                                                    setLevelThreeCategory(
                                                                        newValue,
                                                                    )
                                                                }>
                                                                <TabsList>
                                                                    {levelThreeCategories.map(
                                                                        (
                                                                            category: any,
                                                                        ) => (
                                                                            <TabsTrigger
                                                                                key={
                                                                                    category.label
                                                                                }
                                                                                value={
                                                                                    category.label
                                                                                }>
                                                                                {
                                                                                    category.label
                                                                                }
                                                                            </TabsTrigger>
                                                                        ),
                                                                    )}
                                                                </TabsList>
                                                            </Tabs>
                                                        </div>
                                                    )}
                                                    {dailyCheckCategories &&
                                                        dailyCheckCategories.map(
                                                            (
                                                                category: any,
                                                                index: number,
                                                            ) => (
                                                                <div
                                                                    key={index}
                                                                    className={cn(
                                                                        'flex border-b border-border border-r border-t justify-between bg-background',
                                                                        isInLevel(
                                                                            dailyCheckCategory,
                                                                            3,
                                                                            slallFields,
                                                                        )
                                                                            ? 'col-span-3 flex-row items-center'
                                                                            : 'col-span-3 md:col-span-1 flex-row md:flex-col items-center md:items-start',
                                                                        dailyCheckCategory !==
                                                                            category &&
                                                                            'hidden',
                                                                    )}>
                                                                    <div className="p-4 font-medium text-xl">
                                                                        {category !==
                                                                        'Engine Checks'
                                                                            ? category
                                                                            : 'Engine, steering, electrical & alt power'}{' '}
                                                                        {component.active ? (
                                                                            ''
                                                                        ) : (
                                                                            <BanIcon className="text-destructive" />
                                                                        )}
                                                                    </div>
                                                                    {category !==
                                                                    'Engine Checks' ? (
                                                                        <div className="mt-2 p-4">
                                                                            {(component.subFields ==
                                                                                null ||
                                                                                (component?.subFields &&
                                                                                    component.subFields
                                                                                        .split(
                                                                                            '||',
                                                                                        )
                                                                                        .includes(
                                                                                            category,
                                                                                        ))) &&
                                                                            !imCrew ? (
                                                                                <>
                                                                                    <Button
                                                                                        onClick={deactivateCustomisedSubComponent(
                                                                                            component,
                                                                                            category,
                                                                                        )}
                                                                                        iconLeft={
                                                                                            BanIcon
                                                                                        }
                                                                                        variant={
                                                                                            'destructive'
                                                                                        }>
                                                                                        Disable
                                                                                    </Button>
                                                                                </>
                                                                            ) : (
                                                                                <>
                                                                                    {!imCrew && (
                                                                                        <>
                                                                                            <Button
                                                                                                onClick={activateCustomisedSubComponent(
                                                                                                    component,
                                                                                                    category,
                                                                                                )}
                                                                                                iconLeft={
                                                                                                    CheckCircle
                                                                                                }
                                                                                                variant={
                                                                                                    'primary'
                                                                                                }>
                                                                                                Enable
                                                                                            </Button>
                                                                                        </>
                                                                                    )}
                                                                                </>
                                                                            )}
                                                                        </div>
                                                                    ) : (
                                                                        <div className="flex items-center pr-4">
                                                                            {/* Level Three Categories Enable/Disable Buttons */}
                                                                            {levelThreeCategories &&
                                                                                levelThreeCategories.map(
                                                                                    (
                                                                                        levelThree: any,
                                                                                    ) =>
                                                                                        levelThree.label ===
                                                                                            levelThreeCategory && (
                                                                                            <div
                                                                                                key={
                                                                                                    levelThree.label
                                                                                                }>
                                                                                                {(component.subFields ==
                                                                                                    null ||
                                                                                                    (component?.subFields &&
                                                                                                        component.subFields
                                                                                                            .split(
                                                                                                                '||',
                                                                                                            )
                                                                                                            .includes(
                                                                                                                levelThree.label,
                                                                                                            ))) &&
                                                                                                !imCrew ? (
                                                                                                    <>
                                                                                                        <Button
                                                                                                            onClick={deactivateCustomisedLevelThreeComponent(
                                                                                                                component,
                                                                                                                levelThree,
                                                                                                            )}
                                                                                                            iconLeft={
                                                                                                                BanIcon
                                                                                                            }
                                                                                                            variant={
                                                                                                                'destructive'
                                                                                                            }>
                                                                                                            Disable
                                                                                                        </Button>
                                                                                                    </>
                                                                                                ) : (
                                                                                                    <>
                                                                                                        {!imCrew && (
                                                                                                            <>
                                                                                                                <Button
                                                                                                                    onClick={activateCustomisedLevelThreeComponent(
                                                                                                                        component,
                                                                                                                        levelThree,
                                                                                                                    )}
                                                                                                                    iconLeft={
                                                                                                                        CheckCircle
                                                                                                                    }
                                                                                                                    variant={
                                                                                                                        'primary'
                                                                                                                    }>
                                                                                                                    Enable
                                                                                                                </Button>
                                                                                                            </>
                                                                                                        )}
                                                                                                    </>
                                                                                                )}
                                                                                            </div>
                                                                                        ),
                                                                                )}
                                                                        </div>
                                                                    )}
                                                                </div>
                                                            ),
                                                        )}
                                                </>
                                            )}

                                            <div
                                                className={cn(
                                                    'col-span-3 md:col-span-2 border-b border-t border-border',
                                                    tab ===
                                                        'Pre-Departure Checks' &&
                                                        isInLevel(
                                                            dailyCheckCategory,
                                                            3,
                                                            slallFields,
                                                        ) &&
                                                        'hidden',
                                                )}>
                                                <CheckFieldTopContent className="justify-start" />
                                                {getFilteredAndSortedCustomisedComponentFields(
                                                    component,
                                                ).map(
                                                    (
                                                        field: any,
                                                        index: any,
                                                    ) => (
                                                        <div
                                                            key={field.id}
                                                            className={`${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === getCategory(field, slallFields, tab) && checkLevelThree(field) ? '' : 'hidden') : ''}`}>
                                                            <CheckFieldContent>
                                                                <ConfigCheckField
                                                                    title={
                                                                        field?.customisedFieldTitle
                                                                            ? field.customisedFieldTitle
                                                                            : getFieldName(
                                                                                  field,
                                                                                  slallFields,
                                                                              )
                                                                    }
                                                                    field={
                                                                        field
                                                                    }
                                                                    isRequired={isRequiredField(
                                                                        field,
                                                                    )}
                                                                    className={cn(
                                                                        subCategoryVisibilityCheck(
                                                                            component,
                                                                            tab?.replace(
                                                                                'Logbook',
                                                                                'LogBook',
                                                                            ),
                                                                            dailyCheckCategory,
                                                                            levelThreeCategories,
                                                                            levelThreeCategory,
                                                                        ),
                                                                        !component.active &&
                                                                            'pointer-events-none opacity-50',
                                                                        `field-${getCategory(field, slallFields, tab)}`,
                                                                    )}
                                                                    isDisabled={
                                                                        imCrew ||
                                                                        isRequiredField(
                                                                            field,
                                                                        )
                                                                    }
                                                                    value={
                                                                        !getNoCheckedStatus(
                                                                            field,
                                                                            updatedFields,
                                                                            updatedLocalFields,
                                                                        )
                                                                            ? 'yes'
                                                                            : 'no'
                                                                    }
                                                                    onUpdateField={(
                                                                        field,
                                                                        status,
                                                                    ) =>
                                                                        updateFieldStatus(
                                                                            field,
                                                                            status,
                                                                        )
                                                                    }
                                                                    onDescriptionClick={(
                                                                        title,
                                                                        description,
                                                                    ) =>
                                                                        descriptionDialog.openDialog(
                                                                            title,
                                                                            description,
                                                                        )
                                                                    }
                                                                    onCustomizeClick={(
                                                                        field,
                                                                    ) =>
                                                                        openEditConfigDialog(
                                                                            field,
                                                                        )
                                                                    }
                                                                />
                                                                {field?.fieldType ===
                                                                    'files' ||
                                                                    (isFileField(
                                                                        field,
                                                                        slallFields,
                                                                        tab?.replace(
                                                                            'Logbook',
                                                                            'LogBook',
                                                                        ),
                                                                    ) && (
                                                                        <div
                                                                            className={cn(
                                                                                'px-4',
                                                                                !component.active &&
                                                                                    'pointer-events-none opacity-50',
                                                                                getCategory(
                                                                                    field,
                                                                                    slallFields,
                                                                                    tab?.replace(
                                                                                        'Logbook',
                                                                                        'LogBook',
                                                                                    ),
                                                                                ),
                                                                                isCategorised(
                                                                                    component,
                                                                                    slallFields,
                                                                                    logBookConfig,
                                                                                ) &&
                                                                                    dailyCheckCategory !==
                                                                                        field.fieldSet &&
                                                                                    'hidden',
                                                                            )}>
                                                                            <div></div>
                                                                            <div className="colspan-3">
                                                                                <DocumentField
                                                                                    documents={
                                                                                        documents
                                                                                    }
                                                                                    setDocuments={
                                                                                        setDocuments
                                                                                    }
                                                                                    fileUploadText="Policies"
                                                                                />
                                                                            </div>
                                                                        </div>
                                                                    ))}
                                                            </CheckFieldContent>
                                                        </div>
                                                    ),
                                                )}
                                                {getFieldsNotInConfig(
                                                    component,
                                                    slallFields,
                                                    logBookConfig,
                                                )
                                                    .filter((field: any) =>
                                                        customisedComponentFieldsCombinedFilter(
                                                            field,
                                                        ),
                                                    )
                                                    .map(
                                                        (
                                                            field: any,
                                                            index: number,
                                                        ) => (
                                                            <div
                                                                key={
                                                                    field.value +
                                                                    '_' +
                                                                    index
                                                                }
                                                                className={`${filterFieldClasses(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === (field?.fieldSet ? field.fieldSet : 'Other') && checkLevelThree(field) ? '' : 'hidden') : ''}`}>
                                                                {/* NOT IN CONFIG CHECK FIELD */}
                                                                <ConfigCheckField
                                                                    title={getFieldName(
                                                                        field,
                                                                        slallFields,
                                                                    )}
                                                                    field={
                                                                        field
                                                                    }
                                                                    isRequired={isRequiredField(
                                                                        {
                                                                            ...field,
                                                                            localID:
                                                                                index,
                                                                            customisedLogBookComponentID:
                                                                                component.id,
                                                                            status: 'Required',
                                                                        },
                                                                    )}
                                                                    isDisabled={
                                                                        imCrew ||
                                                                        isRequiredField(
                                                                            {
                                                                                ...field,
                                                                                localID:
                                                                                    index,
                                                                                customisedLogBookComponentID:
                                                                                    component.id,
                                                                                status: 'Required',
                                                                            },
                                                                        )
                                                                    }
                                                                    value={
                                                                        undefined //default value for not in config field is undefined
                                                                    }
                                                                    onUpdateField={(
                                                                        field,
                                                                        status,
                                                                    ) =>
                                                                        updateFieldStatus(
                                                                            {
                                                                                ...field,
                                                                                customisedLogBookComponentID:
                                                                                    component.id,
                                                                            },
                                                                            status,
                                                                        )
                                                                    }
                                                                    className={`${subCategoryVisibilityCheck(component, tab?.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))}`}
                                                                    onCustomizeClick={() => {
                                                                        toast({
                                                                            description:
                                                                                'You can rename this field after setting its value to either "Yes" or "No."',
                                                                        })
                                                                    }}
                                                                />

                                                                {field?.fieldType ===
                                                                    'files' && (
                                                                    <div
                                                                        className={` px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === field.fieldSet ? '' : 'hidden') : ''}`}>
                                                                        <div></div>
                                                                        <div className="colspan-3">
                                                                            <DocumentField
                                                                                documents={
                                                                                    documents
                                                                                }
                                                                                setDocuments={
                                                                                    setDocuments
                                                                                }
                                                                                fileUploadText="Policies"
                                                                                showFileUpload={
                                                                                    !imCrew
                                                                                }
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        ),
                                                    )}
                                            </div>
                                            {/* Group Component Fields */}
                                            <div className="col-span-3">
                                                {filterFieldsWithGroup(
                                                    component.customisedComponentFields.nodes.filter(
                                                        (
                                                            customFields: any,
                                                            index: number,
                                                            self: any[],
                                                        ) =>
                                                            self.findIndex(
                                                                (c: any) =>
                                                                    c.fieldName ===
                                                                    customFields.fieldName,
                                                            ) === index,
                                                    ),
                                                    component.componentClass,
                                                ).map(
                                                    (
                                                        field: any,
                                                        index: number,
                                                    ) => (
                                                        <div
                                                            key={field.id}
                                                            className={`${subCategoryVisibilityCheck(component, tab?.replace('Logbook', 'LogBook'), dailyCheckCategory, levelThreeCategories, levelThreeCategory)} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook')) && checkLevelThreeGroup(field) ? '' : 'hidden') : ''} border rounded-lg m-4 border-border`}>
                                                            <div
                                                                className={`${component.active ? '' : 'pointer-events-none opacity-50'} field-${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))}`}>
                                                                <GroupFieldHeaderCheck
                                                                    className="border-b border-border p-3 bg-background"
                                                                    title={
                                                                        field?.customisedFieldTitle
                                                                            ? field.customisedFieldTitle
                                                                            : getFieldName(
                                                                                  field,
                                                                                  slallFields,
                                                                              )
                                                                    }
                                                                    value={
                                                                        field?.id >
                                                                        0
                                                                            ? !getNoCheckedStatus(
                                                                                  {
                                                                                      ...field,
                                                                                      localID:
                                                                                          index,
                                                                                      customisedLogBookComponentID:
                                                                                          component.id,
                                                                                  },
                                                                                  updatedFields,
                                                                                  updatedLocalFields,
                                                                              )
                                                                                ? 'yes'
                                                                                : 'no'
                                                                            : undefined
                                                                    }
                                                                    field={
                                                                        field
                                                                    }
                                                                    isDisabled={
                                                                        imCrew
                                                                    }
                                                                    onUpdateField={(
                                                                        field,
                                                                        status,
                                                                    ) => {
                                                                        updateFieldStatus(
                                                                            {
                                                                                ...field,
                                                                                customisedLogBookComponentID:
                                                                                    component.id,
                                                                            },
                                                                            status,
                                                                        )
                                                                    }}
                                                                    onCustomizeClick={(
                                                                        field,
                                                                    ) => {
                                                                        if (
                                                                            field?.id >
                                                                            0
                                                                        ) {
                                                                            openEditConfigDialog(
                                                                                field,
                                                                            )
                                                                        } else {
                                                                            toast(
                                                                                {
                                                                                    description:
                                                                                        'You can rename this group after setting its value to either "Yes" or "No."',
                                                                                },
                                                                            )
                                                                        }
                                                                    }}
                                                                    onDescriptionClick={(
                                                                        title,
                                                                        description,
                                                                    ) =>
                                                                        descriptionDialog.openDialog(
                                                                            title,
                                                                            description,
                                                                        )
                                                                    }
                                                                />
                                                                <div className="">
                                                                    <CheckFieldTopContent className="justify-start" />
                                                                    <div
                                                                        className={`md:col-span-2 ${
                                                                            getNoCheckedStatus(
                                                                                {
                                                                                    ...field,
                                                                                    localID:
                                                                                        index,
                                                                                    customisedLogBookComponentID:
                                                                                        component.id,
                                                                                },
                                                                                updatedFields,
                                                                                updatedLocalFields,
                                                                            )
                                                                                ? 'pointer-events-none opacity-50'
                                                                                : ''
                                                                        }`}>
                                                                        {getGroupFields(
                                                                            field.fieldName,
                                                                            slallFields,
                                                                            tab?.replace(
                                                                                'Logbook',
                                                                                'LogBook',
                                                                            ),
                                                                            logBookConfig,
                                                                        ).map(
                                                                            (
                                                                                groupField: any,
                                                                                index: number,
                                                                            ) => (
                                                                                <ConfigCheckField
                                                                                    key={
                                                                                        groupField.value +
                                                                                        '_' +
                                                                                        index
                                                                                    }
                                                                                    title={
                                                                                        groupField?.customisedFieldTitle
                                                                                            ? groupField.customisedFieldTitle
                                                                                            : getFieldName(
                                                                                                  groupField,
                                                                                                  slallFields,
                                                                                              )
                                                                                    }
                                                                                    isRequired={isRequiredField(
                                                                                        groupField,
                                                                                    )}
                                                                                    field={
                                                                                        groupField
                                                                                    }
                                                                                    className={`${groupField?.id} ${component.active ? '' : 'pointer-events-none opacity-50 77'} ${getCategory(groupField, slallFields, tab?.replace('Logbook', 'LogBook'))}`}
                                                                                    isDisabled={
                                                                                        isRequiredField(
                                                                                            groupField,
                                                                                        ) ||
                                                                                        imCrew
                                                                                    }
                                                                                    value={
                                                                                        groupField?.id >
                                                                                        0
                                                                                            ? !getNoCheckedStatus(
                                                                                                  groupField,
                                                                                                  updatedFields,
                                                                                                  updatedLocalFields,
                                                                                              )
                                                                                                ? 'yes'
                                                                                                : 'no'
                                                                                            : undefined
                                                                                    }
                                                                                    onUpdateField={(
                                                                                        field,
                                                                                        status,
                                                                                    ) =>
                                                                                        updateFieldStatus(
                                                                                            field,
                                                                                            status,
                                                                                        )
                                                                                    }
                                                                                    onDescriptionClick={(
                                                                                        title,
                                                                                        description,
                                                                                    ) =>
                                                                                        descriptionDialog.openDialog(
                                                                                            title,
                                                                                            description,
                                                                                        )
                                                                                    }
                                                                                    onCustomizeClick={(
                                                                                        field,
                                                                                    ) => {
                                                                                        if (
                                                                                            field?.id >
                                                                                            0
                                                                                        ) {
                                                                                            openEditConfigDialog(
                                                                                                field,
                                                                                            )
                                                                                        } else {
                                                                                            toast(
                                                                                                {
                                                                                                    description:
                                                                                                        'You can rename this group after setting its value to either "Yes" or "No."',
                                                                                                },
                                                                                            )
                                                                                        }
                                                                                    }}
                                                                                />
                                                                            ),
                                                                        )}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                            {field?.fieldType ===
                                                                'files' ||
                                                                (isFileField(
                                                                    field,
                                                                    slallFields,
                                                                    tab,
                                                                ) && (
                                                                    <div
                                                                        className={` px-4 ${component.active ? '' : 'pointer-events-none opacity-50'} ${getCategory(field, slallFields, tab?.replace('Logbook', 'LogBook'))} ${isCategorised(component, slallFields, logBookConfig) ? (dailyCheckCategory === field.fieldSet ? '' : 'hidden') : ''}`}>
                                                                        <div></div>
                                                                        <div className="colspan-3">
                                                                            <DocumentField
                                                                                documents={
                                                                                    documents
                                                                                }
                                                                                setDocuments={
                                                                                    setDocuments
                                                                                }
                                                                                fileUploadText="Policies"
                                                                            />
                                                                        </div>
                                                                    </div>
                                                                ))}
                                                        </div>
                                                    ),
                                                )}
                                            </div>
                                        </div>
                                    ))}
                            </div>

                            {logBookConfig && (
                                <ComponentsNotInConfig
                                    tab={tab}
                                    logBookConfig={logBookConfig}
                                    slallFields={slallFields}
                                    fields={componentsNotInConfig}
                                    imCrew={imCrew}
                                    createCustomisedComponent={
                                        createCustomisedComponent
                                    }
                                />
                            )}
                        </>
                    )}
                </CardContent>
            </Card>
            <FooterWrapper>
                <Button
                    iconLeft={ArrowLeft}
                    variant="back"
                    onClick={() => {
                        router.back()
                    }}>
                    Cancel
                </Button>
                {!isLoading &&
                    tabs &&
                    logBookConfig.customisedLogBookComponents?.nodes?.length >
                        0 &&
                    logBookConfig.customisedLogBookComponents?.nodes?.map(
                        (component: any) => (
                            <div
                                key={component.id}
                                className={`${tab?.replace('Logbook', 'LogBook') === component.title.replace('Logbook', 'LogBook') ? '' : 'hidden'}`}>
                                {isCategorised(
                                    component,
                                    slallFields,
                                    logBookConfig,
                                ) && (
                                    <>
                                        {component.active ? (
                                            <>
                                                {!imCrew && (
                                                    <>
                                                        <Button
                                                            className="mr-2"
                                                            variant="destructive"
                                                            iconLeft={BanIcon}
                                                            onClick={deactivateCustomisedComponent(
                                                                component.id,
                                                            )}>
                                                            Disable
                                                        </Button>
                                                        <Button
                                                            variant="outline"
                                                            iconLeft={RotateCcw}
                                                            onClick={() =>
                                                                mapConfigToDefault(
                                                                    component,
                                                                )
                                                            }>
                                                            Reset to default
                                                        </Button>
                                                    </>
                                                )}
                                            </>
                                        ) : (
                                            <>
                                                {!imCrew && (
                                                    <Button
                                                        variant="primary"
                                                        onClick={activateCustomisedComponent(
                                                            component.id,
                                                        )}>
                                                        Enable
                                                    </Button>
                                                )}
                                            </>
                                        )}
                                    </>
                                )}
                            </div>
                        ),
                    )}
                {!isLoading &&
                    tabs &&
                    logBookConfig &&
                    componentsNotInConfig.map((component: any) => (
                        <div
                            key={component.label}
                            className={`${tab?.replace('Logbook', 'LogBook') === component.label.replace('Logbook', 'LogBook') ? '' : 'hidden'}`}>
                            {!imCrew && (
                                <Button
                                    variant="primaryOutline"
                                    iconLeft={FilePlusIcon}
                                    onClick={() => {
                                        document.body.style.cursor = 'wait'
                                        createCustomisedComponent({
                                            variables: {
                                                input: {
                                                    title: component.label,
                                                    sortOrder:
                                                        component.sortOrder ||
                                                        0,
                                                    category:
                                                        component.category,
                                                    customisedLogBookConfigID:
                                                        logBookConfig.id,
                                                    componentClass:
                                                        component.componentClass,
                                                    active: true,
                                                },
                                            },
                                        })
                                    }}>
                                    {`Add ${component.label}`}
                                </Button>
                            )}
                        </div>
                    ))}
                {!imCrew && (
                    <Button
                        variant="primary"
                        onClick={() => handleSave()}
                        iconLeft={SaveIcon}
                        disabled={saving}>
                        {saving ? 'Saving' : 'Save'}
                    </Button>
                )}
            </FooterWrapper>

            <EditConfigDialog
                title={editConfigDialog.title}
                field={editConfigDialog.selectedField}
                isFieldGroup={editConfigDialog.isFieldGroup}
                defaultValues={editConfigDialog.form}
                isOpen={editConfigDialog.isOpen}
                onOpenChange={editConfigDialog.onOpenChange}
                onSave={(field, form) => handleUpdateConfigEdit(field, form)}
            />

            <DescriptionDialog
                open={descriptionDialog.open}
                onOpenChange={descriptionDialog.onOpenChange}
                title={descriptionDialog.title}
                content={descriptionDialog.content}
            />
        </div>
    )
}
