'use client'
import Loading from '../loading'
import { canUseTripSchedule, onlyAdminAccess } from '../helpers/userHelper'
import { useEffect, useState } from 'react'
import TripScheduleServiceList from '../ui/schedules/trip-schedule-service-list'

const TripScheduleServiceListPage = () => {
    const [useTripSchedule, setUseTripSchedule] = useState(true)
    useEffect(() => {
        onlyAdminAccess()
        setUseTripSchedule(canUseTripSchedule())
    }, [])
    return (
        <div>
            {useTripSchedule ? (
                <TripScheduleServiceList />
            ) : (
                <Loading message="Trip schedules are not enabled. Please enable them in the settings to use this feature." />
            )}
        </div>
    )
}

export default TripScheduleServiceListPage
