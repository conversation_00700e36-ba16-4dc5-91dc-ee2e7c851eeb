'use client'

import {
    CREATE_SECTION_MEMBER_COMMENT,
    UPDATE_ENGINE,
    UPDATE_ENGINESTARTSTOP,
    UPDATE_SECTION_MEMBER_COMMENT,
} from '@/app/lib/graphQL/mutation'
import {
    GET_ENGINES,
    GET_SECTION_MEMBER_COMMENTS,
} from '@/app/lib/graphQL/query'
import EngineModel from '@/app/offline/models/engine'
import SectionMemberCommentModel from '@/app/offline/models/sectionMemberComment'
import { H4 } from '@/components/ui'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useLazyQuery, useMutation } from '@apollo/client'
import { useDebounceFn } from '@reactuses/core'
import { useSearchParams } from 'next/navigation'
import { Dispatch, useEffect, useRef, useState } from 'react'

interface IProps {
    offline?: boolean
    locked: boolean
    edit_logBookEntry: boolean
    engineList: any
    vesselDailyCheck: any
    setSectionEngineComment: Dispatch<any>
    getComment: (fieldName: string, commentType?: string) => any
    setComments: any
    updateSectionMemberComment: Function
    createSectionMemberComment: Function
}

export default function EngineHour({
    offline = false,
    engineList,
    locked,
    edit_logBookEntry,
    vesselDailyCheck,
    setSectionEngineComment,
    getComment,
    setComments,
    updateSectionMemberComment,
    createSectionMemberComment,
}: IProps) {
    const searchParams = useSearchParams()
    const logentryID = searchParams.get('logentryID') ?? 0
    const engineModel = new EngineModel()
    const commentModel = new SectionMemberCommentModel()
    const commentRef = useRef<HTMLTextAreaElement | null>(null)
    const [engines, setEngines] = useState<any>([])
    const [engineHours, setEngineHours] = useState<any>([])

    const [updateEngineHours] = useMutation(UPDATE_ENGINE, {
        onCompleted: (response) => {},
        onError: (error) => {
            console.error('Error updating engine hours', error)
        },
    })

    const [updateEngineStartStop] = useMutation(UPDATE_ENGINESTARTSTOP, {
        onCompleted: (response) => {
            queryGetEngines({
                variables: {
                    id: engineList.map((engine: any) => engine.id),
                    filter: {
                        logBookEntryID: { eq: +logentryID },
                    },
                },
            })
        },
        onError: (error) => {
            console.error('Error updating engine start stop', error)
        },
    })

    const handleupdateEngineHours = async (engineData: any) => {
        const engineStartStop = engines.find(
            (item: any) => item.id === engineData.id,
        )?.engineStartStops?.nodes
        if (engineStartStop?.length > 0) {
            const initialStartStop = engineStartStop[0]
            if (engineData.currentHours < initialStartStop.hoursStart) {
                updateEngineStartStop({
                    variables: {
                        input: {
                            id: initialStartStop.id,
                            hoursStart: engineData.currentHours,
                        },
                    },
                })
            } else if (engineData.currentHours > initialStartStop.hoursStart) {
                if (initialStartStop.hoursEnd < engineData.currentHours) {
                    engineStartStop.forEach((item: any) => {
                        if (item.hoursEnd < engineData.currentHours) {
                            updateEngineStartStop({
                                variables: {
                                    input: {
                                        id: item.id,
                                        hoursEnd: engineData.currentHours,
                                    },
                                },
                            })
                        }
                        if (item.hoursStart < engineData.currentHours) {
                            updateEngineStartStop({
                                variables: {
                                    input: {
                                        id: item.id,
                                        hoursStart: engineData.currentHours,
                                    },
                                },
                            })
                        }
                    })
                } else {
                    updateEngineStartStop({
                        variables: {
                            input: {
                                id: initialStartStop.id,
                                hoursStart: engineData.currentHours,
                            },
                        },
                    })
                }
            }
        } else {
            if (offline) {
                await engineModel.save(engineData)
            } else {
                await updateEngineHours({
                    variables: {
                        input: engineData,
                    },
                })
            }
        }
    }

    const [queryGetEngines] = useLazyQuery(GET_ENGINES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readEngines.nodes
            setEngines(data)
        },
        onError: (error: any) => {
            console.error('getEngines error', error)
        },
    })

    useEffect(() => {
        if (offline) {
            engineModel.getAll().then((data: any) => {
                if (data) {
                    setEngines(data)
                }
            })
        } else {
            queryGetEngines({
                variables: {
                    id: engineList.map((engine: any) => engine.id),
                    filter: {
                        logBookEntryID: { eq: +logentryID },
                    },
                },
            })
        }
    }, [engineList])

    const getInitialHours = (engine: any) => {
        if (engineHours.length > 0) {
            const engineHoursData = engineHours.find(
                (item: any) => item.id === engine.id,
            )
            if (engineHoursData) {
                return engineHoursData.currentHours
            }
        }
        const engineStartStop = engines.find(
            (item: any) => item.id === engine.id,
        )?.engineStartStops?.nodes
        if (engineStartStop?.length > 0) {
            const initialHours =
                engineStartStop[0].hoursStart ?? engine.currentHours
            return initialHours
        }
        return engine.currentHours
    }

    const [querySectionMemberComments] = useLazyQuery(
        GET_SECTION_MEMBER_COMMENTS,
        {
            fetchPolicy: 'cache-and-network',
            onCompleted: (response: any) => {
                const data = response.readSectionMemberComments.nodes
                if (data) {
                    setComments(data)
                }
            },
            onError: (error: any) => {
                console.error('querySectionMemberComments error', error)
            },
        },
    )

    const loadSectionMemberComments = async () => {
        if (offline) {
            const data = await commentModel.getByLogBookEntrySectionID(
                vesselDailyCheck.id,
            )
            if (data) {
                setComments(data)
            }
        } else {
            await querySectionMemberComments({
                variables: {
                    filter: {
                        logBookEntrySectionID: { eq: vesselDailyCheck.id },
                    },
                },
            })
        }
    }

    const saveSectionEngineComment = () => {
        getComment('DailyCheckEngine', 'Section')?.id > 0
            ? updateSectionMemberComment({
                  variables: {
                      input: {
                          id: getComment('DailyCheckEngine', 'Section').id,
                          comment: commentRef.current?.value,
                      },
                  },
              })
            : createSectionMemberComment({
                  variables: {
                      input: {
                          fieldName: 'DailyCheckEngine',
                          comment: commentRef.current?.value,
                          logBookEntrySectionID: vesselDailyCheck.id,
                          commentType: 'Section',
                      },
                  },
              })
    }

    const { run: debounceSaveSectionEngineComment } = useDebounceFn(() => {
        saveSectionEngineComment()
    }, 1000)

    return (
        <>
            <div className="pt-16">
                <H4>Engine hours</H4>
                <div className="my-4">
                    {engineList?.map((engine: any) => (
                        <Label
                            key={engine.id}
                            label={engine.title}
                            htmlFor={`engine-hours-${engine.id}`}>
                            <Input
                                id={`engine-hours-${engine.id}`}
                                type="number"
                                value={
                                    engineHours.find(
                                        (item: any) => item.id === engine.id,
                                    )?.currentHours ?? getInitialHours(engine)
                                }
                                name="start"
                                placeholder="Engine Hours"
                                className={''}
                                disabled={locked || !edit_logBookEntry}
                                onChange={(e: any) => {
                                    const engineData = {
                                        id: engine.id,
                                        currentHours: +e.target.value,
                                    }
                                    setEngineHours((prev: any) => [
                                        ...prev.filter(
                                            (item: any) =>
                                                item.id !== engine.id,
                                        ),
                                        engineData,
                                    ])
                                }}
                                onBlur={(e: any) => {
                                    const engineData = {
                                        id: engine.id,
                                        currentHours: +e.target.value,
                                    }
                                    handleupdateEngineHours(engineData)
                                }}
                            />
                        </Label>
                    ))}
                    <div className="my-4">
                        <Textarea
                            placeholder="Comment IF Engine hours are different to engine hours at end of previous logbook entry"
                            disabled={locked || !edit_logBookEntry}
                            rows={4}
                            className={`${''} mt-4`}
                            ref={commentRef}
                            onChange={(e) => {
                                setSectionEngineComment(e.target.value)
                                debounceSaveSectionEngineComment()
                            }}
                            // onBlur={(e) =>

                            // }
                            defaultValue={
                                getComment('DailyCheckEngine', 'Section')
                                    ?.comment
                            }>
                            {/* {getComment('Engine', 'Section')?.comment} */}
                        </Textarea>
                    </div>
                </div>
            </div>
        </>
    )
}
