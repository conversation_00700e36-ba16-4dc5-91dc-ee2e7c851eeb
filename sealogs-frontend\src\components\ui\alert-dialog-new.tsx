'use client'

import type * as React from 'react'
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogBody,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle,
} from '@/components/ui/alert-dialog'
import { Button } from '@/components/ui/button'
import {
    AlertCircle,
    AlertTriangle,
    ArrowLeft,
    CheckCircle,
    Info,
} from 'lucide-react'
import { cn } from '@/app/lib/utils'
import { Separator } from './separator'

type AlertVariant = 'info' | 'warning' | 'danger' | 'success' | 'default'
type AlertSize = 'sm' | 'md' | 'lg' | 'xl'
type AlertPosition = 'center' | 'side'

interface AlertDialogNewProps {
    openDialog: boolean
    setOpenDialog: (open: boolean) => void
    handleCreate?: () => void
    handleAction?: () => void
    handleCancel?: () => void
    handleDestructiveAction?: () => void
    children?: React.ReactNode
    title?: string
    description?: string
    actionText?: string
    secondaryActionText?: string | boolean
    cancelText?: string
    destructiveActionText?: string
    noButton?: boolean
    noFooter?: boolean
    className?: string
    contentClassName?: string
    variant?: AlertVariant
    size?: AlertSize
    position?: AlertPosition
    showIcon?: boolean
    loading?: boolean
    destructiveLoading?: boolean
    showDestructiveAction?: boolean
}

const variantStyles: Record<
    AlertVariant,
    {
        icon: React.ReactNode
        className: string
        headerClassName: string
        buttonVariant: string
        iconColor: string
    }
> = {
    default: {
        icon: <Info />,
        className: '',
        headerClassName: '',
        buttonVariant: 'primary',
        iconColor: '',
    },
    info: {
        icon: <Info />,
        className: 'border-blue-500 bg-blue-50',
        headerClassName: 'bg-blue-50',
        buttonVariant: 'default',
        iconColor: 'text-blue-500',
    },
    warning: {
        icon: <AlertTriangle />,
        className:
            'border-fire-bush-700 bg-fire-bush-100 text-fire-bush-700 p-5',
        headerClassName: '',
        buttonVariant: 'primary',
        iconColor: 'text-fire-bush-700',
    },
    danger: {
        icon: <AlertCircle />,
        className: 'border-destructive bg-red-vivid-50',
        headerClassName: 'bg-red-vivid-50',
        buttonVariant: 'destructive',
        iconColor: 'text-destructive',
    },
    success: {
        icon: <CheckCircle />,
        className: 'border-green-500 bg-green-50',
        headerClassName: 'bg-green-50',
        buttonVariant: 'success',
        iconColor: 'text-green-500',
    },
}

const sizeStyles: Record<AlertSize, string> = {
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
}

export function AlertDialogNew({
    openDialog,
    setOpenDialog,
    handleCreate,
    handleAction,
    handleCancel,
    handleDestructiveAction,
    children,
    title,
    description,
    actionText = 'Continue',
    secondaryActionText = false,
    cancelText = 'Cancel',
    destructiveActionText = 'Delete',
    noButton = false,
    noFooter = false,
    className,
    contentClassName,
    variant = 'default',
    size = 'md',
    position = 'center',
    showIcon = false,
    loading = false,
    destructiveLoading = false,
    showDestructiveAction = false,
}: AlertDialogNewProps) {
    const onCancel = () => {
        handleCancel?.()
        setOpenDialog(false)
    }

    const {
        icon,
        buttonVariant,
        className: variantClassName,
        headerClassName,
        iconColor,
    } = variantStyles[variant]

    return (
        <AlertDialog open={openDialog} onOpenChange={setOpenDialog}>
            <AlertDialogContent
                className={cn(
                    sizeStyles[size],
                    position === 'side' &&
                        'sm:ml-auto sm:mr-0 sm:rounded-l-xl sm:rounded-r-none sm:h-full',
                    position === 'center' && 'sm:rounded-xl',
                    contentClassName,
                )}
                innerClassName={cn(variantClassName)}>
                <AlertDialogHeader
                    className={cn(headerClassName, {
                        'sr-only': !title && !description,
                    })}>
                    <div
                        className={cn(
                            'flex-shrink-0 flex items-center gap-2.5',
                            iconColor,
                        )}>
                        {showIcon && icon}
                        <AlertDialogTitle>{title}</AlertDialogTitle>
                    </div>

                    <AlertDialogDescription hidden={!description}>
                        {description}
                    </AlertDialogDescription>
                </AlertDialogHeader>

                <AlertDialogBody className={cn(className)}>
                    {children}
                </AlertDialogBody>

                {!noFooter && (
                    <AlertDialogFooter
                        className={cn(
                            'w-full flex flex-row flex-wrap-reverse 2xs:flex-nowrap justify-end gap-2.5 sm:gap-2',
                            {
                                'flex-nowrap': !showDestructiveAction,
                            },
                        )}>
                        <Button
                            variant="back"
                            className={cn(
                                showDestructiveAction
                                    ? 'w-full 2xs:w-fit 2xs:px-3 sm:px-5'
                                    : 'w-full sm:w-fit px-5',
                            )}
                            iconLeft={<ArrowLeft />}
                            onClick={onCancel}>
                            {!noButton ? cancelText : 'Cancel'}
                        </Button>

                        {showDestructiveAction && (
                            <Separator className="my-2 2xs:hidden" />
                        )}
                        <div
                            className={cn(
                                'flex',
                                showDestructiveAction
                                    ? 'gap-2.5 w-full sm:gap-5'
                                    : 'w-full 2xs:max-w-[200px] sm:w-auto',
                            )}>
                            {showDestructiveAction &&
                                handleDestructiveAction &&
                                !noButton && (
                                    <Button
                                        variant={
                                            variant === 'warning'
                                                ? 'warning'
                                                : 'destructive'
                                        }
                                        className="w-full 2xs:px-3 sm:px-5"
                                        onClick={handleDestructiveAction}
                                        isLoading={destructiveLoading}>
                                        {destructiveActionText}
                                    </Button>
                                )}
                            {!noButton && handleCreate && (
                                <Button
                                    variant={buttonVariant as any}
                                    onClick={handleCreate}
                                    className={cn(
                                        showDestructiveAction
                                            ? 'w-full 2xs:px-3 sm:px-5 '
                                            : 'px-5 w-full',
                                    )}
                                    isLoading={loading}>
                                    {actionText}
                                </Button>
                            )}
                        </div>
                        {secondaryActionText && handleAction && (
                            <Button
                                variant={buttonVariant as any}
                                className={cn(
                                    showDestructiveAction
                                        ? 'w-full 2xs:px-3 sm:px-5'
                                        : 'px-5 w-full',
                                )}
                                onClick={handleAction}>
                                {secondaryActionText}
                            </Button>
                        )}
                    </AlertDialogFooter>
                )}
            </AlertDialogContent>
        </AlertDialog>
    )
}
