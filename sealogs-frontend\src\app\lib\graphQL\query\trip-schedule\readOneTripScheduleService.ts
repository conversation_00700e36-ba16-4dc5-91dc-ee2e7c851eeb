import gql from 'graphql-tag'

export const ReadOneTripScheduleService = gql`
    query ReadOneTripScheduleService($id: ID!) {
        readOneTripScheduleService(filter: { id: { eq: $id } }) {
            id
            title
            code
            sharePaxData
            transitID
            publicDescription
            tripReportSchedules {
                nodes {
                    id
                    title
                    departTime
                }
            }
            vehicles {
                nodes {
                    id
                    title
                }
            }
        }
    }
`
