'use client'
import React, { useState, useRef } from 'react'
import Link from 'next/link'
import { SealogsCrewIcon } from '../../../lib/icons/SealogsCrewIcon'
import { DataTable, ExtendedColumnDef } from '@/components/filteredTable'
import { H1 } from '@/components/ui/typography'
import { useIsMobile } from '@/components/hooks/use-mobile'
import { Button } from '@/components/ui/button'
import { Check, CircleAlert, Users } from 'lucide-react'
import {
    Avatar,
    AvatarFallback,
    Badge,
    Card,
    getCrewInitials,
    Tooltip,
    TooltipContent,
    TooltipTrigger,
} from '@/components/ui'

import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from '@/components/ui/popover'

interface CrewMember {
    id: number
    firstName: string
    surname: string
    trainingStatus?: {
        label: string
        value: string
        dues?: Array<{
            trainingType: { title: string }
            status: { label: string }
        }>
    }
    primaryDuty?: {
        title: string
        id: number
    }
    vehicles?: {
        nodes: Array<{
            id: number
            title: string
        }>
    }
}

export default function CrewCard({
    crewInfo,
    setVesselTab,
    vesselId,
    pathname,
}: {
    crewInfo: CrewMember[]
    setVesselTab: (tab: string) => void
    vesselId: number
    pathname: string
}) {
    const isMobile = useIsMobile()
    const initialPageSize = 5 // Initial number of crew to show
    const incrementPageSize = 20 // Number of crew to add when "View More" is clicked
    const [visiblePages, setVisiblePages] = useState(initialPageSize)
    const [isExpanded, setIsExpanded] = useState(false)
    const cardRef = useRef<HTMLDivElement>(null)

    // Calculate how many crew to show based on visible pages
    const entriesToShow = visiblePages
    const crewToDisplay = isMobile
        ? crewInfo?.slice(0, entriesToShow) || []
        : crewInfo?.slice(0, 5) || []
    const hasMoreCrew = crewInfo && crewInfo.length > entriesToShow
    const canShowMore = crewInfo && crewInfo.length > 5

    const columns = [
        {
            accessorKey: 'title',
            header: '',
            cell: ({ row }: { row: any }) => {
                const crew = row.original
                return (
                    <div className="flex gap-2.5 items-center">
                        <Avatar
                            size="sm"
                            variant={
                                crew.trainingStatus?.label &&
                                crew.trainingStatus.label !== 'Good'
                                    ? 'destructive'
                                    : 'success'
                            }>
                            <AvatarFallback>
                                {getCrewInitials(crew.firstName, crew.surname)}
                            </AvatarFallback>
                        </Avatar>
                        <div className="grid min-w-32">
                            <Link
                                href={`/crew/info?id=${crew.id}`}
                                className="items-center truncate pl-2 text-nowrap">
                                {crew.firstName || '--'}
                                <span className="hidden md:flex">
                                    &nbsp;
                                    {crew.surname || '--'}
                                </span>
                            </Link>
                            <div className="text-sm text-muted-foreground pl-2">
                                {crew.primaryDuty?.title || 'No duty assigned'}
                            </div>
                        </div>
                    </div>
                )
            },
        },
        {
            accessorKey: 'trainingStatus',
            header: '',
            cellAlignment: 'right',
            cell: ({ row }: { row: any }) => {
                const crew = row.original

                return (
                    <>
                        {crew.trainingStatus?.label &&
                        crew.trainingStatus.label !== 'Good' ? (
                            <Popover triggerType="hover">
                                <PopoverTrigger asChild>
                                    <CircleAlert
                                        strokeWidth={1}
                                        className="h-9 w-9 text-destructive cursor-pointer"
                                    />
                                </PopoverTrigger>
                                <PopoverContent>
                                    <div className="bg-background rounded p-2">
                                        <div className="text-xs whitespace-nowrap font-medium focus:outline-none inline-block rounded">
                                            {crew.trainingStatus?.dues?.map(
                                                (
                                                    item: any,
                                                    dueIndex: number,
                                                ) => (
                                                    <div key={dueIndex}>
                                                        {`${item.trainingType?.title || 'Unknown'} - ${item.status?.label || 'Unknown'}`}
                                                    </div>
                                                ),
                                            )}
                                        </div>
                                    </div>
                                </PopoverContent>
                            </Popover>
                        ) : (
                            <Tooltip>
                                <TooltipTrigger>
                                    <Badge variant="success" type="circle">
                                        <Check className="h-5 w-5" />
                                    </Badge>
                                </TooltipTrigger>
                                <TooltipContent>
                                    <p>Training is up to date</p>
                                </TooltipContent>
                            </Tooltip>
                        )}
                    </>
                )
            },
        },
    ]

    return (
        <Card ref={cardRef}>
            <div className="lg:flex flex-col lg:justify-between">
                <div className="flex py-3 items-baseline gap-2 phablet:gap-4">
                    <div className="h-12 w-12 ring-1 p-1 rounded-full">
                        <SealogsCrewIcon className="h-full w-full" />
                    </div>
                    <Link onClick={() => setVesselTab('crew')} href={''}>
                        <H1>Crew</H1>
                    </Link>
                </div>

                {crewInfo && crewInfo.length > 0 ? (
                    <>
                        <div className="pt-0 phablet:pt-1">
                            <DataTable
                                columns={
                                    columns as ExtendedColumnDef<any, unknown>[]
                                }
                                data={crewToDisplay}
                                showToolbar={false}
                                pageSize={
                                    isMobile
                                        ? crewToDisplay.length
                                        : initialPageSize
                                }
                                className="border-0 shadow-none"
                            />
                        </div>
                        {isMobile && canShowMore && (
                            <div className="mt-4 items-center rounded-lg gap-4 xs:gap-0 bg-accent border border-curious-blue-100 p-5 text-center">
                                <Button
                                    variant="outline"
                                    className="text-accent-foreground uppercase hover:text-primary text-xs bg-transparent border-none shadow-none p-0 h-auto"
                                    onClick={() => {
                                        if (hasMoreCrew) {
                                            setVisiblePages(
                                                visiblePages +
                                                    incrementPageSize,
                                            )
                                            setIsExpanded(true)
                                        } else {
                                            setVisiblePages(initialPageSize)
                                            setIsExpanded(false)
                                            cardRef.current?.scrollIntoView({
                                                behavior: 'smooth',
                                                block: 'start',
                                            })
                                        }
                                    }}>
                                    {hasMoreCrew ? 'View More' : 'View Less'}
                                </Button>
                            </div>
                        )}
                    </>
                ) : (
                    <div className="flex justify-between items-center gap-2 p-2 pt-4">
                        <div>
                            <Users className="h-12 w-12 text-muted-foreground" />
                        </div>
                        <div className="text-center">
                            <p className="text-muted-foreground">
                                No crew assigned
                            </p>
                        </div>
                    </div>
                )}
            </div>
        </Card>
    )
}
