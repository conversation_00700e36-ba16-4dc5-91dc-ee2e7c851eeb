import { Label } from '@/components/ui/label'
import { TableWrapper } from '@/app/ui/daily-checks/Components'
import { H3 } from './ui'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Skeleton } from '@/components/ui/skeleton'

const shimmer =
    'before:absolute before:inset-0 before:-translate-x-full before:animate-[shimmer_2s_infinite] before:bg-gradient-to-r before:from-transparent before:via-white/60 before:to-transparent'

export function CrewListTable() {
    return (
        <div className={`${shimmer}mb-2 w-full rounded-md  p-4`}>
            <div className="flex items-center justify-between border-b pb-8">
                <div className="flex items-center">
                    <div className="mr-2 h-8 w-8 rounded-full "></div>
                    <div className="h-6 w-16 rounded "></div>
                </div>
                <div className="h-6 w-16 rounded "></div>
            </div>
            <div className="flex w-full items-center justify-between pt-4">
                <div>
                    <div className="h-6 w-16 rounded "></div>
                    <div className="mt-2 h-6 w-24 rounded "></div>
                </div>
                <div className="flex justify-end gap-2">
                    <div className="h-10 w-10 rounded "></div>
                    <div className="h-10 w-10 rounded "></div>
                </div>
            </div>
        </div>
    )
}

export function LogBookEntrySkeleton() {
    return (
        <div className={`${shimmer} w-full p-0  border  rounded-lg shadow`}>
            <div className="   shadow flex justify-between">
                <div className="  p-3">
                    <div className="h-8 w-64 rounded "></div>
                </div>
            </div>
            <div className="p-4">
                <LogDateSkeleton />
                <div className="mt-5 flex justify-start flex-col md:flex-row items-center">
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                    <div className={`h-10 w-32 rounded  mr-2`}></div>
                </div>
                <hr className="my-4" />
                <div className=" mb-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                    <div className={`h-10 w-48 rounded  mr-2`}></div>
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        <div className="flex md:items-center flex-col md:flex-row gap-2">
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                        </div>
                    </div>
                </div>
                <div className=" mb-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                    <div className={`h-10 w-48 rounded  mr-2`}></div>
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        <div className="flex md:items-center flex-col md:flex-row gap-2">
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                        </div>
                    </div>
                </div>
                <div className=" mb-4 grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 items-center  ">
                    <div className={`h-10 w-48 rounded  mr-2`}></div>
                    <div className="flex flex-col grid-cols-1 md:col-span-2 lg:col-span-3">
                        <div className="flex md:items-center flex-col md:flex-row gap-2">
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                            <div className={`h-10 w-48 rounded  mr-2`}></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}

export function LogDateSkeleton() {
    return (
        <div className="mt-2 flex justify-start flex-col md:flex-row items-center">
            <div className={`h-8 w-48 rounded  mr-2`}></div>
            <div className={`mr-2 h-8 w-8 rounded-full `}></div>
            <div className={`h-8 w-48 rounded `}></div>
        </div>
    )
}

export function CrewDutyListSkeleton() {
    return (
        <>
            {Array.from({ length: 3 }, (_, i) => (
                <tr key={i}>
                    <td className="pl-6">
                        <Skeleton />
                    </td>
                    <td>
                        <Skeleton />
                    </td>
                    <td>
                        <Skeleton />
                    </td>
                </tr>
            ))}
        </>
    )
}

export function TrainingSessionListSkeleton({
    memberId = 0 as number,
    vesselId = 0 as number,
}) {
    return (
        <div className="w-full p-0 shadow ">
            <div className="relative overflow-x-auto shadow-md">
                {memberId === 0 && vesselId === 0 && (
                    <>
                        <div className=" flex items-center justify-between flex-column flex-wrap md:flex-row space-y-4 md:space-y-0 ">
                            <H3 className="p-4">Crew Training List</H3>
                        </div>
                        <div className="p-5 flex justify-between items-center   text-left rtl:text-right  ">
                            <p className="mt-1 mx-4    ">&nbsp;</p>
                        </div>
                    </>
                )}
                <TableWrapper
                    headings={['Date', 'Type of Training', 'Trainer']}>
                    <tr className=" border-b   ">
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-2 py-2">
                            <div className="flex justify-end flex-col md:flex-row">
                                <Skeleton />
                            </div>
                        </td>
                    </tr>
                    <tr className=" border-b   ">
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-2 py-2">
                            <div className="flex justify-end flex-col md:flex-row">
                                <Skeleton />
                            </div>
                        </td>
                    </tr>
                    <tr className=" border-b   ">
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-6 py-4">
                            <Skeleton />
                        </td>
                        <td className="px-2 py-2">
                            <div className="flex justify-end flex-col md:flex-row">
                                <Skeleton />
                            </div>
                        </td>
                    </tr>
                </TableWrapper>
            </div>
        </div>
    )
}

export function TrainingSessionInfoSkeleton() {
    return (
        <div className="space-y-8">
            {/* Header Section */}
            <Card>
                <CardContent className="p-4">
                    <div className="flex justify-between items-center">
                        <div className="space-y-2">
                            <Skeleton className="h-4 w-24" />
                            <Skeleton className="h-6 w-64" />
                        </div>
                        <Skeleton className="h-8 w-24" />
                    </div>
                </CardContent>
            </Card>

            {/* Training Details Section */}
            <Card>
                <CardHeader className="pb-3">
                    <CardTitle className="text-white">
                        <Skeleton className="h-5 w-32" />
                    </CardTitle>
                    <Skeleton className="h-4 w-80" />
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div className="space-y-2">
                            <Skeleton className="h-4 w-16" />
                            <Skeleton className="h-4 w-28" />
                        </div>
                        <div className="space-y-2">
                            <Skeleton className="h-4 w-32" />
                            <Skeleton className="h-4 w-96" />
                        </div>
                    </div>
                </CardContent>
            </Card>

            {/* Members Section */}
            <Card>
                <CardHeader className="pb-3">
                    <CardTitle className="text-white">
                        <Skeleton className="h-5 w-20" />
                    </CardTitle>
                    <Skeleton className="h-4 w-72" />
                </CardHeader>
                <CardContent>
                    <div className="flex flex-wrap gap-3">
                        {[1, 2, 3, 4, 5].map((i) => (
                            <Skeleton
                                key={i}
                                className="h-8 w-24 rounded-full"
                            />
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Summary Section */}
            <Card>
                <CardHeader className="pb-3">
                    <CardTitle className="text-white">
                        <Skeleton className="h-5 w-20" />
                    </CardTitle>
                    <Skeleton className="h-4 w-80" />
                </CardHeader>
                <CardContent>
                    <Card>
                        <CardContent className="p-4 space-y-2">
                            <Skeleton className="h-3 w-full" />
                            <Skeleton className="h-3 w-3/4" />
                            <Skeleton className="h-3 w-1/2" />
                        </CardContent>
                    </Card>
                </CardContent>
            </Card>

            {/* Signatures Section */}
            <Card>
                <CardHeader className="pb-3">
                    <CardTitle className="text-white">
                        <Skeleton className="h-5 w-24" />
                    </CardTitle>
                    <Skeleton className="h-4 w-96" />
                </CardHeader>
                <CardContent>
                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {[
                            'Long John Dong',
                            'Chef Cutlass',
                            'Pirate Pete',
                            'Daniela Crew',
                            'One-eyed Snake',
                        ].map((name, i) => (
                            <Card key={i}>
                                <CardHeader className="pb-2">
                                    <Skeleton className="h-4 w-20" />
                                </CardHeader>
                                <CardContent className="flex items-center justify-center h-20">
                                    <Skeleton className="h-3 w-32" />
                                </CardContent>
                            </Card>
                        ))}
                    </div>
                </CardContent>
            </Card>

            {/* Footer */}
            <div className="flex justify-between text-sm pt-4">
                <Skeleton className="h-3 w-32" />
                <Skeleton className="h-3 w-40" />
            </div>
        </div>
    )
}

export function TrainingSessionFormSkeleton() {
    return (
        <div className="px-0 md:px-4 pt-4 border-t">
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4 ">Training Details</div>
                <div className="col-span-2">
                    <div className="flex w-full gap-4">
                        <div className="w-full">
                            <div className="w-full my-4 flex flex-col">
                                <Label className="mb-1 ">Trainer</Label>
                                <InputSkeleton />
                            </div>
                            <div className="w-full mt-4 flex flex-col">
                                <InputSkeleton />
                            </div>
                        </div>
                        <div className="w-full mt-4 flex flex-col">
                            <Label className="mb-1 ">Crew</Label>
                            <InputSkeleton />
                        </div>
                    </div>
                    <div className="flex w-full gap-4 mt-4">
                        <div className="w-full">
                            <InputSkeleton />
                        </div>
                        <div className="w-full">
                            <InputSkeleton />
                        </div>
                    </div>
                    <div className="w-full my-4 flex flex-col">
                        <InputSkeleton />
                    </div>
                </div>
            </div>
            <hr className="my-2" />
            <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="my-4 ">Signatures</div>
                <div className="col-span-2 my-4 flex justify-between flex-wrap gap-4">
                    <InputSkeleton />
                </div>
            </div>
            <hr className="mb-4" />
            <div className="flex justify-end px-4 pb-4 pt-4">
                <div className="w-48 mr-4">
                    <InputSkeleton />
                </div>
                <div className="w-48 ">
                    <InputSkeleton />
                </div>
            </div>
        </div>
    )
}

export function TrainingTypeListSkeleton() {
    return (
        <div className="w-full p-0  border  rounded-lg shadow ">
            <div className="relative overflow-x-auto shadow-md sm:rounded-lg">
                <div className="flex items-center justify-between flex-column flex-wrap md:flex-row p-4 md:space-y-0 ">
                    <H3>Training Types</H3>
                </div>
                <div className="p-5 flex justify-between items-center   text-left rtl:text-right     ">
                    &nbsp;
                </div>
                <div className="relative overflow-x-auto">
                    <TableWrapper
                        headings={[
                            'Nature of Training',
                            'Vessels',
                            'Occurs Every (days)',
                            'Medium Warning Within (days)',
                            'High Warning Within (days)',
                            '',
                        ]}>
                        <tr className=" border-b   ">
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-6 py-4">
                                <Skeleton />
                            </td>
                            <td className="px-2 py-2">
                                <div className="flex justify-end flex-col md:flex-row">
                                    <Skeleton />
                                </div>
                            </td>
                        </tr>
                    </TableWrapper>
                </div>
            </div>
        </div>
    )
}

export function TrainingTypeInfoSkeleton() {
    return (
        <>
            <div className="flex justify-between pb-4 pt-3 items-center">
                <H3>Training Types</H3>
                <div className="flex">
                    <div className="w-48 ">
                        <InputSkeleton />
                    </div>
                    <div className="w-48 ">
                        <InputSkeleton />
                    </div>
                </div>
            </div>
            <div className="px-0 md:px-4 py-4 border-t border-b">
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Nature Of Training</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Occurs Every</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Medium Warning Within</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>High Warning Within</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Procedure</div>
                    <Skeleton />
                </div>
                <div className="group hover: w-full grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div>Vessels</div>
                    <Skeleton />
                </div>
            </div>
        </>
    )
}

export function InputSkeleton() {
    return (
        <div className="relative overflow-hidden w-full">
            <Skeleton className={`h-11 w-full`}></Skeleton>
        </div>
    )
}

export function TableSkeleton() {
    return (
        <div className="space-y-4 pb-8">
            <div className="p-0 phablet:p-8 lg:p-6 xl:p-8 shadow-none border-0 phablet:border border-border bg-card rounded-lg relative w-full overflow-auto">
                {/* Desktop Table View */}
                <div className="hidden md:block">
                    <table
                        cellSpacing={0}
                        className="w-full caption-bottom border-spacing-0">
                        <thead className="[&_tr]:border-border">
                            <tr className="relative cursor-pointer border-border group">
                                <th className="h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative text-xs tiny:text-xs small:text-sm text-neutral-400 font-normal items-left justify-items-start text-left">
                                    <Skeleton className="h-4 w-12" />
                                </th>
                                <th className="h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative text-xs tiny:text-xs small:text-sm text-neutral-400 font-normal text-center">
                                    <Skeleton className="h-4 w-20 mx-auto" />
                                </th>
                                <th className="h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative text-xs tiny:text-xs small:text-sm text-neutral-400 font-normal text-center">
                                    <Skeleton className="h-4 w-8 mx-auto" />
                                </th>
                                <th className="h-8 px-0 small:px-1 phablet:px-[7px] pb-2 small:p-auto cursor-default relative text-xs tiny:text-xs small:text-sm text-neutral-400 font-normal text-right">
                                    <Skeleton className="h-4 w-12 ml-auto" />
                                </th>
                            </tr>
                        </thead>
                        <tbody className="[&_tr:last-child]:border-0">
                            {Array.from({ length: 20 }, (_, i) => (
                                <tr
                                    key={i}
                                    className="relative cursor-pointer border-border group">
                                    {/* Title column - left aligned */}
                                    <td className="h-20 font-normal align-center text-card-foreground tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5 items-left justify-items-start text-left">
                                        <span className="absolute flex w-full rounded-md pointer-events-none overflow-hidden inset-0">
                                            <span className="bg-accent w-0 group-hover:w-full transition-[width] ease-out duration-300 will-change-transform will-change-width hidden first:block" />
                                        </span>
                                        <span className="relative w-fit z-10">
                                            <div className="flex flex-1 items-left justify-items-start text-left">
                                                <div className="flex flex-col space-y-2">
                                                    <Skeleton className="h-4 w-32" />
                                                    <div className="flex items-center space-x-2">
                                                        <Skeleton className="h-6 w-6 rounded-full" />
                                                        <Skeleton className="h-3 w-24" />
                                                    </div>
                                                </div>
                                            </div>
                                        </span>
                                    </td>

                                    {/* Assigned To column - center aligned */}
                                    <td className="h-20 font-normal align-center text-card-foreground tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5 text-center">
                                        <span className="relative w-fit z-10">
                                            <div className="flex flex-1 justify-center">
                                                <div className="flex items-center space-x-2">
                                                    <Skeleton className="h-8 w-8 rounded-full" />
                                                    <Skeleton className="h-4 w-20" />
                                                </div>
                                            </div>
                                        </span>
                                    </td>

                                    {/* Due column - center aligned */}
                                    <td className="h-20 font-normal align-center text-card-foreground tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5 text-center">
                                        <span className="relative w-fit z-10">
                                            <div className="flex flex-1 justify-center">
                                                <Skeleton className="h-4 w-16" />
                                            </div>
                                        </span>
                                    </td>

                                    {/* Status column - right aligned */}
                                    <td className="h-20 font-normal align-center text-card-foreground tiny:first:pl-1 tiny:last:pr-1 small:first:pl-1.5 small:last:pr-1.5 phablet:first:pl-2.5 phablet:last:pr-2.5 text-right">
                                        <span className="relative w-fit z-10">
                                            <div className="flex flex-1 justify-end">
                                                <Skeleton className="h-6 w-16 rounded-full" />
                                            </div>
                                        </span>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Tablet View - Hide Due column */}
                <div className="hidden sm:block md:hidden">
                    <table
                        cellSpacing={0}
                        className="w-full caption-bottom border-spacing-0">
                        <thead className="[&_tr]:border-border">
                            <tr className="relative cursor-pointer border-border group">
                                <th className="h-8 px-2 pb-2 cursor-default relative text-sm text-neutral-400 font-normal text-left">
                                    <Skeleton className="h-4 w-12" />
                                </th>
                                <th className="h-8 px-2 pb-2 cursor-default relative text-sm text-neutral-400 font-normal text-center">
                                    <Skeleton className="h-4 w-20 mx-auto" />
                                </th>
                                <th className="h-8 px-2 pb-2 cursor-default relative text-sm text-neutral-400 font-normal text-right">
                                    <Skeleton className="h-4 w-12 ml-auto" />
                                </th>
                            </tr>
                        </thead>
                        <tbody className="[&_tr:last-child]:border-0">
                            {Array.from({ length: 20 }, (_, i) => (
                                <tr
                                    key={i}
                                    className="relative cursor-pointer border-border group">
                                    <td className="h-16 font-normal align-center text-card-foreground px-2 text-left">
                                        <span className="absolute flex w-full rounded-md pointer-events-none overflow-hidden inset-0">
                                            <span className="bg-accent w-0 group-hover:w-full transition-[width] ease-out duration-300 will-change-transform will-change-width hidden first:block" />
                                        </span>
                                        <span className="relative w-fit z-10">
                                            <div className="flex flex-col space-y-2">
                                                <Skeleton className="h-4 w-28" />
                                                <div className="flex items-center space-x-2">
                                                    <Skeleton className="h-5 w-5 rounded-full" />
                                                    <Skeleton className="h-3 w-20" />
                                                </div>
                                            </div>
                                        </span>
                                    </td>
                                    <td className="h-16 font-normal align-center text-card-foreground px-2 text-center">
                                        <span className="relative w-fit z-10">
                                            <div className="flex justify-center">
                                                <div className="flex items-center space-x-2">
                                                    <Skeleton className="h-7 w-7 rounded-full" />
                                                    <Skeleton className="h-4 w-16" />
                                                </div>
                                            </div>
                                        </span>
                                    </td>
                                    <td className="h-16 font-normal align-center text-card-foreground px-2 text-right">
                                        <span className="relative w-fit z-10">
                                            <div className="flex justify-end">
                                                <Skeleton className="h-6 w-14 rounded-full" />
                                            </div>
                                        </span>
                                    </td>
                                </tr>
                            ))}
                        </tbody>
                    </table>
                </div>

                {/* Mobile Card View */}
                <div className="block sm:hidden space-y-3">
                    {Array.from({ length: 20 }, (_, i) => (
                        <div
                            key={i}
                            className="bg-card border border-border rounded-lg p-4 space-y-3">
                            {/* Title and avatar */}
                            <div className="flex items-start space-x-3">
                                <Skeleton className="h-8 w-8 rounded-full flex-shrink-0" />
                                <div className="flex-1 space-y-2">
                                    <Skeleton className="h-4 w-full max-w-[200px]" />
                                    <Skeleton className="h-3 w-3/4 max-w-[150px]" />
                                </div>
                            </div>

                            {/* Status and assigned info */}
                            <div className="flex items-center justify-between pt-2">
                                <div className="flex items-center space-x-2">
                                    <Skeleton className="h-6 w-6 rounded-full" />
                                    <Skeleton className="h-3 w-16" />
                                </div>
                                <Skeleton className="h-5 w-12 rounded-full" />
                            </div>

                            {/* Due date */}
                            <div className="pt-1">
                                <Skeleton className="h-3 w-20" />
                            </div>
                        </div>
                    ))}
                </div>
            </div>
        </div>
    )
}

export function List(props?: any) {
    return (
        <TableWrapper
            headings={
                props.heading
                    ? [props.heading + ':firstHead', '', ':last']
                    : [':firstHead', '', ':last']
            }>
            <TR />
            <TR />
            <TR />
            <TR />
            <TR />
            <TR />
        </TableWrapper>
    )
}

export function TR() {
    return (
        <tr className={`border-b  hover: `}>
            <td scope="col" className="px-2 py-6 relative">
                <Skeleton />
            </td>
            <td scope="col" className="px-2 py-6 relative">
                <Skeleton />
            </td>
            <td scope="col" className="px-2 py-6 relative">
                <Skeleton />
            </td>
        </tr>
    )
}

export function DepartmentInfoSkeleton() {
    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 pt-3 items-center">
                <H3>Department:</H3>
                <Skeleton />
            </div>
            <div className="px-0 md:px-4 pt-4 border-t border-b ">
                <div className="grid grid-cols-3 gap-6 py-4 px-4">
                    <div>
                        <Skeleton />
                    </div>
                    <div className="col-span-2">
                        <Skeleton />
                    </div>
                </div>
            </div>
        </div>
    )
}

export function DepartmentFormSkeleton() {
    return (
        <div className="w-full p-0">
            <div className="flex justify-between pb-4 pt-3">
                <H3>Department</H3>
            </div>

            <div className="px-0 md:px-4 pt-4 border-t  ">
                <div className="grid grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div className="my-4 ">
                        Department Details
                        <p className=" mt-4 max-w-[25rem] leading-loose">
                            Lorem ipsum dolor sit amet consectetur adipisicing
                            elit. Facilis possimus harum eaque itaque est id
                            reprehenderit excepturi eius temporibus, illo
                            officia amet nobis sapiente dolorem ipsa earum
                            adipisci recusandae cumque.
                        </p>
                    </div>
                    <div className="col-span-2">
                        <div className="flex w-full gap-4">
                            <div className="w-full">
                                <div className="w-full my-4 flex flex-col">
                                    <Label className="mb-1 ">Name</Label>
                                    <InputSkeleton />
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <hr className="mb-4" />
            </div>
        </div>
    )
}

export function DepartmentListSkeleton() {
    return (
        <TableWrapper headings={['Departments:firstHead']}>
            <tr className={`group border-b hover: `}>
                <th scope="row" className="flex items-center px-2 py-3 lg:px-6">
                    <Skeleton />
                </th>
            </tr>
            <tr className={`group border-b hover: `}>
                <th scope="row" className="flex items-center px-2 py-3 lg:px-6">
                    <Skeleton />
                </th>
            </tr>
            <tr className={`group border-b hover: `}>
                <th scope="row" className="flex items-center px-2 py-3 lg:px-6">
                    <Skeleton />
                </th>
            </tr>
        </TableWrapper>
    )
}

export function GeoLocationListSkeleton() {
    return (
        <TableWrapper
            headings={['Location:firstHead', 'Latitude', 'Longitude']}>
            <tr className={`group border-b  hover: `}>
                <td className="pl-6">
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
            </tr>
            <tr className={`group border-b  hover: `}>
                <td className="pl-6">
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
            </tr>
            <tr className={`group border-b  hover: `}>
                <td className="pl-6">
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
                <td>
                    <Skeleton />
                </td>
            </tr>
        </TableWrapper>
    )
}
