'use client'

import * as React from 'react'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'
import { useSidebar } from '@/components/ui/sidebar'
import Link from 'next/link'
import { useEffect, useState } from 'react'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { SealogsCogIcon } from '@/app/lib/icons'

export const TrainingTypeFilterActions = () => {
    const { isMobile } = useSidebar()

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <SealogsCogIcon size={36} />
            </DropdownMenuTrigger>
            <DropdownMenuContent
                side={isMobile ? 'bottom' : 'right'}
                align={isMobile ? 'end' : 'start'}>
                <Link href={'/training-type/create'}>
                    <DropdownMenuItem>Add A Training Type</DropdownMenuItem>
                </Link>
                <Link href={'/crew-training'}>
                    <DropdownMenuItem>Crew Trainings</DropdownMenuItem>
                </Link>
            </DropdownMenuContent>
        </DropdownMenu>
    )
}
