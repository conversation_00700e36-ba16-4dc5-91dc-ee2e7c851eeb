'use client'

import { getSwellHeightRangeFromFill } from '@/app/helpers/weatherHelper'
import React, { useEffect, useRef, useState } from 'react'

interface IProps {
    fillAmount: number
    onChange: (swell: string) => void
}

const SeaSwellSlider = ({ fillAmount, onChange }: IProps) => {
    const [value, setValue] = useState(fillAmount)
    const svgRef = useRef<SVGSVGElement | null>(null)

    useEffect(() => {
        setValue(fillAmount)
    }, [fillAmount])

    const viewBoxWidth = 126 // match this to your cloud's logical width
    const viewBoxHeight = 50

    const path =
        'M.471,48.438a2.685,2.685,0,0,1,2.684-2.684c6.442,0,14.333-5.368,14.333-11.649a2.684,2.684,0,0,1,5.368,0,11.649,11.649,0,1,0,23.3,0v0a2.684,2.684,0,0,1,5.368,0c0,5.932,10.173,11.7,16.1,11.7a7.7,7.7,0,0,0,6.737-2.979C79.519,35.5,74.687,25.3,69,21.221c-1.449-1.02-4.08-2.469-5.368-.591a3.058,3.058,0,0,1-3.758,1.02c-2.684-1.208-3.785-6.335-2.979-10.173S61.32.5,75.519.5s27.673,8.052,38.3,22.976a94.4,94.4,0,0,1,11.649,22.547'

    const handleDrag = (clientX: number) => {
        if (!svgRef.current) return
        const bounds = svgRef.current.getBoundingClientRect()
        const mouseX = clientX - bounds.left
        const percent = Math.max(
            0,
            Math.min(100, (mouseX / bounds.width) * 100),
        )

        const percentageFill = Number(percent.toFixed(2))
        const swell = getSwellHeightRangeFromFill(percentageFill)

        onChange(swell)
        setValue(percentageFill)
    }

    const handleMouseDown = (e: React.MouseEvent<SVGSVGElement>) => {
        if (!svgRef.current) return

        handleDrag(e.clientX)

        const moveHandler = (e: MouseEvent) => handleDrag(e.clientX)
        const upHandler = () => {
            document.removeEventListener('mousemove', moveHandler)
            document.removeEventListener('mouseup', upHandler)
        }

        document.addEventListener('mousemove', moveHandler)
        document.addEventListener('mouseup', upHandler)
    }

    const handleTouchStart = (e: React.TouchEvent<SVGSVGElement>) => {
        if (e.touches.length > 0) {
            handleDrag(e.touches[0].clientX)

            const moveHandler = (e: TouchEvent) => {
                if (e.touches.length > 0) handleDrag(e.touches[0].clientX)
            }

            const endHandler = () => {
                document.removeEventListener('touchmove', moveHandler)
                document.removeEventListener('touchend', endHandler)
            }

            document.addEventListener('touchmove', moveHandler)
            document.addEventListener('touchend', endHandler)
        }
    }

    const fillWidth = (value / 100) * viewBoxWidth

    const thumbX = fillWidth

    return (
        <div className="max-w-sm mx-auto">
            <svg
                ref={svgRef}
                viewBox={`0 0 ${viewBoxWidth} ${viewBoxHeight}`}
                width="100%"
                height="auto"
                className="w-full max-w-full h-auto cursor-pointer select-none"
                preserveAspectRatio="xMidYMid meet"
                onMouseDown={handleMouseDown}
                onTouchStart={handleTouchStart}>
                {/* Clip path for fill */}
                <clipPath id="seaSwellSliderClip">
                    <path d={path} />
                </clipPath>

                {/* Filled part */}
                <rect
                    x="0"
                    y="0"
                    width={fillWidth}
                    height={viewBoxHeight}
                    fill="#1669aa" // Tailwind blue-600
                    clipPath="url(#seaSwellSliderClip)"
                />

                {/* Cloud outline */}
                <path
                    d={path}
                    fill="none"
                    stroke="#1e3a8a" // Tailwind blue-900
                    strokeWidth="0.5"
                />

                {/* Thumb */}
                <circle
                    cx={thumbX}
                    cy="40"
                    r="5"
                    fill="white"
                    stroke="#333"
                    strokeWidth="0.5"
                />
                <text
                    x={thumbX}
                    y="42"
                    fontSize="5"
                    textAnchor="middle"
                    fill="#333">
                    ||
                </text>
            </svg>
        </div>
    )
}

export default SeaSwellSlider
