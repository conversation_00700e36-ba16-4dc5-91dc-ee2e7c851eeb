'use client'
// Apollo GraphQL
import { useLazyQuery, useMutation } from '@apollo/client'
import { GET_RADIO_LOGS } from '@/app/lib/graphQL/query'
import { CREATE_RADIO_LOG, UPDATE_RADIO_LOG } from '@/app/lib/graphQL/mutation'

// React hooks
import { useEffect, useState } from 'react'

// Shadcn UI components
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { AlertDialogNew } from '@/components/ui/alert-dialog-new'
import { P } from '@/components/ui/typography'

// Icons
import { Plus } from 'lucide-react'

export default function RadioLogScheduleService({ tss }: { tss: any }) {
    const [radioLogs, setRadioLogs] = useState<any>([])
    const [displayLogAlert, setDisplayLogAlert] = useState(false)
    const [radioTitle, setRadioTitle] = useState('')
    const [currentComment, setCurrentComment] = useState<any>('')
    const [currentLog, setCurrentLog] = useState<any>(false)
    const [openCommentAlert, setOpenCommentAlert] = useState(false)
    const [state, setState] = useState<any>([])

    const [getRadioLogs] = useLazyQuery(GET_RADIO_LOGS, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readRadioLogs.nodes
            if (data) {
                setRadioLogs(data)
                setState(
                    data?.map((log: any) => ({
                        id: log?.id,
                        title: log?.title,
                    })),
                )
            }
        },
        onError: (error: any) => {
            console.error('readRadioLogs error', error)
        },
    })

    useEffect(() => {
        if (tss.id > 0) {
            getRadioLogs({
                variables: {
                    filter: {
                        tripScheduleServiceID: { eq: +tss.id },
                    },
                },
            })
        }
    }, [tss])

    const [createRadioLog] = useMutation(CREATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.createRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            tripScheduleServiceID: { eq: +tss.id },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('createRadioLog error', error)
        },
    })

    const [updateRadioLog] = useMutation(UPDATE_RADIO_LOG, {
        fetchPolicy: 'no-cache',
        onCompleted: (response: any) => {
            const data = response.updateRadioLog
            if (data) {
                getRadioLogs({
                    variables: {
                        filter: {
                            tripScheduleServiceID: { eq: +tss.id },
                        },
                    },
                })
                setDisplayLogAlert(false)
            }
        },
        onError: (error: any) => {
            console.error('updateRadioLog error', error)
        },
    })

    const handleAddRadioLog = () => {
        setDisplayLogAlert(false)
        if (currentLog) {
            updateRadioLog({
                variables: {
                    input: {
                        id: currentLog.id,
                        title: radioTitle,
                    },
                },
            })
        } else {
            createRadioLog({
                variables: {
                    input: {
                        tripScheduleServiceID: +tss.id,
                        title: radioTitle,
                    },
                },
            })
        }
    }

    const handleSortRadioLogs = (newState: any) => {
        setState(newState)
        newState.forEach((item: any, index: number) => {
            const log = radioLogs.find((log: any) => log.id === item.id)
            if (log && log.order !== index + 1) {
                updateRadioLog({
                    variables: {
                        input: {
                            id: log.id,
                            order: index + 1,
                        },
                    },
                })
            }
        })
    }

    return (
        <div className="w-full">
            <div className="p-4">
                <div className="flex flex-col items-start">
                    {radioLogs &&
                    radioLogs.length > 0 &&
                    state &&
                    state.length > 0 ? (
                        <div className="w-full">
                            {radioLogs.map((log: any) => (
                                <div
                                    key={log.id}
                                    className="flex flex-row gap-2 mb-2 justify-between items-center">
                                    <span className="text-sm lg:text-base">
                                        <Button
                                            variant="ghost"
                                            onClick={() => {
                                                setDisplayLogAlert(true)
                                                setCurrentLog(log)
                                            }}>
                                            {log.title}
                                        </Button>
                                    </span>
                                    <Button
                                        variant="destructive"
                                        onClick={() => {
                                            updateRadioLog({
                                                variables: {
                                                    input: {
                                                        id: log.id,
                                                        vesselID: 0,
                                                    },
                                                },
                                            })
                                        }}>
                                        Delete
                                    </Button>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="flex justify-center items-start h-full">
                            <P className="text-muted-foreground">
                                No Radio Logs
                            </P>
                        </div>
                    )}
                    <Button
                        variant="ghost"
                        iconLeft={Plus}
                        className="mt-4"
                        onClick={() => {
                            setDisplayLogAlert(true)
                            setCurrentLog(false)
                        }}>
                        Add Radio Log
                    </Button>
                </div>
            </div>
            <AlertDialogNew
                openDialog={displayLogAlert}
                setOpenDialog={setDisplayLogAlert}
                handleCreate={handleAddRadioLog}
                actionText={currentLog ? 'Update' : 'Create'}
                title={`${currentLog ? 'Edit' : 'Create'} Radio Log`}>
                <Label htmlFor="radioLogTitle" label="Title">
                    <Input
                        id="radioLogTitle"
                        placeholder="Enter Location/Title"
                        defaultValue={currentLog?.title}
                        required
                        onChange={(e) => {
                            setRadioTitle(e.target.value)
                        }}
                    />
                </Label>
            </AlertDialogNew>
            <AlertDialogNew
                openDialog={openCommentAlert}
                setOpenDialog={setOpenCommentAlert}
                handleCreate={() => {
                    updateRadioLog({
                        variables: {
                            input: {
                                id: currentLog.id,
                                comment: currentComment,
                            },
                        },
                    })
                    setOpenCommentAlert(false)
                }}
                actionText="Update"
                title="Comment">
                <Label htmlFor="radioLogComment" label="Comment">
                    <Textarea
                        id="radioLogComment"
                        placeholder="Enter Comment"
                        defaultValue={currentComment}
                        required
                        onChange={(e) => {
                            setCurrentComment(e.target.value)
                        }}
                    />
                </Label>
            </AlertDialogNew>
        </div>
    )
}
