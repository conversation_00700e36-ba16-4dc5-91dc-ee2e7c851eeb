import gql from 'graphql-tag'

export const ReadOneSeaLogsMember = gql`
    query ReadOneSeaLogsMember($filter: SeaLogsMemberFilterFields) {
        readOneSeaLogsMember(filter: $filter) {
            client {
                hqAddress {
                    timeZone
                }
            }
            departments {
                nodes {
                    basicComponents {
                        nodes {
                            id
                            title
                            identifier
                        }
                    }
                }
            }
        }
    }
`

export const ReadDashboardData = gql`
    query ReadDashboardData($archived: Int = 0) {
        readDashboardData(archived: $archived) {
            vessels {
                id
                title
                tasksDue
                trainingsDue
                showOnDashboard
                icon
                iconMode
                photoID
                logentryID
                vesselStatus
            }
        }
    }
`

export const ReadVessels = gql`
    query ReadVessels(
        $vesselFilter: VesselFilterFields = {}
        $statusFilter: VesselStatusFilterFields = {}
    ) {
        readVessels(filter: $vesselFilter) {
            nodes {
                id
                statusHistory(
                    filter: $statusFilter
                    sort: { date: DESC, created: DESC }
                    limit: 1
                ) {
                    nodes {
                        id
                        date
                        status
                        created
                        comment
                        reason
                        otherReason
                        expectedReturn
                    }
                }
            }
        }
    }
`
