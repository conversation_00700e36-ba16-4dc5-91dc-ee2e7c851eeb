'use client'

import Link from 'next/link'
import { useEffect, useRef, useState } from 'react'
import { Heading, ListBox, ListBoxItem } from 'react-aria-components'
import { useToast } from '@/hooks/use-toast'
import FileUpload from '../../../components/file-upload'
import { useLazyQuery, useMutation } from '@apollo/client'
import GET_OTHER_COMPANIES from '@/app/lib/graphQL/query/GET_OTHER_COMPANIES'
import { InputSkeleton } from '../../../components/skeletons'
import DialogCreateCompany from './dialog-create-company'
import { GET_KEY_CONTACT_BY_ID } from '@/app/lib/graphQL/query'
import FileItem from '@/components/file-item'
import { Check, XCircle as XCircleIcon } from 'lucide-react'
import {
    CREATE_KEY_CONTACT,
    UPDATE_KEY_CONTACT,
} from '@/app/lib/graphQL/mutation'
import { useRouter } from 'next/navigation'
import DeleteKey<PERSON>ontact from './delete-key-contact'
import { getPermissions, hasPermission } from '@/app/helpers/userHelper'
import { IKeyContactPermission } from './key-contact-list'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Combobox } from '@/components/ui/comboBox'
import { Button } from '@/components/ui/button'

interface IProps {
    id?: number
}

interface ISelectItem {
    value: string
    label: string
}

interface IFormValue {
    firstName: string
    surname: string
    phone?: string
    cellPhone?: string
    email?: string
    vhfChannel?: number | string
    address?: string
}

export default function KeyContactForm({ id }: IProps) {
    const [permission, setPermission] = useState<IKeyContactPermission>({
        EDIT_KEY_CONTACT: undefined,
        DELETE_KEY_CONTACT: undefined,
    })
    const formRef = useRef<HTMLFormElement>(null)
    const router = useRouter()
    const { toast } = useToast()

    //form section
    const [formValue, setFormValue] = useState<IFormValue>({
        firstName: '',
        surname: '',
        phone: '',
        cellPhone: '',
        email: '',
        vhfChannel: '',
        address: '',
    })
    const [attachments, setAttachments] = useState<Array<Record<string, any>>>(
        [],
    )

    const deleteFile = async (id: number) => {
        setAttachments((prev) => prev.filter((doc) => doc.id !== id))
    }

    const handleInputChange = <K extends keyof IFormValue>(
        name: K,
        value: string,
    ) => {
        setFormValue((prev) => ({ ...prev, [name]: value }))
    }
    //end form section

    //start handle select company
    const [openDialogNewCompany, setOpenDialogNewCompany] = useState(false)
    const [selectedCompany, setSelectedCompany] = useState<ISelectItem | null>(
        null,
    )
    const [companies, setCompanies] = useState<ISelectItem[]>([])
    const [
        queryGetCompanies,
        { called: calledCompanies, loading: loadingCompanies },
    ] = useLazyQuery(GET_OTHER_COMPANIES, {
        fetchPolicy: 'cache-and-network',
        onCompleted: (response: any) => {
            const data = response.readOtherCompanies.nodes
            const companiesSelect: ISelectItem[] = [
                {
                    value: 'newOtherCompany',
                    label: ' ---- Create Company ---- ',
                },
                ...data.map((item: any) => ({
                    value: item.id,
                    label: item.title,
                })),
            ]
            setCompanies(companiesSelect)
        },
        onError: (error: any) => {
            console.error('queryGetCompanies error', error)
        },
    })

    const onCompanySelectChange = (selected: ISelectItem) => {
        if (selected.value === 'newOtherCompany') {
            setOpenDialogNewCompany(true)
            return
        }

        setSelectedCompany(selected)
    }

    const onCreateNewCompanySuccess = (newCompany: any) => {
        const selectItem = {
            value: newCompany.id,
            label: newCompany.title,
        }

        setCompanies((prev) => [...prev, selectItem])

        setSelectedCompany(selectItem)
    }

    //end handle select company

    // edit key contact section
    const loadKeyContact = async (id: number) => {
        const { data } = await queryGetKeyContactByID({
            variables: { filter: { id: { eq: id } } },
        })

        const existingKeyContact = data.readOneKeyContact

        setFormValue({
            firstName: existingKeyContact?.firstName || '',
            surname: existingKeyContact?.surname || '',
            phone: existingKeyContact?.phone || '',
            cellPhone: existingKeyContact?.cellPhone || '',
            email: existingKeyContact?.email || '',
            vhfChannel: existingKeyContact?.vhfChannel || '',
            address: existingKeyContact?.address || '',
        })

        const contactCompany = existingKeyContact?.company
        if (contactCompany) {
            setSelectedCompany({
                value: contactCompany.id,
                label: contactCompany.title,
            })
        }

        if (existingKeyContact?.attachments.nodes.length > 0) {
            setAttachments(existingKeyContact?.attachments.nodes)
        }
    }

    const [queryGetKeyContactByID] = useLazyQuery(GET_KEY_CONTACT_BY_ID, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('getKeyContactByID error', error)
        },
    })
    //edit key contact section

    //submit handler
    const [
        saveMutationKeyContact,
        { called: saveMutationCalled, loading: saveMutationLoading },
    ] = useMutation(id ? UPDATE_KEY_CONTACT : CREATE_KEY_CONTACT, {
        onCompleted: () => {
            router.push('/key-contacts')
        },
        onError: (error: any) => {
            console.error('mutationcreateSupplier error', error)
        },
    })

    const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
        e.preventDefault()

        if (permission.EDIT_KEY_CONTACT === false) {
            toast({
                variant: 'destructive',
                title: 'Permission Error',
                description:
                    "You don't have permission to create or update key contact!",
            })
            return
        }

        const input = {
            id: id || '',
            ...formValue,
            vhfChannel: formValue.vhfChannel
                ? parseInt(`${formValue.vhfChannel ?? '0'}`)
                : null,
            companyID: selectedCompany?.value ?? '',
            attachments: attachments.map((item: any) => item.id).join(','),
        }

        saveMutationKeyContact({ variables: { input } })
    }

    //end submit handler

    const initPermission = () => {
        const permissions = getPermissions('EDIT_KEY_CONTACT')

        Object.keys(permission).forEach((value) => {
            const hasThisPermission = hasPermission(value, permissions)

            setPermission((prev) => ({ ...prev, [value]: hasThisPermission }))
        })
    }

    //fetch key contact detail if id is > 0
    useEffect(() => {
        if (id) {
            loadKeyContact(id)
        }
    }, [id])

    useEffect(() => {
        queryGetCompanies()
        initPermission()
    }, [])

    return (
        <div className="w-full p-0  border border-slblue-50 bg-blue-50 ">
            <div className="flex justify-between px-8 py-3 items-center bg-sldarkblue-900 rounded-t-lg">
                <Heading className="font-light font-monasans text-2xl text-white">
                    {id ? 'Edit Key Contact' : 'Add Key Contact'}
                </Heading>
            </div>
            <form onSubmit={handleSubmit} ref={formRef}>
                <div className="grid md:grid-cols-3 gap-6 pb-4 pt-3 px-4">
                    <div className="text-xl">Key Contact Details</div>
                    <div className="md:col-span-2 flex flex-col gap-4">
                        <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="firstName">First name</Label>
                                <Input
                                    id="firstName"
                                    type="text"
                                    placeholder="First name"
                                    name="firstName"
                                    value={formValue.firstName}
                                    required
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="surname">Surname</Label>
                                <Input
                                    id="surname"
                                    type="text"
                                    placeholder="Surname"
                                    name="surname"
                                    value={formValue.surname}
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                        </div>
                        <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="phone">Phone</Label>
                                <Input
                                    id="phone"
                                    type="text"
                                    placeholder="Phone"
                                    name="phone"
                                    value={formValue.phone}
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="cellPhone">Cell Phone</Label>
                                <Input
                                    id="cellPhone"
                                    type="text"
                                    placeholder="Cell Phone"
                                    name="cellPhone"
                                    value={formValue.cellPhone}
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                        </div>
                        <div className="grid md:grid-cols-2 gap-4">
                            <div className="space-y-2">
                                <Label htmlFor="email">Email</Label>
                                <Input
                                    id="email"
                                    type="email"
                                    placeholder="Email"
                                    name="email"
                                    value={formValue.email}
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                            <div className="space-y-2">
                                <Label htmlFor="vhfChannel">VHF Channel</Label>
                                <Input
                                    id="vhfChannel"
                                    type="number"
                                    placeholder="VHF Channel"
                                    name="vhfChannel"
                                    value={formValue.vhfChannel}
                                    onChange={(e) =>
                                        handleInputChange(
                                            e.target.name as any,
                                            e.target.value,
                                        )
                                    }
                                />
                            </div>
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="address">Address</Label>
                            <Textarea
                                id="address"
                                name="address"
                                value={formValue.address}
                                onChange={(e) =>
                                    handleInputChange(
                                        e.target.name as any,
                                        e.target.value,
                                    )
                                }
                                placeholder="Address"
                                rows={3}
                            />
                        </div>
                        <div className="space-y-2">
                            <Label htmlFor="company">Company</Label>
                            {calledCompanies && loadingCompanies ? (
                                <InputSkeleton />
                            ) : (
                                <Combobox
                                    id="company"
                                    options={companies.map((company) => ({
                                        label: company.label,
                                        value: company.value,
                                    }))}
                                    value={
                                        selectedCompany
                                            ? {
                                                  label: selectedCompany.label,
                                                  value: selectedCompany.value,
                                              }
                                            : undefined
                                    }
                                    onChange={(selected: any) => {
                                        if (selected) {
                                            onCompanySelectChange({
                                                value: selected.value,
                                                label: selected.label,
                                            })
                                        }
                                    }}
                                    placeholder="Select Company"
                                />
                            )}
                        </div>
                    </div>
                </div>
            </form>
            <hr className="my-4" />
            <div className="grid md:grid-cols-3 gap-6 pb-4 pt-3 px-4">
                <div className="text-xl">Attachment</div>
                <div className="col-span-2 block pt-3 pb-3 px-7 bg-white border-slblue-200 rounded-lg">
                    <div className="my-4">
                        <div className="grid-cols-1 md:col-span-2 lg:col-span-3">
                            <FileUpload
                                setDocuments={setAttachments}
                                text=""
                                subText="Drag files here or upload"
                                bgClass="bg-slblue-50"
                                documents={attachments}
                                // multipleUpload
                            />
                        </div>
                    </div>
                    <div className="block">
                        {attachments.length > 0 && (
                            <ListBox aria-label="Documents" className={``}>
                                {attachments.map((document: any) => (
                                    <ListBoxItem
                                        key={document.id}
                                        textValue={document.name}
                                        className="flex items-center gap-8 justify-between p-2.5 bg-slblue-50 rounded-lg border border-slblue-300   mb-4 hover:bg-slblue-1000 hover-text-white">
                                        <FileItem document={document} />
                                        <Button
                                            variant="text"
                                            size="sm"
                                            onClick={() =>
                                                deleteFile(document.id)
                                            }
                                            className="text-destructive hover:text-destructive/80"
                                            iconLeft={XCircleIcon}
                                            iconOnly
                                        />
                                    </ListBoxItem>
                                ))}
                            </ListBox>
                        )}
                    </div>
                </div>
            </div>
            <hr className="my-4" />
            <div className="flex justify-end px-8 pb-4 pt-4">
                <Link
                    href="/key-contacts"
                    className="group inline-flex justify-center items-center">
                    <Button variant="text" className="mr-4">
                        Cancel
                    </Button>
                </Link>
                {id && permission.DELETE_KEY_CONTACT === true && (
                    <DeleteKeyContact
                        id={id}
                        fullName={`${formValue.firstName} ${formValue.surname}`}
                    />
                )}
                <Button
                    disabled={
                        permission.EDIT_KEY_CONTACT === false ||
                        (saveMutationCalled && saveMutationLoading)
                    }
                    onClick={() => formRef.current?.requestSubmit()}
                    iconLeft={Check}>
                    Save
                </Button>
            </div>
            <DialogCreateCompany
                isOpen={openDialogNewCompany}
                setIsOpen={setOpenDialogNewCompany}
                onCreateSuccess={onCreateNewCompanySuccess}
            />
        </div>
    )
}
