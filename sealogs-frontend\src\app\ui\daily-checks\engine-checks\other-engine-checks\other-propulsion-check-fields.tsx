'use client'

import { Dispatch, useMemo, useState } from 'react'
import { useEngineFields } from '../use-engine-fields'
import dayjs from 'dayjs'
import {
    composeField,
    displayDescription,
    displayField,
    getFieldLabel,
    getFilteredFields,
} from '../../actions'
import { Button } from '@/components/ui/button'
import { AlertCircle } from 'lucide-react'
import { Separator } from '@/components/ui/separator'
import <PERSON><PERSON><PERSON><PERSON> from '../../crew-checker'
import {
    CheckFieldTopContent,
    DailyCheckField,
} from '@/components/daily-check-field'

interface IProps {
    logentryID: any
    logBookConfig: any
    vesselDailyCheck: any
    locked: boolean
    edit_logBookEntry: boolean
    offline?: boolean
    getComment: (fieldName: string, commentType?: string) => any
    handleEngineChecks: (check: boolean, value: any) => Promise<void>
    setDescriptionPanelHeading: Dispatch<string>
    setDescriptionPanelContent: Dispatch<string>
    setOpenDescriptionPanel: Dispatch<boolean>
    showCommentPopup: (comment: string, field: any) => void
    updateVesselDailyCheck_LogBookEntrySection: Function
}

export default function OtherPropulsionCheckFields({
    logentryID,
    logBookConfig,
    vesselDailyCheck,
    locked,
    edit_logBookEntry,
    offline = false,
    getComment,
    handleEngineChecks,
    setDescriptionPanelHeading,
    setDescriptionPanelContent,
    setOpenDescriptionPanel,
    showCommentPopup,
    updateVesselDailyCheck_LogBookEntrySection,
}: IProps) {
    const { otherPropulsionCheckFields } = useEngineFields(
        logBookConfig,
        vesselDailyCheck,
    )

    const [otherEngineCrewResponsible, setOtherEngineCrewResponsible] =
        useState<any>(
            vesselDailyCheck?.otherEngineCrewResponsible?.nodes?.map(
                (member: any) => ({
                    label: member.firstName + ' ' + member.surname,
                    value: member.id,
                }),
            ),
        )
    const [otherEngineCheckTime, setOtherEngineCheckTime] = useState<any>(
        dayjs(vesselDailyCheck?.otherEngineCheckTime ?? new Date()),
    )

    const handleOtherEngineCrewResponsible = async (crews: any) => {
        setOtherEngineCrewResponsible(crews)
        const crewResponsibleIDs = crews?.map((member: any) => member.value)
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                otherEngineCrewResponsible: crewResponsibleIDs.join(','),
            }
            if (offline) {
                console.log(' offline')
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const handleOtherEngineCheckTime = async (date: any) => {
        setOtherEngineCheckTime(dayjs(date))
        if (vesselDailyCheck?.id > 0) {
            const variables = {
                id: vesselDailyCheck?.id,
                logBookEntryID: logentryID,
                otherEngineCheckTime: dayjs(date).format('YYYY-MM-DD HH:mm'),
            }
            if (offline) {
                console.log(' offline')
                {
                    /*
                    Offline functionality code:
                    const newVesselDailyCheck =
                        await dailyCheckModel.save(variables)
                    // setSaving(true)
                    setSaving(false)
                    setVesselDailyCheck([newVesselDailyCheck])
                    const sections = logbook.logBookEntrySections.nodes
                    const section = {
                        className: 'SeaLogs\\VesselDailyCheck_LogBookEntrySection',
                        id: `${vesselDailyCheck.id}`,
                        logBookComponentClass: 'VesselDailyCheck_LogBookComponent',
                        __typename: 'VesselDailyCheck_LogBookEntrySection',
                    }
                    if (
                        !sections.some(
                            (s: any) =>
                                JSON.stringify(s) === JSON.stringify(section),
                        )
                    ) {
                        sections.push(section)
                    }
                    const lb = {
                        ...logbook,
                        logBookEntrySections: { nodes: sections },
                    }
                    await logBookModel.save(lb)
                    getOfflineLogBookEntry()
                */
                }
            } else {
                updateVesselDailyCheck_LogBookEntrySection({
                    variables: {
                        input: variables,
                    },
                })
            }
        }
    }

    const filteredFields = useMemo(() => {
        return (
            getFilteredFields(
                otherPropulsionCheckFields,
                true,
                logBookConfig,
            )?.filter((groupField: any) =>
                displayField(groupField.name, logBookConfig),
            ) ?? []
        )
    }, [logBookConfig, otherPropulsionCheckFields])

    return (
        <>
            {filteredFields.filter((groupField: any) =>
                groupField?.items?.some((field: any) =>
                    displayField(field.name, logBookConfig),
                ),
            ).length > 0 && <CheckFieldTopContent />}
            {logBookConfig &&
                vesselDailyCheck &&
                filteredFields.map((groupField: any, groupIndex: number) => (
                    <div
                        key={`group-${groupField.name}-${groupIndex}`}
                        className="mb-6">
                        {groupField?.items
                            ?.filter((field: any) =>
                                displayField(field.name, logBookConfig),
                            )
                            ?.map((field: any, index: number) => (
                                <DailyCheckField
                                    locked={locked || !edit_logBookEntry}
                                    key={`field-${field.name}-${field.value}-${index}`}
                                    displayField={displayField(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    displayDescription={displayDescription(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    setOpenDescriptionPanel={
                                        setOpenDescriptionPanel
                                    }
                                    setDescriptionPanelHeading={
                                        setDescriptionPanelHeading
                                    }
                                    displayLabel={getFieldLabel(
                                        field.name,
                                        logBookConfig,
                                    )}
                                    inputId={field.value}
                                    handleNoChange={() =>
                                        handleEngineChecks(false, field.value)
                                    }
                                    defaultNoChecked={
                                        field.checked === 'Not_Ok'
                                    }
                                    handleYesChange={() =>
                                        handleEngineChecks(true, field.value)
                                    }
                                    defaultYesChecked={field.checked === 'Ok'}
                                    commentAction={() =>
                                        showCommentPopup(
                                            getComment(field.name),
                                            composeField(
                                                field.name,
                                                logBookConfig,
                                            ),
                                        )
                                    }
                                    comment={getComment(field.name)?.comment}
                                />
                            ))}
                        {displayDescription(groupField.name, logBookConfig) && (
                            <Button
                                variant="text"
                                iconLeft={AlertCircle}
                                onClick={() => {
                                    setDescriptionPanelContent(
                                        displayDescription(
                                            groupField.name,
                                            logBookConfig,
                                        ),
                                    )
                                    setOpenDescriptionPanel(true)
                                    setDescriptionPanelHeading(groupField.name)
                                }}
                            />
                        )}
                    </div>
                ))}
            <CrewChecker
                crewKey="OtherEngineCrewResponsible"
                timeKey="OtherEngineCheckTime"
                logBookConfig={logBookConfig}
                locked={locked}
                offline={offline}
                edit_logBookEntry={edit_logBookEntry}
                setCrewResponsible={handleOtherEngineCrewResponsible}
                crewResponsible={otherEngineCrewResponsible}
                checkTime={otherEngineCheckTime}
                handleCheckTime={handleOtherEngineCheckTime}
                setCheckTime={setOtherEngineCheckTime}
            />
        </>
    )
}
