# DatePicker Component

The DatePicker component is a versatile date and datetime picker that supports single date selection and date range selection.

## Features

- Single date or date range selection
- Date-only or datetime selection
- Custom date formatting
- Time picker integration
- Validation rules for date selection
- Dual clear functionality (secondary X button and primary Clear button)
- Custom icon support
- Accessibility features

## Usage

```tsx
import DatePicker from '@/components/DateRange'
import { Clock, Calendar } from 'lucide-react'

// Basic date picker
<DatePicker
  onChange={(date) => console.log(date)}
  mode="single"
/>

// Date picker with date and time
<DatePicker
  onChange={(date) => console.log(date)}
  mode="single"
  type="datetime"
/>

// Date range picker
<DatePicker
  onChange={({ startDate, endDate }) => console.log(startDate, endDate)}
  mode="range"
/>

// Date picker with custom icon
<DatePicker
  onChange={(date) => console.log(date)}
  mode="single"
  icon={Clock}
/>

// Date picker with JSX icon element
<DatePicker
  onChange={(date) => console.log(date)}
  mode="single"
  icon={<Calendar className="text-blue-500" />}
/>

// Date picker with clear functionality
<DatePicker
  onChange={(date) => console.log(date)}
  mode="single"
  clearable={true}
  confirmSelection={true}
/>

// Date picker with validation
<DatePicker
  onChange={(date) => console.log(date)}
  mode="single"
  validation={{
    minDate: new Date(2023, 0, 1),
    maxDate: new Date(2023, 11, 31),
    disabledDaysOfWeek: [0, 6] // Disable weekends
  }}
/>
```

## Props

| Prop | Type | Default | Description |
| ---- | ---- | ------- | ----------- |
| `onChange` | `(value: any) => void` | - | Callback when date(s) change |
| `mode` | `'single' \| 'range'` | `'range'` | Mode of the date picker |
| `type` | `'date' \| 'datetime'` | `'date'` | Type of the date picker |
| `value` | `Date \| DateRange` | - | Selected date or date range |
| `dateFormat` | `string` | `'dd LLLL, y'` | Date format for display |
| `timeFormat` | `string` | `'h:mm a'` | Time format for display |
| `disabled` | `boolean` | `false` | Whether the date picker is disabled |
| `placeholder` | `string` | `'Select date'` | Placeholder text when no date is selected |
| `validation` | `DateValidation` | - | Validation rules for date selection |
| `numberOfMonths` | `number` | `1` or `2` | Number of months to display |
| `closeOnSelect` | `boolean` | `true` | Whether to close the popover when a date is selected |
| `showWeekNumbers` | `boolean` | `false` | Whether to show week numbers |
| `timeMode` | `'single' \| 'range'` | `'single'` | Mode of the time picker |
| `timeInterval` | `number` | `30` | Interval between time options in minutes |
| `label` | `string` | - | Label for the date picker |
| `labelPosition` | `'top' \| 'left' \| 'right'` | `'top'` | Position of the label |
| `clearable` | `boolean` | `false` | Whether to show a clear button |
| `icon` | `React.ReactNode \| LucideIcon` | `<CalendarIcon />` | Custom icon for the date picker |
| `className` | `string` | - | Additional class names |

## Clear Functionality

When `clearable={true}` is set, the DatePicker provides two ways to clear selected dates:

### Secondary Clear Button (X Icon)
- Appears as a small X icon beside the date picker input when dates are selected
- Provides quick access to clear dates without opening the popover
- Positioned on the right side of the input field
- Only visible when `clearable={true}` and dates are selected

### Primary Clear Button (Inside Popover)
- Appears as a "Clear" button inside the popover next to the "Confirm" button
- Only visible when both `clearable={true}` and `confirmSelection={true}`
- Clears the dates and automatically closes the popover
- Provides a more explicit clearing action within the date selection interface

Both clear buttons:
- Reset the date range state completely
- Trigger the `onChange` callback with `null`
- Clear any associated time selections
- Maintain proper accessibility with ARIA labels

## Notes

- The `includeTime` prop is deprecated. Use `type="datetime"` instead.
- When using `mode="range"`, the `onChange` callback receives an object with `startDate` and `endDate` properties.
- When using `mode="single"`, the `onChange` callback receives a `Date` object.
- Clear functionality is only available when `clearable={true}` is set.
