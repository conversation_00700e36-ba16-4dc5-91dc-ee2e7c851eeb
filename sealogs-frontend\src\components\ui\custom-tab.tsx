'use client'

import React from 'react'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ScrollArea } from '@/components/ui/scroll-area'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu'

import { Menu } from 'lucide-react'
import { Button } from './button'

export interface TabItem {
    id: string
    value: string
    label: string
    icon?: React.ReactNode
    component?: React.ReactNode
}

interface CustomTabProps {
    tabs: TabItem[]
    activeTab: string
    onTabChange: (value: string) => void
    mobileTitle?: string
    className?: string
    tabsListClassName?: string
    tabsTriggerClassName?: string
    tabsContentClassName?: string
    showMobileDropdown?: boolean
    showScrollableTabs?: boolean
}

export const CustomTab: React.FC<CustomTabProps> = ({
    tabs,
    activeTab,
    onTabChange,
    mobileTitle = 'Navigation',
    className = '',
    tabsListClassName = '',
    tabsTriggerClassName = '',
    tabsContentClassName = '',
    showMobileDropdown = true,
    showScrollableTabs = false,
}) => {
    // Filter out tabs without components
    const tabsWithComponents = tabs.filter((tab) => tab.component)

    return (
        <div className={`w-full ${className}`}>
            {/* Desktop Navigation */}
            <div className="hidden md:block mb-6">
                <Tabs value={activeTab} onValueChange={onTabChange}>
                    {showScrollableTabs ? (
                        <ScrollArea className="w-full pb-2">
                            <TabsList
                                className={`inline-flex w-max px-1 ${tabsListClassName}`}>
                                {tabs.map((tab) => (
                                    <TabsTrigger
                                        key={tab.id}
                                        value={tab.value}
                                        className={tabsTriggerClassName}>
                                        {tab.icon && (
                                            <span className="mr-2">
                                                {tab.icon}
                                            </span>
                                        )}
                                        {tab.label}
                                    </TabsTrigger>
                                ))}
                            </TabsList>
                        </ScrollArea>
                    ) : (
                        <TabsList className={tabsListClassName}>
                            {tabs.map((tab) => (
                                <TabsTrigger
                                    key={tab.id}
                                    value={tab.value}
                                    className={tabsTriggerClassName}>
                                    {tab.icon && (
                                        <span className="mr-2">{tab.icon}</span>
                                    )}
                                    {tab.label}
                                </TabsTrigger>
                            ))}
                        </TabsList>
                    )}

                    {/* Render tab content */}
                    {tabsWithComponents.map((tab) => (
                        <TabsContent
                            key={tab.id}
                            value={tab.value}
                            className={tabsContentClassName}>
                            {tab.component}
                        </TabsContent>
                    ))}
                </Tabs>
            </div>

            {/* Mobile Navigation */}
            {showMobileDropdown && (
                <div className="md:hidden mb-4 flex justify-between items-center">
                    <h2 className="text-lg font-medium">{mobileTitle}</h2>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                            <Button
                                variant="secondary"
                                size="sm"
                                iconLeft={Menu}
                                aria-label="Menu"
                            />
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-56">
                            {tabs.map((tab) => (
                                <DropdownMenuItem
                                    key={tab.id}
                                    onClick={() => onTabChange(tab.value)}
                                    className={
                                        activeTab === tab.value
                                            ? 'bg-muted'
                                            : ''
                                    }>
                                    {tab.icon && (
                                        <span className="mr-2">{tab.icon}</span>
                                    )}
                                    {tab.label}
                                </DropdownMenuItem>
                            ))}
                        </DropdownMenuContent>
                    </DropdownMenu>
                </div>
            )}

            {/* Mobile Content - Show active tab content */}
            <div className="md:hidden">
                {
                    tabsWithComponents.find((tab) => tab.value === activeTab)
                        ?.component
                }
            </div>
        </div>
    )
}

export default CustomTab
