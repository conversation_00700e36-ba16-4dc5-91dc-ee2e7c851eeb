import * as dateFns from 'date-fns';
import { getServiceSummary, IStatusHistory, IVesselServiceSummary, VesselStatus } from "@/app/ui/reporting/new-service-report/action";


type TVesselSummary = {
  [vesselID: number]: IVesselServiceSummary
}

export const createServiceSummaryGroupByVessel = (
  status: IStatusHistory[],
  startingStatus: VesselStatus,
  startDate: Date,
  endDate: Date,
): TVesselSummary => {
  const vesselIDs = new Set(status.map((val) => val.vesselID!));

  let result: TVesselSummary = {};
  vesselIDs.forEach((vesselID) => {
    const vesselServiceHistories = status.filter((val) => val.vesselID === vesselID);

    result[vesselID] = getServiceSummary(vesselServiceHistories, startingStatus, startDate, endDate);
  })

  return result;
}

export const calculateServiceSummary = (serviceSummaryByVessel: TVesselSummary): IVesselServiceSummary => {
  let summary: IVesselServiceSummary = {
    onVoyage: 0,
    availableForVoyage: 0,
    outOfService: 0
  }

  for (const vesselID in serviceSummaryByVessel) {
    if (Object.prototype.hasOwnProperty.call(serviceSummaryByVessel, vesselID)) {
      const vessselSummary = serviceSummaryByVessel[vesselID];

      summary.onVoyage += vessselSummary.onVoyage;
      summary.availableForVoyage += vessselSummary.availableForVoyage;
      summary.outOfService += vessselSummary.outOfService;
    }
  }

  return summary;
}

export const populateMonths = (start: Date, end: Date): string[] => {
  let items: string[] = []

  let currentMonth = new Date(start.getTime())
  do {
    items.push(dateFns.format(currentMonth, 'yyyy-MM') + '-01')

    currentMonth = new Date(dateFns.addMonths(currentMonth, 1).getTime())
  } while (dateFns.isSameMonth(currentMonth, end) === false)

  items.push(dateFns.format(currentMonth, 'yyyy-MM') + '-01')

  return items
}