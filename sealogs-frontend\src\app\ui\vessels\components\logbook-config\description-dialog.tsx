'use client'

import {
    Sheet,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    She<PERSON><PERSON><PERSON><PERSON>,
} from '@/components/ui'

interface IProps {
    open: boolean
    onOpenChange: (open: boolean) => void
    title: string
    content: string
}

export const DescriptionDialog = ({
    open,
    onOpenChange,
    title,
    content,
}: IProps) => {
    return (
        <Sheet open={open} onOpenChange={onOpenChange}>
            <SheetContent>
                <SheetHeader>
                    <SheetTitle>
                        Field - <span className="font-thin">{title}</span>
                    </SheetTitle>
                </SheetHeader>
                <SheetBody>
                    <div
                        className="leading-loose"
                        dangerouslySetInnerHTML={{
                            __html: content,
                        }}></div>
                </SheetBody>
            </SheetContent>
        </Sheet>
    )
}
