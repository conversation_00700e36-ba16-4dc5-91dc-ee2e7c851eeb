import { precipitations, visibilities } from '@/app/helpers/weatherHelper'
import { useEffect, useState } from 'react'
import VisibilityDropdown from '../visibility-dropdown'
import PrecipitationDropdown from '../precipitation-dropdown'
import CloudCoverSlider from '../cloud-cover-slider'
import { Label } from '@/components/ui/label'
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui'
import CloudCoverImageSlider from '../cloud-cover-image-slider'
import PrecipitationPicker from '../precipitation-picker'

const CloudWidget = ({
    visibilityValue = 'good',
    precipitationValue = 'none',
    cloudCoverValue = 0,
    editMode = false,
    iconOnly = false,
    onChange,
}: {
    visibilityValue?: string
    precipitationValue?: string
    cloudCoverValue?: number
    editMode?: boolean
    iconOnly?: boolean
    onChange?: any
}) => {
    const [selectedVisibility, setSelectedVisibility] = useState(
        visibilities.find((item) => item.value === visibilityValue) ||
            visibilities[0],
    )
    const [selectedPrecipitation, setSelectedPrecipitation] = useState(
        precipitations.find((item) => item.value === precipitationValue) ||
            precipitations[0],
    )
    const [cloudCover, setCloudCover] = useState(cloudCoverValue)
    const [fillAmount, setFillAmount] = useState(0)
    const handleVisibilityChange = (item: any) => {
        const visibility =
            visibilities.find((option) => option.value === item) ||
            visibilities[0]

        setSelectedVisibility(visibility)
        if (onChange) {
            onChange({
                visibility: visibility,
                precipitation: selectedPrecipitation,
                cloudCover,
            })
        }
    }

    const handlePrecipitationChange = (item: any) => {
        setSelectedPrecipitation(item)
        if (onChange) {
            onChange({
                visibility: selectedVisibility,
                precipitation: item,
                cloudCover,
            })
        }
    }

    const handleCloudCoverChange = (value: number) => {
        setCloudCover(value)
        if (onChange) {
            onChange({
                visibility: selectedVisibility,
                precipitation: selectedPrecipitation,
                cloudCover: value,
            })
        }
    }

    const cloudSVG = (
        <svg
            xmlns="http://www.w3.org/2000/svg"
            width="125.739"
            height="74.389"
            viewBox="0 0 125.739 74.389"
            className={` ${iconOnly ? 'h-8' : ''}`}>
            <defs>
                <linearGradient
                    id="fillGradient"
                    x1="0%"
                    y1="0%"
                    x2={`${fillAmount}%`}
                    y2="0%">
                    <stop offset="0%" stopColor="#035388" />
                    <stop offset="100%" stopColor="#035388" />
                </linearGradient>
                <mask
                    id="cloudMask"
                    maskUnits="userSpaceOnUse"
                    x="0"
                    y="0"
                    width="125.739"
                    height="74.389">
                    <path
                        id="Path_238"
                        data-name="Path 238"
                        d="M103.841,31.572a21.814,21.814,0,0,0-5.578.724,28.436,28.436,0,0,0-39.287-18.9A21.42,21.42,0,0,0,18.089,19.1a21.076,21.076,0,0,0,2.357,12.532A21.953,21.953,0,0,0,8.705,36.2,20.835,20.835,0,0,0,6.074,66.968a21.5,21.5,0,0,0,15.821,6.913h81.958a21.156,21.156,0,1,0-.012-42.309Z"
                        transform="translate(0.011 0.008)"
                        fill="#fff"
                    />
                </mask>
            </defs>
            <rect
                x="0"
                y="0"
                width="100%"
                height="100%"
                fill="url(#fillGradient)"
                mask="url(#cloudMask)"
                style={{
                    clipPath: `inset(0 ${100 - fillAmount}% 0 0)`,
                }}
            />

            <path
                id="Path_238"
                data-name="Path 238"
                d="M103.841,31.572a21.814,21.814,0,0,0-5.578.724,28.436,28.436,0,0,0-39.287-18.9A21.42,21.42,0,0,0,18.089,19.1a21.076,21.076,0,0,0,2.357,12.532A21.953,21.953,0,0,0,8.705,36.2,20.835,20.835,0,0,0,6.074,66.968a21.5,21.5,0,0,0,15.821,6.913h81.958a21.156,21.156,0,1,0-.012-42.309Z"
                transform="translate(0.011 0.008)"
                fill="none"
                stroke="#035388"
                strokeMiterlimit="10"
                strokeWidth="1"
            />
        </svg>
    )
    const fillColors = [
        '#FFFFFF',
        '#D9E3EC',
        '#B3C8DA',
        '#8DACCB',
        '#6691BB',
        '#035388',
    ]

    useEffect(() => {
        const meterValue = Math.ceil(cloudCoverValue) // round up the meter value
        const fillPercentage = (meterValue / 100) * 100 // calculate the fill percentage
        setFillAmount(Math.min(Math.max(fillPercentage, 0), 100)) // cap the fill amount between 0 and 100

        setCloudCover(cloudCoverValue)
    }, [cloudCoverValue])

    useEffect(() => {
        setSelectedVisibility(
            visibilities.find((item) => item.value === visibilityValue) ||
                visibilities[0],
        )
        setSelectedPrecipitation(
            precipitations.find((item) => item.value === precipitationValue) ||
                precipitations[0],
        )
    }, [visibilityValue, precipitationValue])

    if (editMode) {
        return (
            <div className="flex flex-col w-full gap-4">
                <Label label="Visibility">
                    <VisibilityDropdown
                        value={visibilityValue}
                        onChange={handleVisibilityChange}
                    />
                </Label>

                <Label label={`Precipitation: ${selectedPrecipitation.label}`}>
                    <PrecipitationPicker
                        selected={selectedPrecipitation}
                        onChange={handlePrecipitationChange}
                    />
                </Label>

                <Label label={`Cloud Cover: ${cloudCover}%`}>
                    <CloudCoverImageSlider
                        value={cloudCover}
                        onChange={handleCloudCoverChange}
                    />
                    {/* 
                    <CloudCoverSlider
                        value={cloudCover}
                        onChange={handleCloudCoverChange}
                    /> */}
                </Label>
            </div>
        )
    }

    return (
        <div className="flex flex-col items-center p-4">
            {!iconOnly && (
                <div>
                    <span className="font-semibold">Visibility:</span>{' '}
                    {selectedVisibility.label}
                </div>
            )}
            {iconOnly && (
                <>
                    <Popover triggerType="hover">
                        <PopoverTrigger asChild>
                            <div className="flex flex-col items-center">
                                <div>{cloudSVG}</div>
                                <div className="flex flex-inline py-2">
                                    {precipitations.map(
                                        (precipitation, index) => {
                                            const selectedIndex =
                                                precipitations.findIndex(
                                                    (p) =>
                                                        p.value ===
                                                        selectedPrecipitation.value,
                                                )
                                            const fillColor =
                                                index <= selectedIndex
                                                    ? precipitation.fillColor
                                                    : '#FFFFFF'
                                            return (
                                                <div key={precipitation.value}>
                                                    <DropletSVG
                                                        iconOnly={iconOnly}
                                                        fill={fillColor}
                                                    />
                                                </div>
                                            )
                                        },
                                    )}
                                </div>
                            </div>
                        </PopoverTrigger>
                        <PopoverContent>
                            <div>
                                <div>
                                    <span className="font-bold">
                                        Visibility:
                                    </span>{' '}
                                    {selectedVisibility.label}
                                </div>
                                <div>{selectedPrecipitation.label}</div>
                                <div>
                                    <span className="font-bold">
                                        Cloud Cover:
                                    </span>{' '}
                                    {cloudCoverValue}%
                                </div>
                            </div>
                        </PopoverContent>
                    </Popover>
                </>
            )}
            {!iconOnly && (
                <>
                    <div>{cloudSVG}</div>
                    <div className="flex flex-inline py-2">
                        {precipitations.map((precipitation, index) => {
                            const selectedIndex = precipitations.findIndex(
                                (p) => p.value === selectedPrecipitation.value,
                            )
                            const fillColor =
                                index <= selectedIndex
                                    ? precipitation.fillColor
                                    : '#FFFFFF'

                            return (
                                <div key={precipitation.value}>
                                    <DropletSVG
                                        iconOnly={iconOnly}
                                        fill={fillColor}
                                    />
                                </div>
                            )
                        })}
                    </div>
                </>
            )}

            {!iconOnly && (
                <>
                    <div>{selectedPrecipitation.label}</div>
                    <div>
                        <span className="font-semibold">Cloud Cover:</span>{' '}
                        {cloudCoverValue}%
                    </div>
                </>
            )}
        </div>
    )
}

const DropletSVG = ({
    fill,
    iconOnly = false,
}: {
    fill: any
    iconOnly?: boolean
}) => (
    <svg
        xmlns="http://www.w3.org/2000/svg"
        width={iconOnly ? '10' : '20'}
        height={iconOnly ? '10' : '20'}
        viewBox="0 0 43.025 43.023">
        <path
            d="M18.167,42.508a17.407,17.407,0,0,1-4.258-.522,17.725,17.725,0,0,1-.267-34.333L40.1.564A1.909,1.909,0,0,1,42.435,2.9l-7.09,26.454A17.822,17.822,0,0,1,18.167,42.508Z"
            transform="translate(0.011 0.015)"
            fill={fill}
            stroke="#035388"
            strokeMiterlimit="10"
            strokeWidth="1"
        />
    </svg>
)

export default CloudWidget
