import gql from 'graphql-tag'

export const ReadCrewMembers_LogBookEntrySections = gql`
    query readCrewMembers_LogBookEntrySections(
        $filter: CrewMembers_LogBookEntrySectionFilterFields = {}
    ) {
        readCrewMembers_LogBookEntrySections(filter: $filter) {
            nodes {
                id
                punchIn
                punchOut
                archived
                dutyHours
                workDetails
                crewMemberID
                crewMember {
                    id
                    firstName
                    surname
                    trainingSessionsDue {
                        nodes {
                            id
                            dueDate
                            memberID
                            member {
                                id
                                firstName
                                surname
                            }
                            vesselID
                            vessel {
                                id
                                title
                            }
                            trainingTypeID
                            trainingType {
                                id
                                title
                            }
                        }
                    }
                }
                dutyPerformedID
                dutyPerformed {
                    id
                    title
                    abbreviation
                }
                logBookEntryID
                logBookEntry {
                    id
                    startDate
                    vehicleID
                    vehicle {
                        id
                        title
                    }
                }
            }
        }
    }
`

export const ReadCrewWelfare_LogBookEntrySections = gql`
    query ReadCrewWelfare_LogBookEntrySections($id: [ID]!) {
        readCrewWelfare_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                fitness
                safetyActions
                waterQuality
                imSafe
            }
        }
    }
`

export const ReadFuel_LogBookEntrySections = gql`
    query ReadFuel_LogBookEntrySections($id: [ID]!) {
        readFuel_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                fuelTankStartStops {
                    nodes {
                        id
                        start
                        end
                        fuelType
                        comments
                        fuelTankID
                    }
                }
            }
        }
    }
`

export const ReadOneCustomisedLogBookConfig = gql`
    query ReadOneCustomisedLogBookConfig($id: ID!) {
        readOneCustomisedLogBookConfig(
            filter: { customisedLogBookID: { eq: $id } }
        ) {
            id
            title
            lastEdited
            customisedComponentCategories
            customisedLogBook {
                id
                title
            }
            customisedLogBookComponents {
                nodes {
                    id
                    title
                    active
                    sortOrder
                    subView
                    subViewTitle
                    subFields
                    customEntryType
                    componentClass
                    category
                    disclaimer {
                        id
                        disclaimerText
                    }
                    customisedComponentFields(
                        sort: {
                            sortOrder: ASC
                            customisedFieldTitle: ASC
                            fieldName: ASC
                        }
                        limit: 500
                    ) {
                        pageInfo {
                            totalCount
                        }
                        nodes {
                            id
                            fieldName
                            status
                            sortOrder
                            description
                            customisedFieldTitle
                            customisedFieldType
                            customisedEngineTypes
                        }
                    }
                }
            }
            policies {
                nodes {
                    id
                    fileFilename
                    name
                    title
                }
            }
        }
    }
`

export const ReadOneLogBookEntry = gql`
    query ReadOneLogBookEntry($logbookEntryId: ID!) {
        readOneLogBookEntry(filter: { id: { eq: $logbookEntryId } }) {
            id
            masterID
            state
            className
            startDate
            endDate
            fuelLevel
            logBookID
            createdByID
            signOffCommentID
            signOffSignatureID
            clientID
            lockedDate
            lastConfig
            fuelLog {
                nodes {
                    id
                    fuelAdded
                    fuelBefore
                    fuelAfter
                    date
                    costPerLitre
                    totalCost
                    fuelTank {
                        id
                        capacity
                        safeFuelCapacity
                        currentLevel
                        title
                    }
                }
            }
            engineStartStop {
                nodes {
                    id
                    hoursStart
                    engineID
                    engine {
                        id
                        title
                        currentHours
                    }
                }
            }
            logBook {
                id
                title
                componentConfig
            }
            master {
                id
                firstName
                surname
            }
            logBookEntrySections {
                nodes {
                    id
                    className
                }
            }
            vehicle {
                id
                seaLogsMembers {
                    nodes {
                        id
                        firstName
                        surname
                        archived
                        primaryDutyID
                    }
                }
            }
        }
    }
`

export const ReadSectionMemberComments = gql`
    query ReadSectionMemberComments(
        $limit: Int = 100
        $offset: Int = 0
        $filter: SectionMemberCommentFilterFields = {}
    ) {
        readSectionMemberComments(
            limit: $limit
            offset: $offset
            filter: $filter
        ) {
            nodes {
                id
                commentType
                fieldName
                comment
                logBookEntrySectionID
                hideComment
            }
        }
    }
`

// LogBookSignOff_LogBookEntrySection
export const ReadLogBookSignOff_LogBookEntrySections = gql`
    query ReadLogBookSignOff_LogBookEntrySections($id: [ID]!) {
        readLogBookSignOff_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                review
                safetyEquipmentCheck
                forecastAccuracy
                ais
                navigationLightsAndShapes
                electronicNavigationalAids
                mainEngines
                auxiliarySystems
                fuelAndOil
                bilgeSystems
                power
                batteryMaintenance
                circuitInspections
                mooringAndAnchoring
                cargoAndAccessEquipment
                hatchesAndWatertightDoors
                galleyAppliances
                wasteManagement
                ventilationAndAirConditioning
                emergencyReadiness
                environmentalCompliance
                fuelStart
                completedTime
                endLocationID
                endLocation {
                    id
                    lat
                    long
                    geoLocationID
                    geoLocation {
                        id
                        title
                        lat
                        long
                    }
                }
                sectionSignatureID
                sectionSignature {
                    id
                    signatureData
                }
            }
        }
    }
`

// Supernumerary_LogBookEntrySection
export const ReadSupernumerary_LogBookEntrySections = gql`
    query ReadSupernumerary_LogBookEntrySections($id: [ID]!) {
        readSupernumerary_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                firstName
                surname
            }
        }
    }
`

// TripReport_LogBookEntrySection
export const ReadTripReport_LogBookEntrySections = gql`
    query ReadTripReport_LogBookEntrySections($id: [ID]!) {
        readTripReport_LogBookEntrySections(
            filter: { id: { in: $id } }
            sort: { created: ASC }
        ) {
            nodes {
                id
                archived
                departTime
                totalVehiclesCarried
                arriveTime
                arrive
                pob
                comment
                fromLocationID
                fromLocation {
                    id
                    title
                }
                fromLat
                fromLong
                toLocationID
                toLocation {
                    id
                    title
                }
                toLat
                toLong
                dangerousGoodsChecklist {
                    id
                }
                sectionSignatureID
                sectionSignature {
                    id
                    signatureData
                }
                enableDGR
                designatedDangerousGoodsSailing
                masterID
                tripEvents(sort: { created: ASC }) {
                    nodes {
                        id
                        eventCategory
                        tripUpdate {
                            id
                            date
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        eventType_TaskingID
                        eventType_Tasking {
                            id
                            type
                            status
                            fuelLog {
                                nodes {
                                    id
                                    fuelTank {
                                        id
                                    }
                                    fuelAfter
                                }
                            }
                        }
                        eventType_RefuellingBunkeringID
                        eventType_RefuellingBunkering {
                            id
                            fuelLog {
                                nodes {
                                    id
                                    fuelTank {
                                        id
                                    }
                                    fuelAfter
                                    fuelAdded
                                    costPerLitre
                                }
                            }
                        }
                        eventType_PassengerDropFacilityID
                        eventType_PassengerDropFacility {
                            id
                            type
                            fuelLog {
                                nodes {
                                    id
                                    fuelTank {
                                        id
                                    }
                                    fuelAfter
                                }
                            }
                        }
                        infringementNotice {
                            id
                            time
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        incidentRecord {
                            id
                            title
                        }
                        crewTraining {
                            id
                            startTime
                            geoLocationID
                            geoLocation {
                                id
                                title
                            }
                        }
                        supernumerary {
                            id
                            title
                            totalGuest
                            briefingTime
                            isBriefed
                            guestList {
                                nodes {
                                    id
                                    firstName
                                    surname
                                    sectionSignature {
                                        id
                                        signatureData
                                    }
                                }
                            }
                        }
                        eventType_RestrictedVisibilityID
                        eventType_BarCrossingID
                        eventType_BarCrossing {
                            id
                        }
                    }
                }
                tripReport_Stops {
                    nodes {
                        id
                        lat
                        long
                        arriveTime
                        departTime
                        otherCargo
                        comments
                        stopLocationID
                        stopLocation {
                            id
                            title
                        }
                    }
                }
            }
        }
    }
`

// VesselDailyCheck_LogBookEntrySection
export const ReadVesselDailyCheck_LogBookEntrySections = gql`
    query ReadVesselDailyCheck_LogBookEntrySections($id: [ID]!) {
        readVesselDailyCheck_LogBookEntrySections(filter: { id: { in: $id } }) {
            nodes {
                id
                checkTime
                bilgeLevels
                bilgePumps
                hull
                navEquipment
                oilAndWater
                engineRoomChecks
                forwardAndReverseBelts
                driveShafts
                steeringTiller
                cablesFRPullies
                throttleAndCable
                wiring
                beltsHosesClamps
                sandTraps
                batteries
                safetyEquipment
                checksWithManual
                cabin
                preStartupChecks
                engineChecks
                otherNavigation
                navigationCharts
                engineCheckPropellers
                forwardReverse
                electricalVisualFields
                postElectricalStrainers
                engineOilWater
                engineMountsAndStabilisers
                postStartupEngineChecks
                preEngineAndPropulsion
                postEngineAndPropulsion
                postElectrical
                otherEngineFields
                preFuelLevelStart
                preFuelLevelEnd
                houseBatteriesStatus
                checkOilPressure
                batteryIsCharging
                shorePowerIsDisconnected
                lockToLockSteering
                trimTabs
                steeringTrimTabs
                oilWater
                electrical
                postStartupChecks
                navigationChecks
                depthSounder
                radar
                tracPlus
                chartPlotter
                sart
                aisOperational
                vhf
                uhf
                forwardAndReverse
                hull_HullStructure
                pontoonPressure
                bungsInPlace
                hull_DeckEquipment
                swimPlatformLadder
                biminiTopsCanvasCovers
                windscreenCheck
                nightLineDockLinesRelease
                floor
                engineMounts
                engineOil
                engineTellTale
                engineIsFit
                steeringFluid
                steeringRams
                steeringIsFit
                epirb
                lifeJackets
                fireExtinguisher
                unitTransomBolts
                cotterPins
                reverseBucketAndRam
                nozzleAndBearings
                tailHousing
                weatherSummary
                windDirection
                windStrength
                swell
                lifeRings
                flares
                fireHoses
                fireBuckets
                fireBlanket
                fireAxes
                firePump
                fireFlaps
                lifeRaft
                highWaterAlarm
                firstAid
                exterior
                interior
                cleanGalleyBench
                cleanGalleyFloor
                cleanTable
                cleanMirrorGlass
                cleanToilet
                cleanSink
                cleanDeckFloor
                cleanOutsideWallWindow
                cleanGarbageBin
                cleanBoothSeat
                cleanFridge
                cleanCupboard
                cleanOven
                cleanSouvenir
                cleanRestockSalesItem
                cleanTill
                charts
                documentCrewBriefings
                recordComments
                engineChecks
                steering
                cooling
                propulsion
                bilge
                engineRoom
                throttle
                jetUnit
                generator
                fuelLevel
                fuelTanks
                fuelFilters
                fuel
                hullStructure
                deckEquipment
                anchor
                hatches
                dayShapes
                hvac
                tv
                stabilizationSystems
                electronics
                gps
                radio
                navigationLights
                compass
                soundSignallingDevices
                navigationHazards
                wheelhouse
                bilgeCheck
                sewage
                freshWater
                sanitation
                pestControl
                mainEngine
                transmission
                steeringPropultion
                propulsionCheck
                stabilizers
                exhaust
                propulsionBatteriesStatus
                personOverboardRescueEquipment
                smokeDetectors
                shorePower
                electricalPanels
                seaStrainers
                sail
                mainEngineChecks
                propulsionEngineChecks
                propulsionPropulsion
                electricalChecks
                engineRoomVisualInspection
                fuelSystems
                steeringChecks
                throttleAndCableChecks
                tenderOperationalChecks
                airShutoffs
                fireDampeners
                coolantLevels
                fuelShutoffs
                separators
                steeringRudders
                steeringHoses
                steeringTillers
                steeringHydraulicSystems
                operationalTestsOfHelms
                driveShaftsChecks
                gearBox
                propellers
                skeg
                engrMechanical
                mechCrankcaseOilLevel
                mechCoolingWaterLevel
                mechTransmissionOilLevel
                mechInspectPipework
                mechHydraulicSteeringOilLevel
                mechGearBoxOilLevel
                mechInspectVeeBelts
                engrGenerator
                genCrankcaseOilLevel
                genCoolingWaterLevel
                genElectrical
                genPracxisSystemOperative
                genTest24VLighting
                genRunningTankFuelLevel
                engrElectronics
                electrDeckLights
                electrSearchLights
                electrChart
                engrTowlineWinch
                towCheckWinchCondition
                towProveWinchOperation
                towSelectControlStation
                towCheckTowlineCondition
                biosecGlueBoardTraps
                crewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                preCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                preCheckTime
                postCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                postCheckTime
                otherEngineCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                otherEngineCheckTime
                deckOpsCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                deckOpsCheckTime
                navigationCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                navigationCheckTime
                jetCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                jetCheckTime
                cleaningCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                cleaningCheckTime
                hvacCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                hvacCheckTime
                plumbingCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                plumbingCheckTime
                sailCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                sailCheckTime
                engrCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                engrCheckTime
                biosecCrewResponsible {
                    nodes {
                        id
                        firstName
                        surname
                    }
                }
                biosecCheckTime
            }
        }
    }
`
