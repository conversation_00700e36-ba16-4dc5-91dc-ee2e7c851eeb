import { useState } from 'react'
import { IConfigForm } from '../types'

export const useEditConfigDialog = () => {
    const [title, setTitle] = useState('')
    const [isFieldGroup, setIsFieldGroup] = useState(true)
    const [isOpen, setIsOpen] = useState(false)
    const [selectedField, setSelectedField] = useState<any>(null)
    const [form, setForm] = useState<IConfigForm>({
        sortOrder: 0,
        description: '',
        fieldName: '',
    })

    const openDialog = ({
        field,
        title,
        isFieldGroup,
    }: {
        field: any
        title: string
        isFieldGroup: boolean
    }) => {
        setSelectedField(field)

        setForm({
            fieldName: field?.customisedFieldTitle ?? '',
            sortOrder: field?.sortOrder ?? 0,
            description: field?.description ?? '',
        })

        setIsFieldGroup(isFieldGroup)
        setTitle(title)
        setIsOpen(true)
    }

    return {
        title,
        isFieldGroup,
        selectedField,
        form,
        isOpen,
        onOpenChange(open: boolean) {
            setIsOpen(open)
        },
        openDialog,
        closeDialog() {
            setIsOpen(false)
        },
    }
}
