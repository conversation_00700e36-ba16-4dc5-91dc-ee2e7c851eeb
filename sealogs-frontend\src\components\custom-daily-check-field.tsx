'use client'

import { useState } from 'react'
import SeaLogsButton from './ui/sea-logs-button'
import { Button, Input, Label } from './ui'
import { MessageSquare, MessageSquareText } from 'lucide-react'

const CustomDailyCheckField = (props: any) => {
    const [yes, setYes] = useState(props.defaultYesChecked)
    const [No, setNo] = useState(props.defaultNoChecked)

    function callyes() {
        props.handleYesChange()
        setYes(true)
        setNo(false)
    }
    function callNo() {
        props.handleNoChange()
        setNo(true)
        setYes(false)
    }
    const classes = {
        fieldWrapper: 'flex flex-row items-center my-2',
        inputWrapper: 'flex flex-row gap-2 justify-between',
        inputWrapperInner: 'flex flex-row gap-2 justify-between items-center',
        radio: 'flex flex-row items-center gap-1',
        radioInput: 'w-6 h-6 ',
        radioLabel: '',
        textarea: 'block p-2.5 w-full mt-4    rounded-lg border   ',
        label: `  uppercase  `,
    }
    return (
        <div
            className={`customDailyCheck my-4  ${props.displayField ? '' : 'hidden'}  w-full gap-2 justify-between`}>
            <div className="w-full flex flex-row gap-2 justify-start items-center ">
                <span className=" lg:">{props.displayLabel}</span>
                {props.displayDescription && (
                    <SeaLogsButton
                        icon="alert"
                        className="w-6 h-6 ml-2"
                        action={() => {
                            props.setDescriptionPanelContent(
                                props.displayDescription,
                            )
                            props.setOpenDescriptionPanel(true)
                            props.setDescriptionPanelHeading(props.displayLabel)
                        }}
                    />
                )}
            </div>
            <div className={`flex flex-row gap-4`}>
                <div
                    className={` ${props.locked ? 'pointer-events-none opacity-60' : ''}`}>
                    <div className="md:w-8 md:h-8 w-6 h-6">
                        {No ? (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-not-ok-check1.svg"
                                alt=""
                            />
                        ) : (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-empty-check.svg"
                                alt=""
                                onClick={() => callNo()}
                            />
                        )}
                        <Input
                            id={`${props.inputId}-no_radio`}
                            type="radio"
                            name={`${props.inputId}-radio`}
                            onChange={() => callNo()}
                            className={' ' + 'hidden'}
                            defaultChecked={props.defaultNoChecked}
                        />
                    </div>
                    <Label
                        htmlFor={`${props.inputId}-no_radio`}
                        className={'cursor-pointer'}>
                        No
                    </Label>
                </div>
                <div
                    className={` ${props.locked ? 'pointer-events-none opacity-60' : ''}`}>
                    <div className="md:w-8 md:h-8 w-6 h-6">
                        {yes ? (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-ok-check.svg"
                                alt=""
                            />
                        ) : (
                            <img
                                className=" ring-1  p-0.5 rounded-full"
                                src="/sealogs-empty-check.svg"
                                alt=""
                                onClick={() => callyes()}
                            />
                        )}
                    </div>
                    <input
                        id={`${props.inputId}-yes_radio`}
                        type="radio"
                        name={`${props.inputId}-radio`}
                        onChange={() => callyes()}
                        className={' ' + 'hidden'}
                        defaultChecked={props.defaultYesChecked}
                    />
                    <Label
                        htmlFor={`${props.inputId}-yes_radio`}
                        className={'cursor-pointer'}>
                        Yes
                    </Label>
                </div>
                <Button className="-mt-4" onClick={props.commentAction}>
                    {props.comment ? (
                        <MessageSquareText className="w-6 h-6" />
                    ) : (
                        <MessageSquare className="w-6 h-6  " />
                    )}
                </Button>
            </div>
        </div>
    )
}

export default CustomDailyCheckField
