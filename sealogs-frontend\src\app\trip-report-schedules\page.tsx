'use client'
import Loading from '../loading'
import { canUseTripSchedule, onlyAdminAccess } from '../helpers/userHelper'
import { useEffect, useState } from 'react'
import TripReportScheduleList from '../ui/schedules/trip-report-schedule-list'

const TripReportScheduleListPage = () => {
    const [useTripSchedule, setUseTripSchedule] = useState(true)
    useEffect(() => {
        onlyAdminAccess()
        setUseTripSchedule(canUseTripSchedule())
    }, [])
    return (
        <div>
            {useTripSchedule ? (
                <TripReportScheduleList />
            ) : (
                <Loading message="Trip schedules are not enabled. Please enable them in the settings to use this feature." />
            )}
        </div>
    )
}

export default TripReportScheduleListPage
