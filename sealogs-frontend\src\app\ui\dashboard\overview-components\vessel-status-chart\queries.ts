import gql from 'graphql-tag'

export const ReadVesselStatuss = gql`
    query readVesselStatuss(
        $filter: VesselStatusFilterFields = {}
        $offset: Int = 0
        $limit: Int = 100
    ) {
        readVesselStatuss(
            sort: { date: ASC }
            filter: $filter
            limit: $limit
            offset: $offset
        ) {
            nodes {
                id
                date
                status
                vesselID
                created
            }
        }
    }
`

export const ReadVessels = gql`
    query ReadVessels(
        $vesselFilter: VesselFilterFields = {}
        $statusFilter: VesselStatusFilterFields = {}
    ) {
        readVessels(filter: $vesselFilter) {
            nodes {
                id
                statusHistory(
                    filter: $statusFilter
                    sort: { date: DESC, created: DESC }
                    limit: 1
                ) {
                    nodes {
                        id
                        date
                        created
                        status
                    }
                }
            }
        }
    }
`
