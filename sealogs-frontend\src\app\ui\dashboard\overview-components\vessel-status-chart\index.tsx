'use client'

import {
    ChartConfig,
    ChartContainer,
    ChartTooltip,
    ChartTooltipContent,
} from '@/components/ui/chart'
import { Line, LineChart, <PERSON>Axis, YAxis } from 'recharts'
import * as dateFns from 'date-fns'
import { useCallback, useEffect, useMemo, useState } from 'react'
import { useLazyQuery } from '@apollo/client'
import {
    getLatestStatusFromHistory,
    getServiceSummary,
    IStatusHistory,
    IVesselServiceSummary,
    VesselStatus,
} from '../../../reporting/new-service-report/action'
import { calculateServiceSummary, populateMonths } from './action'
import { ReadVessels, ReadVesselStatuss } from './queries'
import { P } from '@/components/ui'

interface IChartData {
    title: string
    service_days_percent: number
    service_days: number
    total_days: number
}

const chartConfig = {
    service_days_percent: {
        label: 'Service Status',
        color: '#7B8795',
    },
} satisfies ChartConfig

interface IProps {
    startMonth: Date
    endMonth: Date
}

// Also the line is not actually the correct colour. The dots should have a fill of #E3F8FF and a border of #7B879A

export default function VesselStatusChart({ startMonth, endMonth }: IProps) {
    const [chartData, setChartData] = useState<IChartData[]>([])
    const [loadVesselStatus, { called, loading }] = useLazyQuery(
        ReadVesselStatuss,
        {
            fetchPolicy: 'cache-and-network',
            onError: (error: any) => {
                console.error('queryLogBookEntrySections error', error)
            },
        },
    )

    const [getVessselsWithLatestStatus] = useLazyQuery(ReadVessels, {
        fetchPolicy: 'cache-and-network',
        onError: (error: any) => {
            console.error('queryLogBookEntrySections error', error)
        },
    })

    const generateChartData = useCallback(async () => {
        const startDate = dateFns.format(
            dateFns.startOfMonth(startMonth),
            'yyyy-MM-dd',
        )
        const endDate = dateFns.format(
            dateFns.endOfMonth(endMonth),
            'yyyy-MM-dd',
        )

        const filter = {
            date: {
                gte: startDate,
                lte: endDate,
            },
        }

        const { data } = await loadVesselStatus({
            variables: { filter, limit: 0 },
        })
        const { data: latestStatusData } = await getVessselsWithLatestStatus({
            variables: {
                statusFilter: {
                    date: {
                        lte: startDate,
                    },
                },
                filter: { archived: { eq: false } },
            },
        })

        const vesselsWithLatestStatus =
            latestStatusData?.readVessels?.nodes ?? []

        const vesselStatus: IStatusHistory[] =
            data?.readVesselStatuss?.nodes ?? []

        const months = populateMonths(startMonth, endMonth)

        let vesselLastStatus: {
            [vesselID: number]: VesselStatus
        } = {}

        vesselsWithLatestStatus.forEach((element: any) => {
            vesselLastStatus[element.id] = getLatestStatusFromHistory(
                element.statusHistory?.nodes ?? [],
            )
        })

        const items = months.map((monthStr) => {
            const statusHistoryByMonth: IStatusHistory[] = vesselStatus.filter(
                (val) =>
                    dateFns.format(val.date, 'yyyy-MM') ===
                    dateFns.format(monthStr, 'yyyy-MM'),
            )

            //Create Service Summary Group by Vessel
            const vesselIDs: number[] = vesselsWithLatestStatus.map(
                (val: any) => val.id as number,
            )

            let vesselServiceSummary: {
                [vesselID: number]: IVesselServiceSummary
            } = {}

            vesselIDs.forEach((vesselID) => {
                const vesselServiceHistories = statusHistoryByMonth.filter(
                    (val) => val.vesselID === vesselID,
                )

                const startingStatus = vesselLastStatus[vesselID]

                const startOfMonth = dateFns.startOfMonth(monthStr)
                const endOfMonth = dateFns.endOfMonth(monthStr)

                vesselServiceSummary[vesselID] = getServiceSummary(
                    vesselServiceHistories,
                    startingStatus,
                    startOfMonth,
                    endOfMonth,
                )

                if (vesselServiceHistories.length > 0) {
                    vesselLastStatus[vesselID] = getLatestStatusFromHistory(
                        vesselServiceHistories,
                    )
                }
            })

            let serviceDaysPercentage = 100
            let serviceDays = 0
            let totalDays = 0

            if (Object.keys(vesselServiceSummary).length > 0) {
                const serviceSummary =
                    calculateServiceSummary(vesselServiceSummary)

                serviceDays =
                    serviceSummary.onVoyage + serviceSummary.availableForVoyage

                totalDays =
                    serviceSummary.onVoyage +
                    serviceSummary.availableForVoyage +
                    serviceSummary.outOfService

                serviceDaysPercentage =
                    totalDays > 0 ? (serviceDays / totalDays) * 100 : 100
            }

            return {
                title: dateFns.format(monthStr, 'MMM yy'),
                service_days_percent: parseFloat(
                    serviceDaysPercentage.toFixed(2),
                ),
                service_days: serviceDays,
                total_days: totalDays,
            }
        })

        setChartData(items)
    }, [startMonth, endMonth])

    useEffect(() => {
        generateChartData()
    }, [generateChartData])

    return (
        <div className="flex flex-col h-80">
            <div>
                <P>Vessels in service</P>
                <InServiceSummary data={chartData} />
            </div>
            <div className="p-2 h-40">
                <ChartContainer config={chartConfig}>
                    <LineChart
                        accessibilityLayer
                        data={chartData}
                        margin={{
                            top: 20,
                            left: 12,
                            right: 12,
                        }}>
                        <ChartTooltip
                            cursor={false}
                            content={
                                <ChartTooltipContent
                                    hideLabel
                                    hideIndicator
                                    className="chartToolTip"
                                    formatter={(value, name, item, index) => {
                                        const chartDataPayload = item.payload

                                        return (
                                            <div className="max-w-sm">
                                                <div>
                                                    {chartDataPayload.title}
                                                </div>
                                                <div className="font-normal text-xs">
                                                    Your vessels were in service
                                                    <br />
                                                    for{' '}
                                                    <b className="font-semibold">
                                                        {value}%
                                                    </b>{' '}
                                                    of the month
                                                </div>
                                            </div>
                                        )
                                    }}
                                />
                            }
                        />
                        <Line
                            dataKey="service_days_percent"
                            type="linear"
                            stroke="var(--color-service_days_percent)"
                            strokeWidth={1.5}
                            dot={{
                                stroke: 'var(--color-service_days_percent)',
                                strokeWidth: 1.5,
                                fill: '#BEDEF9',
                                r: 5,
                            }}
                            activeDot={{
                                r: 6,
                            }}
                        />
                    </LineChart>
                </ChartContainer>
            </div>
        </div>
    )
}

const InServiceSummary = ({ data }: { data: IChartData[] }) => {
    const percentage = useMemo(() => {
        const length = data.length
        const current = data[length - 1]
        return current?.service_days_percent ?? 100
    }, [data])

    const diffFromPrevious = useMemo(() => {
        const length = data.length

        const current = data[length - 1]
        const previous = data[length - 2]

        const currentPercentage = current?.service_days_percent ?? 100
        const previousPercentage = previous?.service_days_percent ?? 100

        return parseFloat((currentPercentage - previousPercentage).toFixed(2))
    }, [data])

    return (
        <>
            <p className="text-5xl font-black mb-1">{percentage}%</p>
            {diffFromPrevious !== 0 && (
                <p className="text-curious-blue-400 text-sm">
                    {diffFromPrevious > 0 && '+'}
                    {diffFromPrevious}% from last month
                </p>
            )}
            {diffFromPrevious === 0 && (
                <p className="text-curious-blue-400 text-xs">
                    No changes from last month
                </p>
            )}
        </>
    )
}
