'use client'

import { useEffect, useState } from 'react'
import { usePathname } from 'next/navigation'
import { isEmpty } from 'lodash'
import { Button } from '@/components/ui/button'
import {
    Dialog,
    DialogContent,
    DialogHeader,
    DialogTitle,
    DialogFooter,
} from '@/components/ui/dialog'
import { Expand } from 'lucide-react'
import LocationMap from '@/components/location-map'

// TypeScript interface for FullMap props
interface FullMapProps {
    vessels: any[]
    data?: any
    enableClickToSetPosition?: boolean
    scrollWheelZoom?: boolean
    resizeDebounceMs?: number
    className?: string
}

export default function FullMap({
    vessels,
    data,
    enableClickToSetPosition = false,
    scrollWheelZoom = false,
    resizeDebounceMs = 150,
    className = '',
}: FullMapProps) {
    const pathname = usePathname()
    const [clientTitle, setClientTitle] = useState('')
    const [departmentTitle, setDepartmentTitle] = useState('')
    const [vesselsList, setVesselsList] = useState<any>(vessels)
    const [expandMap, setExpandMap] = useState(false)
    useEffect(() => {
        setClientTitle(localStorage.getItem('clientTitle') || '')
        const departmentTitle = localStorage.getItem('departmentTitle')
        setDepartmentTitle(
            departmentTitle === 'null' ? '' : departmentTitle || '',
        )
        setVesselsList(vessels)
    }, [vessels])
    // Get the center position from the first vessel with valid coordinates
    const getCenterPosition = (): [number, number] => {
        if (vesselsList.length === 0) return [0, 0]

        const firstVessel = vesselsList.find(
            (vessel: any) =>
                vessel.vesselPosition?.lat && vessel.vesselPosition?.long,
        )

        if (firstVessel) {
            return [
                firstVessel.vesselPosition.lat || 0,
                firstVessel.vesselPosition.long || 0,
            ]
        }

        return [0, 0]
    }

    const centerPosition = getCenterPosition()

    return (
        <div
            className={`rounded-lg relative overflow-hidden flex flex-col h-full ${className}`}>
            {!expandMap && (
                <LocationMap
                    position={centerPosition}
                    vessels={vesselsList}
                    zoom={12}
                    scrollWheelZoom={scrollWheelZoom}
                    enableClickToSetPosition={enableClickToSetPosition}
                    resizeDebounceMs={resizeDebounceMs}
                    className="h-full z-0"
                />
            )}
            <Button
                variant="ghost"
                iconOnly
                className="absolute right-4 top-4"
                iconLeft={<Expand size={20} className='bg-background' />}
                onClick={() => setExpandMap(true)}
            />
            <Dialog open={expandMap} onOpenChange={setExpandMap}>
                <DialogContent className="max-w-[90vw] w-full p-0">
                    <DialogHeader className="p-6 pb-0">
                        <DialogTitle>
                            {`${clientTitle}${!isEmpty(departmentTitle) ? ' - ' + departmentTitle : ''} - Map View`}
                        </DialogTitle>
                    </DialogHeader>
                    <div className="h-[85vh] w-full">
                        <LocationMap
                            position={centerPosition}
                            vessels={vesselsList}
                            zoom={12}
                            scrollWheelZoom={scrollWheelZoom}
                            enableClickToSetPosition={enableClickToSetPosition}
                            resizeDebounceMs={resizeDebounceMs}
                            className="h-full z-0"
                        />
                    </div>
                    <DialogFooter className="pt-0 p-2.5">
                        <Button
                            variant="outline"
                            onClick={() => setExpandMap(false)}>
                            Close
                        </Button>
                    </DialogFooter>
                </DialogContent>
            </Dialog>
        </div>
    )
}
